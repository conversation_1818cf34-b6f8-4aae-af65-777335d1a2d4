# 车辆管理系统 - 表格排序功能

## 功能概述

车辆管理系统现已支持完整的表格排序功能，用户可以按照多个字段对车辆数据进行升序或降序排序。

## 支持的排序字段

| 字段 | 数据库字段名 | 显示名称 | 支持排序 |
|------|-------------|----------|----------|
| ID | `auto_id` | ID | ✅ |
| 车牌号 | `license_plate` | 车牌号 | ✅ |
| 型号 | `model` | 型号 | ✅ |
| 品牌 | `brand` | 品牌 | ✅ |
| 颜色 | `color` | 颜色 | ✅ |
| 年份 | `year` | 年份 | ✅ |
| 状态更新时间 | `status_updated_at` | 状态更新时间 | ✅ |

## 使用方法

### 1. 表格列头排序

- 点击表格列头即可进行排序
- 首次点击为升序排序
- 再次点击为降序排序
- 第三次点击取消排序

### 2. 排序指示器

在筛选条件栏中，有一个绿色的排序指示器显示当前排序状态：

```
排序: ID ⬇️
```

- 显示当前排序字段的中文名称
- 箭头指示排序方向（⬆️ 升序，⬇️ 降序）
- 点击"切换排序"按钮可快速切换排序方向

### 3. 重置功能

- 点击"全部"按钮会重置所有筛选条件和排序
- 重置后默认按ID降序排序

## API参数

### 请求参数

```javascript
{
  page: 1,              // 页码
  size: 20,             // 每页数量
  order_by: 'auto_id',  // 排序字段
  order_direction: 'desc' // 排序方向 (asc/desc)
}
```

### API地址

```
GET http://************:15679/drive_ops/api/v1/vehicles
```

### 完整示例

```javascript
// 按车牌号升序排序
GET http://************:15679/drive_ops/api/v1/vehicles?page=1&size=20&order_by=license_plate&order_direction=asc

// 按年份降序排序
GET http://************:15679/drive_ops/api/v1/vehicles?page=1&size=20&order_by=year&order_direction=desc
```

## 技术实现

### 前端实现

1. **表格配置**
   ```vue
   <el-table @sort-change="handleSortChange">
     <el-table-column 
       prop="auto_id" 
       sortable="custom" 
       label="ID"
     />
   </el-table>
   ```

2. **排序事件处理**
   ```javascript
   handleSortChange(sortInfo) {
     if (sortInfo.prop) {
       this.listQuery.order_by = sortInfo.prop
       this.listQuery.order_direction = sortInfo.order === 'ascending' ? 'asc' : 'desc'
     }
     this.getList()
   }
   ```

3. **字段映射**
   ```javascript
   const fieldMapping = {
     'auto_id': 'auto_id',
     'license_plate': 'license_plate',
     'model': 'model',
     'brand': 'brand',
     'color': 'color',
     'year': 'year',
     'status_updated_at': 'status_updated_at'
   }
   ```

### 后端要求

后端API需要支持以下参数：

- `order_by`: 排序字段名
- `order_direction`: 排序方向（asc/desc）

示例后端实现（伪代码）：
```python
@app.route('/api/v1/vehicles')
def get_vehicles():
    page = request.args.get('page', 1, type=int)
    size = request.args.get('size', 20, type=int)
    order_by = request.args.get('order_by', 'auto_id')
    order_direction = request.args.get('order_direction', 'desc')
    
    query = Vehicle.query
    
    # 应用排序
    if order_direction == 'asc':
        query = query.order_by(asc(getattr(Vehicle, order_by)))
    else:
        query = query.order_by(desc(getattr(Vehicle, order_by)))
    
    # 分页
    pagination = query.paginate(page=page, per_page=size)
    
    return {
        'items': [item.to_dict() for item in pagination.items],
        'total': pagination.total,
        'page': page,
        'size': size
    }
```

## 调试功能

### 调试按钮

页面上有一个"调试查询"按钮，点击后会显示：
- 当前查询参数
- 排序字段和方向
- 清理后的参数

### API测试页面

创建了专门的API测试页面 (`src/views/vehicle-management/api-test.vue`)，可以：
- 测试不同的排序参数
- 查看API响应结果
- 检查响应时间
- 预览数据内容

## 注意事项

1. **默认排序**: 系统默认按ID降序排序
2. **性能考虑**: 对于大数据集，建议在数据库字段上建立索引
3. **错误处理**: 如果排序字段不存在，系统会回退到默认排序
4. **数据一致性**: 排序和筛选可以同时使用

## 扩展功能

### 添加新的排序字段

1. 在表格列定义中添加 `sortable="custom"` 属性
2. 在 `fieldMapping` 中添加字段映射
3. 在 `getSortDisplayName` 中添加显示名称
4. 确保后端API支持该字段排序

### 多字段排序

如需支持多字段排序，可以扩展API参数：
```javascript
{
  order_by: ['auto_id', 'license_plate'],
  order_direction: ['desc', 'asc']
}
```

## 故障排除

### 常见问题

1. **排序不生效**
   - 检查后端API是否正确处理order_by和order_direction参数
   - 确认字段名映射是否正确

2. **排序方向错误**
   - 检查前端的排序方向转换逻辑
   - 确认后端的asc/desc处理

3. **性能问题**
   - 检查数据库索引
   - 考虑使用缓存机制
   - 优化查询语句

### 调试步骤

1. 打开浏览器开发者工具
2. 点击"调试查询"按钮查看参数
3. 检查网络请求和响应
4. 使用API测试页面验证功能
5. 查看控制台日志

## 更新日志

- **2024-01-XX**: 初始版本，支持基本排序功能
- **2024-01-XX**: 添加排序指示器和切换按钮
- **2024-01-XX**: 增加API测试页面
- **2024-01-XX**: 完善调试功能

## 参考文档

- [Element UI Table组件文档](https://element.eleme.cn/#/zh-CN/component/table)
- [Vue.js官方文档](https://vuejs.org/)
- API文档: `/src/api/vehicle.js` 