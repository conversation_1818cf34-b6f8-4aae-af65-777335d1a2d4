# 构建阶段
FROM node:14 AS builder

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 设置 git 使用 https 协议克隆
RUN git config --global url."https://".insteadOf git://

WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build:prod

# 生产环境
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY --from=builder /app/vue.config.js /etc/nginx/conf.d/

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]