import Vue from 'vue'

import Cookies from 'js-cookie'
// import '../src/assets/css'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'
import enLang from 'element-ui/lib/locale/lang/en'// 如果使用中文语言包请默认支持，无需额外引入，请删除该依赖

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters

import { staticsClient, apolloClient1, apolloClient2, goodsClient, infoClient, wlClient, companyClient } from './apolloClient'
import VueApollo from 'vue-apollo'

// 国际化支持
import i18n from './lang'

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

// 创建 Apollo Provider
const apolloProvider = new VueApollo({
  clients: {
    client1: apolloClient1,
    client2: apolloClient2,
    goodsClient: goodsClient,
    infoClient: infoClient,
    wlClient: wlClient,
    companyClient: companyClient,
    staticsClient: staticsClient
  },
  defaultClient: apolloClient1
})

Vue.use(VueApollo)

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  apolloProvider,
  render: h => h(App)
}).$mount('#app')
