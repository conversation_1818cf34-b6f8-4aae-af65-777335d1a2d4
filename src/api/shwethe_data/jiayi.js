import request from '@/utils/request'

/**
 * 获取所有 jiayi 数据
 * @returns {Promise} API 响应结果
 */
export function getJiayiList() {
  return request({
    url: '/api/jiayi',
    method: 'get'
  }, 'shwethe_data')
}

/**
 * 搜索 jiayi 数据
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {number} params.page - 页码，默认为1
 * @param {number} params.page_size - 每页数量，默认为10
 * @returns {Promise} API 响应结果
 */
export function searchJiayi(params = {}) {
  return request({
    url: '/api/jiayi/search',
    method: 'get',
    params: {
      keyword: params.keyword || '',
      page: params.page || 1,
      page_size: params.page_size || 10
    }
  }, 'shwethe_data')
}

/**
 * 根据 ID 获取 jiayi 详情
 * @param {string|number} id - jiayi ID
 * @returns {Promise} API 响应结果
 */
export function getJiayiById(id) {
  return request({
    url: `/api/jiayi/${id}`,
    method: 'get'
  }, 'shwethe_data')
}

// 默认导出
export default {
  getJiayiList,
  searchJiayi,
  getJiayiById
}
