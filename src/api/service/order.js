/**
 * 订单服务 API
 */
import { getServiceRequest } from './index'

// 获取订单服务请求实例
const orderService = getServiceRequest('order')

/**
 * 订单API
 */
const orderApi = {
  /**
   * 获取订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 订单列表
   */
  getOrders(params) {
    return orderService.get('/orders', { params })
  },
  
  /**
   * 获取订单详情
   * @param {number|string} id - 订单ID
   * @returns {Promise<Object>} 订单详情
   */
  getOrder(id) {
    return orderService.get(`/orders/${id}`)
  },
  
  /**
   * 创建订单
   * @param {Object} data - 订单数据
   * @returns {Promise<Object>} 创建的订单
   */
  createOrder(data) {
    return orderService.post('/orders', data)
  },
  
  /**
   * 更新订单
   * @param {number|string} id - 订单ID
   * @param {Object} data - 更新的订单数据
   * @returns {Promise<Object>} 更新后的订单
   */
  updateOrder(id, data) {
    return orderService.put(`/orders/${id}`, data)
  },
  
  /**
   * 取消订单
   * @param {number|string} id - 订单ID
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 操作结果
   */
  cancelOrder(id, reason) {
    return orderService.post(`/orders/${id}/cancel`, { reason })
  },
  
  /**
   * 获取用户订单
   * @param {number|string} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 订单列表
   */
  getUserOrders(userId, params) {
    return orderService.get(`/users/${userId}/orders`, { params })
  },
  
  /**
   * 更新订单状态
   * @param {number|string} id - 订单ID
   * @param {string} status - 订单状态
   * @param {Object} data - 附加数据
   * @returns {Promise<Object>} 更新后的订单
   */
  updateOrderStatus(id, status, data = {}) {
    return orderService.patch(`/orders/${id}/status`, { status, ...data })
  },
  
  /**
   * 订单支付通知处理
   * @param {number|string} id - 订单ID
   * @param {Object} paymentData - 支付数据
   * @returns {Promise<Object>} 处理结果
   */
  processPaymentNotification(id, paymentData) {
    return orderService.post(`/orders/${id}/payment-notification`, paymentData)
  },
  
  /**
   * 获取订单统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 统计数据
   */
  getOrderStatistics(params) {
    return orderService.get('/orders/statistics', { params })
  },
  
  /**
   * 导出订单数据
   * @param {Object} params - 导出参数
   * @returns {Promise<Blob>} 导出文件
   */
  exportOrders(params) {
    return orderService.get('/orders/export', { 
      params,
      responseType: 'blob'
    })
  }
}

export default orderApi