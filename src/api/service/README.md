# 服务 API 模块

这个目录包含了按服务分类的API接口定义，每个服务有自己独立的基础URL和API路由结构，适合微服务架构。

## 目录结构

```
service/
├── index.js           # 服务API基础设施（创建服务请求实例）
├── auth.js            # 认证服务API
├── user.js            # 用户服务API
├── product.js         # 产品服务API
├── order.js           # 订单服务API
└── payment.js         # 支付服务API
```

## 微服务架构

系统采用微服务架构，每个服务职责单一且独立：

- **认证服务** (`auth`): 处理用户认证、Token签发和验证
- **用户服务** (`user`): 处理用户管理、权限控制
- **产品服务** (`product`): 处理产品和分类管理
- **订单服务** (`order`): 处理订单创建和管理
- **支付服务** (`payment`): 处理支付、退款和金融交易

每个服务有自己独立的基础URL，可以独立部署和扩展。

## 使用方法

### 服务请求对象

`index.js` 提供了获取服务请求实例的方法：

```js
import { getServiceRequest } from '@/api/service'

// 获取产品服务的请求实例
const productService = getServiceRequest('product')

// 使用实例发起请求
productService.get('/products').then(data => {
  console.log(data)
})
```

### 使用特定服务API

每个服务都有专用的API模块，提供该服务所有功能的方法：

```js
// 导入产品服务API
import productApi from '@/api/service/product'

// 获取产品列表
productApi.getProducts({ category: 'electronics' }).then(products => {
  console.log(products)
})

// 获取单个产品
productApi.getProduct(123).then(product => {
  console.log(product)
})
```

### 跨服务交互

服务之间可以协同工作完成复杂业务流程：

```js
import authApi from '@/api/service/auth'
import userApi from '@/api/service/user'
import productApi from '@/api/service/product'
import orderApi from '@/api/service/order'
import paymentApi from '@/api/service/payment'

// 完整的下单流程示例
async function placeOrder(productId, quantity, userData) {
  // 1. 用户认证
  const authResult = await authApi.login(userData)
  
  // 2. 获取用户配送地址
  const addresses = await userApi.getUserAddresses(authResult.userId)
  const defaultAddress = addresses.find(addr => addr.isDefault)
  
  // 3. 查询产品信息
  const product = await productApi.getProduct(productId)
  
  // 4. 创建订单
  const order = await orderApi.createOrder({
    userId: authResult.userId,
    products: [{ id: productId, quantity }],
    address: defaultAddress.id,
    totalAmount: product.price * quantity
  })
  
  // 5. 创建支付
  const payment = await paymentApi.createPayment({
    orderId: order.id,
    amount: order.totalAmount,
    currency: 'CNY',
    method: 'wechat'
  })
  
  return { order, payment }
}
```

## 服务配置

服务配置位于 `index.js` 中的 `SERVICES` 对象，定义了每个服务的URL和超时设置：

```js
// 服务配置示例
export const SERVICES = {
  auth: {
    baseURL: 'http://api.example.com/auth-service',
    timeout: 5000
  },
  user: {
    baseURL: 'http://api.example.com/user-service',
    timeout: 5000
  },
  // ...其他服务
}
```

您可以根据实际部署情况修改这些配置。 