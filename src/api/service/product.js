/**
 * 产品服务 API
 */
import { getServiceRequest } from './index'

// 获取产品服务请求实例
const productService = getServiceRequest('product')

/**
 * 产品API
 */
const productApi = {
  /**
   * 获取产品列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 产品列表
   */
  getProducts(params) {
    return productService.get('/products', { params })
  },
  
  /**
   * 获取产品详情
   * @param {number|string} id - 产品ID
   * @returns {Promise<Object>} 产品详情
   */
  getProduct(id) {
    return productService.get(`/products/${id}`)
  },
  
  /**
   * 创建产品
   * @param {Object} data - 产品数据
   * @returns {Promise<Object>} 创建的产品
   */
  createProduct(data) {
    return productService.post('/products', data)
  },
  
  /**
   * 更新产品
   * @param {number|string} id - 产品ID
   * @param {Object} data - 更新的产品数据
   * @returns {Promise<Object>} 更新后的产品
   */
  updateProduct(id, data) {
    return productService.put(`/products/${id}`, data)
  },
  
  /**
   * 删除产品
   * @param {number|string} id - 产品ID
   * @returns {Promise<Object>} 操作结果
   */
  deleteProduct(id) {
    return productService.delete(`/products/${id}`)
  },
  
  /**
   * 获取产品分类
   * @returns {Promise<Array>} 产品分类列表
   */
  getCategories() {
    return productService.get('/categories')
  },
  
  /**
   * 根据分类获取产品
   * @param {string} categoryId - 分类ID
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 产品列表
   */
  getProductsByCategory(categoryId, params) {
    return productService.get(`/categories/${categoryId}/products`, { params })
  },
  
  /**
   * 搜索产品
   * @param {string} keyword - 搜索关键词
   * @param {Object} params - 其他搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  searchProducts(keyword, params = {}) {
    return productService.get('/products/search', { 
      params: { keyword, ...params }
    })
  },
  
  /**
   * 更新产品库存
   * @param {number|string} id - 产品ID
   * @param {number} quantity - 变更数量(正数增加,负数减少)
   * @returns {Promise<Object>} 更新后的库存
   */
  updateStock(id, quantity) {
    return productService.patch(`/products/${id}/stock`, { quantity })
  },
  
  /**
   * 获取热门产品
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 热门产品列表
   */
  getHotProducts(params) {
    return productService.get('/products/hot', { params })
  },
  
  /**
   * 获取产品统计信息
   * @returns {Promise<Object>} 产品统计信息
   */
  getProductStats() {
    return productService.get('/products/stats')
  }
}

export default productApi 