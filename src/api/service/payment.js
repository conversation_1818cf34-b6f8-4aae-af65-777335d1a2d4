/**
 * 支付服务 API
 */
import { getServiceRequest } from './index'

// 获取支付服务请求实例
const paymentService = getServiceRequest('payment')

/**
 * 支付API
 */
const paymentApi = {
  /**
   * 创建支付交易
   * @param {Object} data - 支付数据
   * @returns {Promise<Object>} 支付交易信息
   */
  createPayment(data) {
    return paymentService.post('/payments', data)
  },
  
  /**
   * 获取支付详情
   * @param {string} paymentId - 支付ID
   * @returns {Promise<Object>} 支付详情
   */
  getPayment(paymentId) {
    return paymentService.get(`/payments/${paymentId}`)
  },
  
  /**
   * 处理支付回调
   * @param {string} gateway - 支付网关
   * @param {Object} data - 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  handleCallback(gateway, data) {
    return paymentService.post(`/payments/callback/${gateway}`, data)
  },
  
  /**
   * 获取支付方式列表
   * @returns {Promise<Array>} 支付方式列表
   */
  getPaymentMethods() {
    return paymentService.get('/payment-methods')
  },
  
  /**
   * 获取特定区域可用的支付方式
   * @param {string} region - 区域代码
   * @returns {Promise<Array>} 支付方式列表
   */
  getRegionPaymentMethods(region) {
    return paymentService.get(`/payment-methods/region/${region}`)
  },
  
  /**
   * 申请退款
   * @param {string} paymentId - 支付ID
   * @param {Object} data - 退款数据
   * @returns {Promise<Object>} 退款信息
   */
  refund(paymentId, data) {
    return paymentService.post(`/payments/${paymentId}/refund`, data)
  },
  
  /**
   * 获取退款信息
   * @param {string} refundId - 退款ID
   * @returns {Promise<Object>} 退款详情
   */
  getRefund(refundId) {
    return paymentService.get(`/refunds/${refundId}`)
  },
  
  /**
   * 获取支付交易列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 交易列表
   */
  getTransactions(params) {
    return paymentService.get('/transactions', { params })
  },
  
  /**
   * 获取支付统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 统计数据
   */
  getPaymentStatistics(params) {
    return paymentService.get('/statistics', { params })
  },
  
  /**
   * 检查支付状态
   * @param {string} paymentId - 支付ID
   * @returns {Promise<Object>} 支付状态
   */
  checkPaymentStatus(paymentId) {
    return paymentService.get(`/payments/${paymentId}/status`)
  }
}

export default paymentApi 