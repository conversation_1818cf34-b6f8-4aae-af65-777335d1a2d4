/**
 * 认证服务 API
 */
import { getServiceRequest } from './index'

// 获取认证服务请求实例
const authService = getServiceRequest('auth')

/**
 * 认证API
 */
const authApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录数据
   * @returns {Promise<Object>} 登录结果
   */
  login(data) {
    return authService.post('/login', data)
  },
  
  /**
   * 用户注册
   * @param {Object} data - 注册数据
   * @returns {Promise<Object>} 注册结果
   */
  register(data) {
    return authService.post('/register', data)
  },
  
  /**
   * 退出登录
   * @returns {Promise<Object>} 操作结果
   */
  logout() {
    return authService.post('/logout')
  },
  
  /**
   * 刷新token
   * @param {string} refreshToken - 刷新token
   * @returns {Promise<Object>} 新的token
   */
  refreshToken(refreshToken) {
    return authService.post('/refresh-token', { refreshToken })
  },
  
  /**
   * 重置密码请求
   * @param {string} email - 用户邮箱
   * @returns {Promise<Object>} 操作结果
   */
  requestPasswordReset(email) {
    return authService.post('/password/reset-request', { email })
  },
  
  /**
   * 执行密码重置
   * @param {Object} data - 重置数据
   * @returns {Promise<Object>} 操作结果
   */
  resetPassword(data) {
    return authService.post('/password/reset', data)
  },
  
  /**
   * 修改密码
   * @param {Object} data - 密码数据
   * @returns {Promise<Object>} 操作结果
   */
  changePassword(data) {
    return authService.post('/password/change', data)
  },
  
  /**
   * 发送验证码
   * @param {string} type - 验证码类型(sms/email)
   * @param {string} contact - 联系方式(手机号/邮箱)
   * @returns {Promise<Object>} 操作结果
   */
  sendVerificationCode(type, contact) {
    return authService.post('/verification-code/send', { type, contact })
  },
  
  /**
   * 验证邮箱
   * @param {string} token - 验证token
   * @returns {Promise<Object>} 验证结果
   */
  verifyEmail(token) {
    return authService.post('/email/verify', { token })
  },
  
  /**
   * 验证短信验证码
   * @param {string} phone - 手机号
   * @param {string} code - 验证码
   * @returns {Promise<Object>} 验证结果
   */
  verifySmsCode(phone, code) {
    return authService.post('/sms/verify', { phone, code })
  },
  
  /**
   * 第三方登录
   * @param {string} provider - 第三方提供商
   * @param {Object} data - 验证数据
   * @returns {Promise<Object>} 登录结果
   */
  socialLogin(provider, data) {
    return authService.post(`/social/${provider}`, data)
  },
  
  /**
   * 获取当前用户信息
   * @returns {Promise<Object>} 用户信息
   */
  getCurrentUser() {
    return authService.get('/user')
  }
}

export default authApi 