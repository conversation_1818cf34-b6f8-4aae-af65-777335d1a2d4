/**
 * 服务API封装
 * 按服务类型组织API调用，每个服务有自己的基础URL和API结构
 */

import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 服务配置定义
export const SERVICES = {
  // 用户认证服务
  auth: {
    baseURL: 'http://api.example.com/auth-service',
    timeout: 5000
  },
  // 用户管理服务
  user: {
    baseURL: 'http://api.example.com/user-service',
    timeout: 5000
  },
  // 产品服务
  product: {
    baseURL: 'http://api.example.com/product-service',
    timeout: 10000
  },
  // 订单服务
  order: {
    baseURL: 'http://api.example.com/order-service',
    timeout: 8000
  },
  // 支付服务
  payment: {
    baseURL: 'http://api.example.com/payment-service',
    timeout: 15000
  }
}

/**
 * 创建服务请求实例
 * @param {string} serviceName - 服务名称
 * @returns {object} Axios请求实例
 */
export const createServiceRequest = (serviceName) => {
  const serviceConfig = SERVICES[serviceName]
  
  if (!serviceConfig) {
    throw new Error(`服务 "${serviceName}" 未定义`)
  }
  
  // 创建Axios实例
  const instance = axios.create({
    baseURL: serviceConfig.baseURL,
    timeout: serviceConfig.timeout,
    withCredentials: false
  })
  
  // 请求拦截器
  instance.interceptors.request.use(
    config => {
      if (store.getters.token) {
        config.headers['Authorization'] = `Bearer ${getToken()}`
      }
      return config
    },
    error => {
      console.error('请求错误:', error)
      return Promise.reject(error)
    }
  )
  
  // 响应拦截器
  instance.interceptors.response.use(
    response => {
      const res = response.data
      
      // 服务成功响应
      if (res.code === 20000 || res.code === 200) {
        return res.data
      }
      
      // 错误处理
      Message({
        message: res.message || '服务错误',
        type: 'error',
        duration: 5000
      })
      
      // 特殊状态码处理
      if (res.code === 50008 || res.code === 50012 || res.code === 50014 || res.code === 401) {
        // 登出提示
        MessageBox.confirm('您已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      
      return Promise.reject(new Error(res.message || '服务错误'))
    },
    error => {
      console.error('响应错误:', error)
      
      let message = '连接服务失败'
      if (error.response) {
        switch (error.response.status) {
          case 401:
            message = '未授权，请重新登录'
            break
          case 403:
            message = '拒绝访问'
            break
          case 404:
            message = '请求的资源不存在'
            break
          case 500:
            message = '服务器内部错误'
            break
          default:
            message = `请求错误 (${error.response.status})`
        }
      } else if (error.request) {
        message = '服务未响应'
      } else {
        message = error.message
      }
      
      Message({
        message,
        type: 'error',
        duration: 5000
      })
      
      return Promise.reject(error)
    }
  )
  
  return instance
}

// 创建各个服务的请求实例
const serviceInstances = {}

Object.keys(SERVICES).forEach(serviceName => {
  serviceInstances[serviceName] = createServiceRequest(serviceName)
})

/**
 * 获取服务请求实例
 * @param {string} serviceName - 服务名称
 * @returns {object} 对应服务的请求实例
 */
export const getServiceRequest = (serviceName) => {
  if (!serviceInstances[serviceName]) {
    throw new Error(`服务 "${serviceName}" 未定义`)
  }
  return serviceInstances[serviceName]
}

export default {
  SERVICES,
  getServiceRequest
} 