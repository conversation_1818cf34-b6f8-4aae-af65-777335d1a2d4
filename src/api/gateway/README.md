# API网关模式

这个目录实现了通过API网关访问后端服务的模式，前端统一通过一个网关地址访问所有微服务。

## 目录结构

```
gateway/
├── index.js       # API网关基础设施（网关请求实例）
├── auth.js        # 认证服务API
├── user.js        # 用户服务API
├── product.js     # 产品服务API
├── order.js       # 订单服务API
└── payment.js     # 支付服务API
```

## API网关架构

在API网关架构中，前端不直接连接各个微服务，而是通过一个统一的网关进行请求：

```
                   ┌─────────────┐
                   │             │
                   │  前端应用    │
                   │             │
                   └──────┬──────┘
                          │
                          ▼
                   ┌─────────────┐
                   │             │
                   │  API网关     │
                   │             │
                   └──────┬──────┘
                          │
           ┌──────────────┼─────────────┐
           │              │             │
           ▼              ▼             ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │             │ │             │ │             │
    │  认证服务    │ │  用户服务    │ │  产品服务    │ ...
    │             │ │             │ │             │
    └─────────────┘ └─────────────┘ └─────────────┘
```

网关负责：
- 请求路由
- 服务发现
- 负载均衡
- 认证授权
- 请求限流
- 日志监控
- 缓存管理

## 使用方法

### 直接使用网关API服务

```js
import gateway, { API_SERVICES } from '@/api/gateway'

// 直接使用网关实例
gateway.get(`${API_SERVICES.PRODUCT}/products`).then(products => {
  console.log(products)
})

// 使用HTTP方法对应的服务API
gateway.post(`${API_SERVICES.ORDER}/orders`, orderData).then(order => {
  console.log(order)
})
```

### 使用服务模块

每个服务都有对应的简化模块，提供基础HTTP方法：

```js
import { product, order } from '@/api/gateway'

// 使用产品服务
product.get('/products').then(products => {
  console.log(products)
})

// 使用订单服务
order.post('/orders', orderData).then(order => {
  console.log(order)
})
```

### 使用封装的API模块

为了更好的代码可维护性，推荐使用封装好的API模块：

```js
import productApi from '@/api/gateway/product'
import orderApi from '@/api/gateway/order'

// 获取产品列表
productApi.getProducts({ category: 'electronics' }).then(products => {
  console.log(products)
})

// 创建订单
orderApi.createOrder({
  products: [{ id: 1, quantity: 2 }],
  address: '...'
}).then(order => {
  console.log(order)
})
```

## 跨服务业务流程

网关架构使跨服务调用变得简单：

```js
import authApi from '@/api/gateway/auth'
import userApi from '@/api/gateway/user'
import productApi from '@/api/gateway/product'
import orderApi from '@/api/gateway/order'

// 下单流程示例
async function placeOrder(productId, quantity) {
  // 1. 获取当前用户
  const user = await authApi.getCurrentUser()
  
  // 2. 获取用户地址
  const addresses = await userApi.getUserAddresses(user.id)
  const defaultAddress = addresses.find(addr => addr.isDefault)
  
  // 3. 获取产品信息
  const product = await productApi.getProduct(productId)
  
  // 4. 创建订单
  const order = await orderApi.createOrder({
    products: [{ id: productId, quantity }],
    addressId: defaultAddress.id,
    totalAmount: product.price * quantity
  })
  
  return order
}
```

## 配置

API网关的基础URL在 `index.js` 中配置：

```js
const API_GATEWAY_URL = 'http://api.example.com/gateway'
```

各个微服务的路径前缀在 `API_SERVICES` 常量中定义：

```js
export const API_SERVICES = {
  AUTH: '/auth-service',
  USER: '/user-service',
  // ...其他服务
}
```

根据实际部署情况修改这些配置。 