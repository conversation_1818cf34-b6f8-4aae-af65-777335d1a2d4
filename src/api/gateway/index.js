/**
 * API网关模式
 * 
 * 前端统一通过API网关访问后端服务，网关负责请求转发和服务发现
 * 对前端而言，统一使用一个基础URL，但API路径包含服务标识
 */

import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// API网关基础URL
const API_GATEWAY_URL = 'http://api.example.com/gateway'

// 创建API网关请求实例
const gateway = axios.create({
  baseURL: API_GATEWAY_URL,
  timeout: 10000,
  withCredentials: false
})

// 请求拦截器
gateway.interceptors.request.use(
  config => {
    // 添加认证头
    if (store.getters.token) {
      config.headers['Authorization'] = `Bearer ${getToken()}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
gateway.interceptors.response.use(
  response => {
    const res = response.data
    
    // 判断请求是否成功
    if (res.code === 20000 || res.code === 200) {
      return res.data
    }
    
    // 错误处理
    Message({
      message: res.message || '请求错误',
      type: 'error',
      duration: 5000
    })
    
    // 特殊状态码处理
    if ([50008, 50012, 50014, 401].includes(res.code)) {
      // 登出提示
      MessageBox.confirm('您已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
    }
    
    return Promise.reject(new Error(res.message || '请求错误'))
  },
  error => {
    console.error('响应错误:', error)
    
    let message = '连接服务失败'
    if (error.response) {
      const status = error.response.status
      
      // HTTP状态码错误处理
      if (status === 401) {
        message = '未授权，请重新登录'
        // 可以在这里处理自动登出
      } else if (status === 403) {
        message = '拒绝访问'
      } else if (status === 404) {
        message = '请求的资源不存在'
      } else if (status === 500) {
        message = '服务器内部错误'
      } else {
        message = `请求错误 (${status})`
      }
    } else if (error.request) {
      message = '服务未响应'
    } else {
      message = error.message
    }
    
    Message({
      message,
      type: 'error',
      duration: 5000
    })
    
    return Promise.reject(error)
  }
)

/**
 * API服务前缀，用于构建网关路径
 */
export const API_SERVICES = {
  AUTH: '/auth-service',
  USER: '/user-service', 
  PRODUCT: '/product-service',
  ORDER: '/order-service',
  PAYMENT: '/payment-service'
}

/**
 * 创建服务API函数
 * @param {string} servicePath - 服务路径前缀
 * @returns {Function} - 接受路径和配置的请求函数
 */
export const createServiceApi = (servicePath) => {
  return (path, config = {}) => {
    const fullPath = `${servicePath}${path}`
    return gateway(fullPath, config)
  }
}

// 创建各个服务的API请求函数
export const auth = {
  get: (path, config) => gateway.get(`${API_SERVICES.AUTH}${path}`, config),
  post: (path, data, config) => gateway.post(`${API_SERVICES.AUTH}${path}`, data, config),
  put: (path, data, config) => gateway.put(`${API_SERVICES.AUTH}${path}`, data, config),
  delete: (path, config) => gateway.delete(`${API_SERVICES.AUTH}${path}`, config)
}

export const user = {
  get: (path, config) => gateway.get(`${API_SERVICES.USER}${path}`, config),
  post: (path, data, config) => gateway.post(`${API_SERVICES.USER}${path}`, data, config),
  put: (path, data, config) => gateway.put(`${API_SERVICES.USER}${path}`, data, config),
  delete: (path, config) => gateway.delete(`${API_SERVICES.USER}${path}`, config)
}

export const product = {
  get: (path, config) => gateway.get(`${API_SERVICES.PRODUCT}${path}`, config),
  post: (path, data, config) => gateway.post(`${API_SERVICES.PRODUCT}${path}`, data, config),
  put: (path, data, config) => gateway.put(`${API_SERVICES.PRODUCT}${path}`, data, config),
  delete: (path, config) => gateway.delete(`${API_SERVICES.PRODUCT}${path}`, config)
}

export const order = {
  get: (path, config) => gateway.get(`${API_SERVICES.ORDER}${path}`, config),
  post: (path, data, config) => gateway.post(`${API_SERVICES.ORDER}${path}`, data, config),
  put: (path, data, config) => gateway.put(`${API_SERVICES.ORDER}${path}`, data, config),
  delete: (path, config) => gateway.delete(`${API_SERVICES.ORDER}${path}`, config)
}

export const payment = {
  get: (path, config) => gateway.get(`${API_SERVICES.PAYMENT}${path}`, config),
  post: (path, data, config) => gateway.post(`${API_SERVICES.PAYMENT}${path}`, data, config),
  put: (path, data, config) => gateway.put(`${API_SERVICES.PAYMENT}${path}`, data, config),
  delete: (path, config) => gateway.delete(`${API_SERVICES.PAYMENT}${path}`, config)
}

// 导出API网关实例
export default gateway 