/**
 * 认证API - 基于API网关
 */
import { auth } from './index'

/**
 * 认证API
 */
const authApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录数据
   * @returns {Promise<Object>} 登录结果
   */
  login(data) {
    return auth.post('/login', data)
  },
  
  /**
   * 用户注册
   * @param {Object} data - 注册数据
   * @returns {Promise<Object>} 注册结果
   */
  register(data) {
    return auth.post('/register', data)
  },
  
  /**
   * 退出登录
   * @returns {Promise<Object>} 操作结果
   */
  logout() {
    return auth.post('/logout')
  },
  
  /**
   * 刷新token
   * @param {string} refreshToken - 刷新token
   * @returns {Promise<Object>} 新的token
   */
  refreshToken(refreshToken) {
    return auth.post('/refresh-token', { refreshToken })
  },
  
  /**
   * 重置密码请求
   * @param {string} email - 用户邮箱
   * @returns {Promise<Object>} 操作结果
   */
  requestPasswordReset(email) {
    return auth.post('/password/reset-request', { email })
  },
  
  /**
   * 执行密码重置
   * @param {Object} data - 重置数据
   * @returns {Promise<Object>} 操作结果
   */
  resetPassword(data) {
    return auth.post('/password/reset', data)
  },
  
  /**
   * 修改密码
   * @param {Object} data - 密码数据
   * @returns {Promise<Object>} 操作结果
   */
  changePassword(data) {
    return auth.post('/password/change', data)
  },
  
  /**
   * 发送验证码
   * @param {string} type - 验证码类型(sms/email)
   * @param {string} contact - 联系方式(手机号/邮箱)
   * @returns {Promise<Object>} 操作结果
   */
  sendVerificationCode(type, contact) {
    return auth.post('/verification-code/send', { type, contact })
  },
  
  /**
   * 验证邮箱
   * @param {string} token - 验证token
   * @returns {Promise<Object>} 验证结果
   */
  verifyEmail(token) {
    return auth.post('/email/verify', { token })
  },
  
  /**
   * 验证短信验证码
   * @param {string} phone - 手机号
   * @param {string} code - 验证码
   * @returns {Promise<Object>} 验证结果
   */
  verifySmsCode(phone, code) {
    return auth.post('/sms/verify', { phone, code })
  },
  
  /**
   * 第三方登录
   * @param {string} provider - 第三方提供商
   * @param {Object} data - 验证数据
   * @returns {Promise<Object>} 登录结果
   */
  socialLogin(provider, data) {
    return auth.post(`/social/${provider}`, data)
  },
  
  /**
   * 获取当前用户信息
   * @returns {Promise<Object>} 用户信息
   */
  getCurrentUser() {
    return auth.get('/user')
  }
}

export default authApi 