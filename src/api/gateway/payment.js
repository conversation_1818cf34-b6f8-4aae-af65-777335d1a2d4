/**
 * 支付API - 基于API网关
 */
import { payment } from './index'

/**
 * 支付API
 */
const paymentApi = {
  /**
   * 创建支付交易
   * @param {Object} data - 支付数据
   * @returns {Promise<Object>} 支付交易信息
   */
  createPayment(data) {
    return payment.post('/payments', data)
  },
  
  /**
   * 获取支付详情
   * @param {string} paymentId - 支付ID
   * @returns {Promise<Object>} 支付详情
   */
  getPayment(paymentId) {
    return payment.get(`/payments/${paymentId}`)
  },
  
  /**
   * 处理支付回调
   * @param {string} gateway - 支付网关
   * @param {Object} data - 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  handleCallback(gateway, data) {
    return payment.post(`/payments/callback/${gateway}`, data)
  },
  
  /**
   * 获取支付方式列表
   * @returns {Promise<Array>} 支付方式列表
   */
  getPaymentMethods() {
    return payment.get('/payment-methods')
  },
  
  /**
   * 获取特定区域可用的支付方式
   * @param {string} region - 区域代码
   * @returns {Promise<Array>} 支付方式列表
   */
  getRegionPaymentMethods(region) {
    return payment.get(`/payment-methods/region/${region}`)
  },
  
  /**
   * 申请退款
   * @param {string} paymentId - 支付ID
   * @param {Object} data - 退款数据
   * @returns {Promise<Object>} 退款信息
   */
  refund(paymentId, data) {
    return payment.post(`/payments/${paymentId}/refund`, data)
  },
  
  /**
   * 获取退款信息
   * @param {string} refundId - 退款ID
   * @returns {Promise<Object>} 退款详情
   */
  getRefund(refundId) {
    return payment.get(`/refunds/${refundId}`)
  },
  
  /**
   * 获取支付交易列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 交易列表
   */
  getTransactions(params) {
    return payment.get('/transactions', { params })
  },
  
  /**
   * 获取支付统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 统计数据
   */
  getPaymentStatistics(params) {
    return payment.get('/statistics', { params })
  },
  
  /**
   * 检查支付状态
   * @param {string} paymentId - 支付ID
   * @returns {Promise<Object>} 支付状态
   */
  checkPaymentStatus(paymentId) {
    return payment.get(`/payments/${paymentId}/status`)
  }
}

export default paymentApi 