/**
 * 用户API - 基于API网关
 */
import { user } from './index'

/**
 * 用户API
 */
const userApi = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 用户列表
   */
  getUsers(params) {
    return user.get('/users', { params })
  },
  
  /**
   * 获取用户详情
   * @param {number|string} id - 用户ID
   * @returns {Promise<Object>} 用户详情
   */
  getUser(id) {
    return user.get(`/users/${id}`)
  },
  
  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise<Object>} 创建的用户
   */
  createUser(data) {
    return user.post('/users', data)
  },
  
  /**
   * 更新用户
   * @param {number|string} id - 用户ID
   * @param {Object} data - 更新的用户数据
   * @returns {Promise<Object>} 更新后的用户
   */
  updateUser(id, data) {
    return user.put(`/users/${id}`, data)
  },
  
  /**
   * 删除用户
   * @param {number|string} id - 用户ID
   * @returns {Promise<Object>} 操作结果
   */
  deleteUser(id) {
    return user.delete(`/users/${id}`)
  },
  
  /**
   * 获取当前用户信息
   * @returns {Promise<Object>} 用户信息
   */
  getCurrentUser() {
    return user.get('/users/current')
  },
  
  /**
   * 更新用户头像
   * @param {number|string} id - 用户ID
   * @param {File} file - 头像文件
   * @returns {Promise<Object>} 更新结果
   */
  updateAvatar(id, file) {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return user.post(`/users/${id}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 获取用户角色
   * @param {number|string} id - 用户ID
   * @returns {Promise<Array>} 角色列表
   */
  getUserRoles(id) {
    return user.get(`/users/${id}/roles`)
  },
  
  /**
   * 分配用户角色
   * @param {number|string} id - 用户ID
   * @param {Array} roles - 角色ID列表
   * @returns {Promise<Object>} 操作结果
   */
  assignRoles(id, roles) {
    return user.post(`/users/${id}/roles`, { roles })
  },
  
  /**
   * 获取用户权限
   * @param {number|string} id - 用户ID
   * @returns {Promise<Array>} 权限列表
   */
  getUserPermissions(id) {
    return user.get(`/users/${id}/permissions`)
  },
  
  /**
   * 用户搜索
   * @param {Object} params - 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  searchUsers(params) {
    return user.get('/users/search', { params })
  },
  
  /**
   * 更新用户状态
   * @param {number|string} id - 用户ID
   * @param {string} status - 状态
   * @returns {Promise<Object>} 操作结果
   */
  updateUserStatus(id, status) {
    return user.put(`/users/${id}/status`, { status })
  },
  
  /**
   * 获取用户收货地址
   * @param {number|string} userId - 用户ID
   * @returns {Promise<Array>} 地址列表
   */
  getUserAddresses(userId) {
    return user.get(`/users/${userId}/addresses`)
  },
  
  /**
   * 添加用户收货地址
   * @param {number|string} userId - 用户ID
   * @param {Object} address - 地址数据
   * @returns {Promise<Object>} 创建的地址
   */
  addUserAddress(userId, address) {
    return user.post(`/users/${userId}/addresses`, address)
  }
}

export default userApi 