/**
 * 订单API - 基于API网关
 */
import { order } from './index'

/**
 * 订单API
 */
const orderApi = {
  /**
   * 获取订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 订单列表
   */
  getOrders(params) {
    return order.get('/orders', { params })
  },
  
  /**
   * 获取订单详情
   * @param {number|string} id - 订单ID
   * @returns {Promise<Object>} 订单详情
   */
  getOrder(id) {
    return order.get(`/orders/${id}`)
  },
  
  /**
   * 创建订单
   * @param {Object} data - 订单数据
   * @returns {Promise<Object>} 创建的订单
   */
  createOrder(data) {
    return order.post('/orders', data)
  },
  
  /**
   * 更新订单
   * @param {number|string} id - 订单ID
   * @param {Object} data - 更新的订单数据
   * @returns {Promise<Object>} 更新后的订单
   */
  updateOrder(id, data) {
    return order.put(`/orders/${id}`, data)
  },
  
  /**
   * 取消订单
   * @param {number|string} id - 订单ID
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 操作结果
   */
  cancelOrder(id, reason) {
    return order.post(`/orders/${id}/cancel`, { reason })
  },
  
  /**
   * 获取用户订单
   * @param {number|string} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 订单列表
   */
  getUserOrders(userId, params) {
    return order.get(`/users/${userId}/orders`, { params })
  },
  
  /**
   * 更新订单状态
   * @param {number|string} id - 订单ID
   * @param {string} status - 订单状态
   * @param {Object} data - 附加数据
   * @returns {Promise<Object>} 更新后的订单
   */
  updateOrderStatus(id, status, data = {}) {
    return order.put(`/orders/${id}/status`, { status, ...data })
  },
  
  /**
   * 订单支付通知处理
   * @param {number|string} id - 订单ID
   * @param {Object} paymentData - 支付数据
   * @returns {Promise<Object>} 处理结果
   */
  processPaymentNotification(id, paymentData) {
    return order.post(`/orders/${id}/payment-notification`, paymentData)
  },
  
  /**
   * 获取订单统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 统计数据
   */
  getOrderStatistics(params) {
    return order.get('/orders/statistics', { params })
  },
  
  /**
   * 导出订单数据
   * @param {Object} params - 导出参数
   * @returns {Promise<Blob>} 导出文件
   */
  exportOrders(params) {
    return order.get('/orders/export', { 
      params,
      responseType: 'blob'
    })
  }
}

export default orderApi 