// 引入原有API
import * as user from './user'
import * as article from './article'
import * as role from './role'
import * as qiniu from './qiniu'
import * as remoteSearch from './remote-search'

// 引入REST API（按资源组织）
import productsApi from './rest/products'
import customersApi from './rest/customers'
import ordersApi from './rest/orders'
import authApi from './rest/auth'
import usersApi from './rest/users'
import paymentsApi from './rest/payments'
import uploadApi from './rest/upload'
import settingsApi from './rest/settings'
import statisticsApi from './rest/statistics'
import notificationsApi from './rest/notifications'

// 引入服务API（按服务组织）
import { SERVICES, getServiceRequest } from './service'
import authService from './service/auth'
import userService from './service/user'
import productService from './service/product'
import orderService from './service/order'
import paymentService from './service/payment'

// 导出服务信息
import { serviceBaseUrls } from '@/utils/request'
export const services = {
  baseUrls: serviceBaseUrls,
  names: Object.keys(serviceBaseUrls)
}

// 导出服务API（按服务组织）
export const serviceApi = {
  auth: authService,
  user: userService,
  product: productService,
  order: orderService,
  payment: paymentService,
  getServiceRequest,
  SERVICES
}

// 导出所有API（按资源组织）
export {
  // 原有API
  user,
  article,
  role,
  qiniu,
  remoteSearch,
  
  // REST API
  productsApi,
  customersApi,
  ordersApi,
  authApi,
  usersApi,
  paymentsApi,
  uploadApi,
  settingsApi,
  statisticsApi,
  notificationsApi,
  
  // 单独导出服务API
  authService,
  userService,
  productService,
  orderService,
  paymentService
}

// 默认导出所有API的集合
export default {
  // 原有API
  user,
  article,
  role,
  qiniu,
  remoteSearch,
  
  // REST API（按资源组织）
  products: productsApi,
  customers: customersApi,
  orders: ordersApi,
  auth: authApi,
  users: usersApi,
  payments: paymentsApi,
  upload: uploadApi,
  settings: settingsApi,
  statistics: statisticsApi,
  notifications: notificationsApi,
  
  // 服务API（按服务组织）
  service: serviceApi
} 