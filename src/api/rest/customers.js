import { createRestApi } from './index'
import request from '@/utils/request'

// 创建基本的客户REST API
const customersApi = createRestApi('customers')

// 扩展客户专有的API
export default {
  ...customersApi,
  // 获取客户订单列表
  getCustomerOrders(customerId, params) {
    return request({
      url: `/customers/${customerId}/orders`,
      method: 'get',
      params
    })
  },
  // 客户高级搜索
  advancedSearch(params) {
    return request({
      url: '/customers/advanced-search',
      method: 'get',
      params
    })
  },
  // 获取客户购买历史
  getPurchaseHistory(customerId, params) {
    return request({
      url: `/customers/${customerId}/purchase-history`,
      method: 'get',
      params
    })
  }
} 