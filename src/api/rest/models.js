/**
 * API 数据模型定义
 * 这个文件定义了与后端交互时使用的主要数据结构
 */

/**
 * 用户模型
 * @typedef {Object} User
 * @property {number} id - 用户ID
 * @property {string} username - 用户名
 * @property {string} email - 邮箱
 * @property {string} avatar - 头像URL
 * @property {string} role - 角色
 * @property {string} status - 状态
 * @property {Date} createdAt - 创建时间
 * @property {Date} updatedAt - 更新时间
 */

/**
 * 产品模型
 * @typedef {Object} Product
 * @property {number} id - 产品ID
 * @property {string} name - 产品名称
 * @property {string} description - 产品描述
 * @property {number} price - 产品价格
 * @property {number} stock - 库存数量
 * @property {string} category - 产品类别
 * @property {string[]} images - 产品图片URL列表
 * @property {boolean} active - 是否激活
 * @property {Date} createdAt - 创建时间
 * @property {Date} updatedAt - 更新时间
 */

/**
 * 客户模型
 * @typedef {Object} Customer
 * @property {number} id - 客户ID
 * @property {string} name - 客户名称
 * @property {string} email - 邮箱
 * @property {string} phone - 电话
 * @property {string} address - 地址
 * @property {Date} registerDate - 注册日期
 * @property {number} orderCount - 订单数
 * @property {Date} lastOrderDate - 最近订单日期
 * @property {Date} createdAt - 创建时间
 * @property {Date} updatedAt - 更新时间
 */

/**
 * 订单模型
 * @typedef {Object} Order
 * @property {number} id - 订单ID
 * @property {number} customerId - 客户ID
 * @property {Customer} customer - 客户信息
 * @property {OrderItem[]} items - 订单项
 * @property {number} totalAmount - 总金额
 * @property {string} status - 订单状态
 * @property {string} paymentMethod - 支付方式
 * @property {boolean} paid - 是否已支付
 * @property {Date} orderDate - 下单日期
 * @property {Date} createdAt - 创建时间
 * @property {Date} updatedAt - 更新时间
 */

/**
 * 订单项模型
 * @typedef {Object} OrderItem
 * @property {number} id - 订单项ID
 * @property {number} orderId - 订单ID
 * @property {number} productId - 产品ID
 * @property {Product} product - 产品信息
 * @property {number} quantity - 数量
 * @property {number} price - 价格
 * @property {number} totalPrice - 总价
 */

/**
 * 通知模型
 * @typedef {Object} Notification
 * @property {number} id - 通知ID
 * @property {number} userId - 用户ID
 * @property {string} title - 标题
 * @property {string} content - 内容
 * @property {string} type - 通知类型
 * @property {boolean} read - 是否已读
 * @property {Date} createdAt - 创建时间
 */

/**
 * 统计数据模型
 * @typedef {Object} Statistics
 * @property {number} totalSales - 总销售额
 * @property {number} orderCount - 订单数
 * @property {number} customerCount - 客户数
 * @property {number} productCount - 产品数
 * @property {Object} salesByCategory - 按类别的销售额
 * @property {Object} salesByPeriod - 按时间段的销售额
 */

// 后端响应结构
export const ApiResponse = {
  /**
   * 成功响应
   * @param {any} data - 响应数据
   * @param {string} message - 响应消息
   * @returns {Object} 格式化的响应对象
   */
  success: (data, message = 'operation successful') => ({
    code: 20000,
    data,
    message
  }),

  /**
   * 错误响应
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   * @returns {Object} 格式化的错误响应对象
   */
  error: (message, code = 50000) => ({
    code,
    message
  })
}

// 分页请求参数
export const PaginationParams = {
  /**
   * 创建分页参数
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @param {string} sort - 排序字段
   * @param {string} order - 排序方向
   * @returns {Object} 分页参数对象
   */
  create: (page = 1, limit = 20, sort = 'id', order = 'desc') => ({
    page,
    limit,
    sort,
    order
  })
}

// 导出类型定义（仅用于注释，实际使用时 JS 不需要）
export default {
  ApiResponse,
  PaginationParams
} 