import request from '@/utils/request'

// 认证相关API，使用用户服务
export default {
  // 用户登录
  login(data) {
    return request({
      url: '/auth/login',
      method: 'post',
      data
    }, 'user')
  },
  // 用户注册
  register(data) {
    return request({
      url: '/auth/register',
      method: 'post',
      data
    }, 'user')
  },
  // 刷新token
  refreshToken(refreshToken) {
    return request({
      url: '/auth/refresh-token',
      method: 'post',
      data: { refreshToken }
    }, 'user')
  },
  // 退出登录
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post'
    }, 'user')
  },
  // 重置密码
  resetPassword(data) {
    return request({
      url: '/auth/reset-password',
      method: 'post',
      data
    }, 'user')
  },
  // 发送验证码
  sendVerificationCode(phoneOrEmail) {
    return request({
      url: '/auth/verification-code',
      method: 'post',
      data: { contact: phoneOrEmail }
    }, 'user')
  },
  // 验证邮箱
  verifyEmail(token) {
    return request({
      url: '/auth/verify-email',
      method: 'post',
      data: { token }
    }, 'user')
  }
} 