import request from '@/utils/request'

// 系统设置相关API
export default {
  // 获取所有设置
  getAllSettings() {
    return request({
      url: '/settings',
      method: 'get'
    })
  },
  
  // 获取特定类别的设置
  getCategorySettings(category) {
    return request({
      url: `/settings/${category}`,
      method: 'get'
    })
  },
  
  // 更新单个设置
  updateSetting(key, value) {
    return request({
      url: `/settings/${key}`,
      method: 'put',
      data: { value }
    })
  },
  
  // 批量更新设置
  batchUpdateSettings(data) {
    return request({
      url: '/settings/batch',
      method: 'put',
      data
    })
  },
  
  // 重置特定设置
  resetSetting(key) {
    return request({
      url: `/settings/${key}/reset`,
      method: 'post'
    })
  },
  
  // 重置所有设置
  resetAllSettings() {
    return request({
      url: '/settings/reset-all',
      method: 'post'
    })
  }
} 