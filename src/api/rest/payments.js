import { createRestApi } from './index'
import request from '@/utils/request'

// 创建基本的支付REST API，使用支付服务
const paymentsApi = createRestApi('payments', 'payment')

// 扩展支付专有的API
export default {
  ...paymentsApi,
  
  // 创建支付
  createPayment(orderData) {
    return request({
      url: '/payments/create',
      method: 'post',
      data: orderData
    }, 'payment')
  },
  
  // 获取支付方式列表
  getPaymentMethods() {
    return request({
      url: '/payments/methods',
      method: 'get'
    }, 'payment')
  },
  
  // 验证支付状态
  verifyPayment(paymentId) {
    return request({
      url: `/payments/${paymentId}/verify`,
      method: 'get'
    }, 'payment')
  },
  
  // 取消支付
  cancelPayment(paymentId, reason) {
    return request({
      url: `/payments/${paymentId}/cancel`,
      method: 'post',
      data: { reason }
    }, 'payment')
  },
  
  // 退款
  refund(paymentId, refundData) {
    return request({
      url: `/payments/${paymentId}/refund`,
      method: 'post',
      data: refundData
    }, 'payment')
  },
  
  // 获取交易历史
  getTransactionHistory(params) {
    return request({
      url: '/payments/transactions',
      method: 'get',
      params
    }, 'payment')
  },
  
  // 获取支付统计
  getPaymentStatistics(params) {
    return request({
      url: '/payments/statistics',
      method: 'get',
      params
    }, 'payment')
  }
} 