import request from '@/utils/request'

// 统计数据相关API
export default {
  // 获取仪表盘统计数据
  getDashboardStatistics() {
    return request({
      url: '/statistics/dashboard',
      method: 'get'
    })
  },
  
  // 获取销售统计
  getSalesStatistics(params) {
    return request({
      url: '/statistics/sales',
      method: 'get',
      params
    })
  },
  
  // 获取用户活跃度统计
  getUserActivityStatistics(params) {
    return request({
      url: '/statistics/user-activity',
      method: 'get',
      params
    })
  },
  
  // 获取产品统计
  getProductStatistics(params) {
    return request({
      url: '/statistics/products',
      method: 'get',
      params
    })
  },
  
  // 获取收入统计
  getRevenueStatistics(params) {
    return request({
      url: '/statistics/revenue',
      method: 'get',
      params
    })
  },
  
  // 获取自定义时间范围的统计
  getCustomRangeStatistics(startDate, endDate, type = 'sales') {
    return request({
      url: '/statistics/custom-range',
      method: 'get',
      params: { 
        startDate,
        endDate,
        type
      }
    })
  },
  
  // 导出统计数据
  exportStatistics(params) {
    return request({
      url: '/statistics/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
} 