import request from '@/utils/request'

// 文件上传相关API
export default {
  // 上传单个文件
  uploadFile(file, onUploadProgress) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/upload/file',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    })
  },
  
  // 上传多个文件
  uploadMultipleFiles(files, onUploadProgress) {
    const formData = new FormData()
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    
    return request({
      url: '/upload/multiple',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    })
  },
  
  // 上传图片并压缩
  uploadImage(image, options = {}, onUploadProgress) {
    const formData = new FormData()
    formData.append('image', image)
    
    if (options.width) formData.append('width', options.width)
    if (options.height) formData.append('height', options.height)
    if (options.quality) formData.append('quality', options.quality)
    
    return request({
      url: '/upload/image',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    })
  },
  
  // 删除已上传文件
  deleteFile(fileId) {
    return request({
      url: `/upload/file/${fileId}`,
      method: 'delete'
    })
  },
  
  // 获取文件列表
  getFiles(params) {
    return request({
      url: '/upload/files',
      method: 'get',
      params
    })
  }
} 