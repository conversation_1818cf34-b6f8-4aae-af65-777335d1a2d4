import { createRestApi } from './index'
import request from '@/utils/request'

// 创建基本的订单REST API，使用订单服务
const ordersApi = createRestApi('orders', 'order')

// 扩展订单专有的API
export default {
  ...ordersApi,
  // 获取订单详情（包含产品、客户等详细信息）
  getOrderDetails(orderId) {
    return request({
      url: `/orders/${orderId}/details`,
      method: 'get'
    }, 'order')
  },
  // 更新订单状态
  updateStatus(orderId, status) {
    return request({
      url: `/orders/${orderId}/status`,
      method: 'patch',
      data: { status }
    }, 'order')
  },
  // 获取订单统计数据
  getStatistics(params) {
    return request({
      url: '/orders/statistics',
      method: 'get',
      params
    }, 'order')
  },
  // 取消订单
  cancelOrder(orderId, reason) {
    return request({
      url: `/orders/${orderId}/cancel`,
      method: 'post',
      data: { reason }
    }, 'order')
  },
  // 处理支付相关操作，使用支付服务
  processPayment(orderId, paymentData) {
    return request({
      url: `/orders/${orderId}/payment`,
      method: 'post',
      data: paymentData
    }, 'payment')
  }
} 