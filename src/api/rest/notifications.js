import { createRest<PERSON>pi } from './index'
import request from '@/utils/request'

// 创建基本的通知REST API
const notificationsApi = createRestApi('notifications')

// 扩展通知专有的API
export default {
  ...notificationsApi,
  
  // 获取未读通知数量
  getUnreadCount() {
    return request({
      url: '/notifications/unread-count',
      method: 'get'
    })
  },
  
  // 标记通知为已读
  markAsRead(notificationId) {
    return request({
      url: `/notifications/${notificationId}/read`,
      method: 'patch'
    })
  },
  
  // 标记所有通知为已读
  markAllAsRead() {
    return request({
      url: '/notifications/read-all',
      method: 'patch'
    })
  },
  
  // 获取用户通知设置
  getNotificationSettings() {
    return request({
      url: '/notifications/settings',
      method: 'get'
    })
  },
  
  // 更新用户通知设置
  updateNotificationSettings(settings) {
    return request({
      url: '/notifications/settings',
      method: 'put',
      data: settings
    })
  },
  
  // 订阅推送通知
  subscribeToPushNotifications(subscription) {
    return request({
      url: '/notifications/push-subscribe',
      method: 'post',
      data: subscription
    })
  },
  
  // 取消订阅推送通知
  unsubscribeFromPushNotifications() {
    return request({
      url: '/notifications/push-unsubscribe',
      method: 'post'
    })
  }
} 