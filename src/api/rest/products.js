import { createRestApi } from './index'
import request from '@/utils/request'

// 创建基本的产品REST API，使用产品服务
const productsApi = createRestApi('products', 'product')

// 扩展产品专有的API
export default {
  ...productsApi,
  // 获取热门产品
  getHotProducts(params) {
    return request({
      url: '/products/hot',
      method: 'get',
      params
    }, 'product')
  },
  // 产品搜索
  search(keyword, params) {
    return request({
      url: '/products/search',
      method: 'get',
      params: { keyword, ...params }
    }, 'product')
  },
  // 更新产品库存
  updateStock(id, quantity) {
    return request({
      url: `/products/${id}/stock`,
      method: 'patch',
      data: { quantity }
    }, 'product')
  }
} 