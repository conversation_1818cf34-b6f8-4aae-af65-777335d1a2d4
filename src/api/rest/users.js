import { createRest<PERSON><PERSON> } from './index'
import request from '@/utils/request'

// 创建基本的用户REST API，使用用户服务
const usersApi = createRestApi('users', 'user')

// 扩展用户专有的API
export default {
  ...usersApi,
  
  // 获取当前用户信息
  getCurrentUser() {
    return request({
      url: '/users/current',
      method: 'get'
    }, 'user')
  },
  
  // 更新用户个人资料
  updateProfile(userId, profileData) {
    return request({
      url: `/users/${userId}/profile`,
      method: 'put',
      data: profileData
    }, 'user')
  },
  
  // 更新用户头像
  updateAvatar(userId, avatarFile) {
    const formData = new FormData()
    formData.append('avatar', avatarFile)
    
    return request({
      url: `/users/${userId}/avatar`,
      method: 'put',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }, 'user')
  },
  
  // 修改密码
  changePassword(userId, passwordData) {
    return request({
      url: `/users/${userId}/password`,
      method: 'put',
      data: passwordData
    }, 'user')
  },
  
  // 获取用户角色和权限
  getUserRoles(userId) {
    return request({
      url: `/users/${userId}/roles`,
      method: 'get'
    }, 'user')
  },
  
  // 分配角色
  assignRoles(userId, roles) {
    return request({
      url: `/users/${userId}/roles`,
      method: 'put',
      data: { roles }
    }, 'user')
  }
} 