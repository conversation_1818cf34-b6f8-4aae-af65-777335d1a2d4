# REST API 模块

这个目录包含了所有 RESTful API 的接口定义，基于统一的请求封装，方便与后端服务交互。支持多种微服务架构下的接口调用。

## 目录结构

```
rest/
├── index.js            # 通用 REST API 创建函数
├── auth.js             # 认证相关 API (用户服务)
├── customers.js        # 客户相关 API (主服务)
├── notifications.js    # 通知相关 API (主服务)
├── orders.js           # 订单相关 API (订单服务)
├── payments.js         # 支付相关 API (支付服务)
├── products.js         # 产品相关 API (产品服务)
├── settings.js         # 系统设置 API (主服务)
├── statistics.js       # 统计数据 API (主服务)
├── upload.js           # 文件上传 API (主服务)
└── users.js            # 用户相关 API (用户服务)
```

## 多服务架构

系统支持多种服务的调用，主要包括以下服务：

- **主服务** (`main`): 默认服务，处理一般请求
- **用户服务** (`user`): 处理用户认证、权限等
- **产品服务** (`product`): 处理产品相关功能
- **订单服务** (`order`): 处理订单相关功能
- **支付服务** (`payment`): 处理支付相关功能

## 使用方法

### 通用 REST API

`index.js` 文件提供了创建通用 REST API 的函数，支持标准的 CRUD 操作，并可指定服务：

```js
import { createRestApi } from '@/api/rest/index'

// 创建一个资源的 REST API，指定服务
const resourceApi = createRestApi('resource-name', 'service-name')

// 使用 API
// 获取列表
resourceApi.list({ page: 1, limit: 10 })
// 获取单个资源
resourceApi.get(123)
// 创建资源
resourceApi.create({ name: '新资源', ... })
// 更新资源
resourceApi.update(123, { name: '更新的资源', ... })
// 删除资源
resourceApi.delete(123)
```

### 专门的 API 模块

每个专门的 API 模块都从通用 REST API 扩展而来，并添加了特定的方法：

```js
// 引入产品 API (使用产品服务)
import productsApi from '@/api/rest/products'

// 使用标准 CRUD 方法
productsApi.list({ category: 'electronics' })
productsApi.get(123)

// 使用特定方法
productsApi.getHotProducts()
productsApi.search('手机')
```

### 跨服务调用示例

不同服务之间的协作示例：

```js
// 导入不同服务的API
import { ordersApi, productsApi, paymentsApi } from '@/api'

// 创建订单流程
async function createOrderFlow(productId, quantity, userId) {
  // 1. 检查产品库存 (产品服务)
  const product = await productsApi.get(productId)
  
  if (product.stock < quantity) {
    throw new Error('库存不足')
  }
  
  // 2. 创建订单 (订单服务)
  const order = await ordersApi.create({
    productId,
    quantity,
    userId
  })
  
  // 3. 创建支付 (支付服务)
  const payment = await paymentsApi.createPayment({
    orderId: order.id,
    amount: product.price * quantity
  })
  
  // 4. 更新库存 (产品服务)
  await productsApi.updateStock(productId, -quantity)
  
  return {
    order,
    payment
  }
}
```

## 统一导入

所有 API 都可以从统一的入口导入：

```js
// 单个导入
import { productsApi, ordersApi } from '@/api'

// 或者整体导入
import api from '@/api'
api.products.list()
api.orders.getOrderDetails(123)
```

## 服务信息

可以获取可用的服务信息：

```js
import { services } from '@/api'

// 获取所有服务名称
console.log(services.names) // ['main', 'user', 'product', 'order', 'payment']

// 获取服务基础URL
console.log(services.baseUrls.user) // 'http://example.com/user-service'
``` 