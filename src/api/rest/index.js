import request from '@/utils/request'

/**
 * 创建通用的REST API请求方法
 * @param {string} resource - API资源路径
 * @param {string} serviceName - 服务名称(main, user, product, order, payment)
 * @returns {Object} 包含CRUD操作的对象
 */
export const createRestApi = (resource, serviceName = 'main') => {
  return {
    list(params) {
      return request({
        url: `/${resource}`,
        method: 'get',
        params
      }, serviceName)
    },
    get(id) {
      return request({
        url: `/${resource}/${id}`,
        method: 'get'
      }, serviceName)
    },
    create(data) {
      return request({
        url: `/${resource}`,
        method: 'post',
        data
      }, serviceName)
    },
    update(id, data) {
      return request({
        url: `/${resource}/${id}`,
        method: 'put',
        data
      }, serviceName)
    },
    delete(id) {
      return request({
        url: `/${resource}/${id}`,
        method: 'delete'
      }, serviceName)
    }
  }
} 