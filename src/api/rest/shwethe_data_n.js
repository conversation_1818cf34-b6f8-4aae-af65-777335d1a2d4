import { createRest<PERSON><PERSON> } from './index'
import request from '@/utils/request'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import { serviceBaseUrls } from '@/utils/request'

const shwetheDataApi = createRestApi('shwethe_data_n', 'shwethe_data_n')

// 添加自定义API方法，直接使用axios，绕过request拦截器的错误处理
export const postCheci = async (data) => {
  try {
    const response = await axios({
      method: 'post',
      url: `${serviceBaseUrls.shwethe_data_n}/api/checi`,
      headers: {
        'Authorization': 'Bearer ' + getToken(),
        'Content-Type': 'application/json'
      },
      data
    })
    
    return {
      success: true,
      data: response.data
    }
  } catch (error) {
    console.error('API调用失败:', error)
    return {
      success: false,
      error: error.response ? error.response.data : error.message
    }
  }
}

export default shwetheDataApi
