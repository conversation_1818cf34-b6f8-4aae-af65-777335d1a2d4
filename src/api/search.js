import request from '@/utils/request'

// 基础搜索URL
const BASE_SEARCH_URL = 'http://pv-api.shwethe.com/mongodb_data_api/api/v1/search'

/**
 * 嘉益文本搜索 v3
 * @param {string} text - 搜索文本
 * @returns {Promise} 返回搜索结果
 */
export function searchJiaYiText(text) {
  return request({
    url: `${BASE_SEARCH_URL}/jia_yi_search_text_v3`,
    method: 'get',
    params: { text },
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    transformResponse: [(data) => {
      try {
        const jsonData = JSON.parse(data)
        return {
          code: 20000,
          data: jsonData
        }
      } catch (e) {
        return {
          code: 50000,
          message: 'Parse data error',
          data: []
        }
      }
    }]
  })
}

/**
 * 嘉益高级搜索
 * @param {Object} params - 搜索参数
 * @param {string} params.text - 搜索文本
 * @param {string} [params.startDate] - 开始日期 YYYY-MM-DD
 * @param {string} [params.endDate] - 结束日期 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} 返回搜索结果
 */
export function searchJiaYiAdvanced(params) {
  return request({
    url: `${BASE_SEARCH_URL}/jia_yi_search_advanced`,
    method: 'get',
    params
  })
}

/**
 * 嘉益项目搜索
 * @param {Object} params - 搜索参数
 * @param {string} params.projectId - 项目ID
 * @param {string} [params.startDate] - 开始日期 YYYY-MM-DD
 * @param {string} [params.endDate] - 结束日期 YYYY-MM-DD
 * @returns {Promise} 返回搜索结果
 */
export function searchJiaYiProject(params) {
  return request({
    url: `${BASE_SEARCH_URL}/jia_yi_search_project`,
    method: 'get',
    params
  })
}

/**
 * 嘉益客户搜索
 * @param {Object} params - 搜索参数
 * @param {string} params.customerId - 客户ID
 * @param {string} [params.startDate] - 开始日期 YYYY-MM-DD
 * @param {string} [params.endDate] - 结束日期 YYYY-MM-DD
 * @returns {Promise} 返回搜索结果
 */
export function searchJiaYiCustomer(params) {
  return request({
    url: `${BASE_SEARCH_URL}/jia_yi_search_customer`,
    method: 'get',
    params
  })
}
