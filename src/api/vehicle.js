import request from '@/utils/request'

// 获取车辆列表（支持分页和排序）
export function fetchVehicleList(query) {
  return request({
    url: '/api/v1/vehicles',
    method: 'get',
    params: query // 支持 page, size, order_by, order_direction 参数
  }, 'shwethe_drive_ops')
}

// 获取单个车辆详情
export function fetchVehicle(che_liang_id) {
  return request({
    url: `/api/v1/vehicles/${che_liang_id}`,
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 创建车辆
export function createVehicle(data) {
  return request({
    url: '/api/v1/vehicles',
    method: 'post',
    data
  }, 'shwethe_drive_ops')
}

// 更新车辆
export function updateVehicle(data) {
  return request({
    url: `/api/v1/vehicles/${data.che_liang_id}`,
    method: 'put',
    data
  }, 'shwethe_drive_ops')
}

// 删除车辆
export function deleteVehicle(che_liang_id) {
  return request({
    url: `/api/v1/vehicles/${che_liang_id}`,
    method: 'delete'
  }, 'shwethe_drive_ops')
}

// 搜索车辆
export function searchVehicles(query) {
  return request({
    url: '/api/v1/vehicles/search',
    method: 'get',
    params: query // 支持 q, page, size 参数
  }, 'shwethe_drive_ops')
}

// 获取车辆统计信息
export function getVehicleStats() {
  return request({
    url: '/api/v1/vehicles/stats',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 根据车主ID获取车辆列表
export function getVehiclesByOwner(ownerId) {
  return request({
    url: `/api/v1/vehicles/owner/${ownerId}`,
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 获取车辆类型列表
export function getVehicleTypes() {
  return request({
    url: '/api/v1/vehicle-types',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 创建车辆类型
export function createVehicleType(data) {
  return request({
    url: '/api/v1/vehicle-types',
    method: 'post',
    data
  }, 'shwethe_drive_ops')
}

// 获取单个车辆类型
export function getVehicleType(id) {
  return request({
    url: `/api/v1/vehicle-types/${id}`,
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 更新车辆类型
export function updateVehicleType(id, data) {
  return request({
    url: `/api/v1/vehicle-types/${id}`,
    method: 'put',
    data
  }, 'shwethe_drive_ops')
}

// 删除车辆类型
export function deleteVehicleType(id) {
  return request({
    url: `/api/v1/vehicle-types/${id}`,
    method: 'delete'
  }, 'shwethe_drive_ops')
}

// 获取车主列表
export function getOwners(query) {
  return request({
    url: '/api/v1/owners',
    method: 'get',
    params: query // 支持 page, size 参数
  }, 'shwethe_drive_ops')
}

// 获取进入记录列表
export function getInserts(query) {
  return request({
    url: '/api/v1/inserts',
    method: 'get',
    params: query // 支持 page, size 参数
  }, 'shwethe_drive_ops')
}

// 获取特定车辆的每日输入记录
export function getVehicleInserts(vehicleId, query) {
  return request({
    url: `/api/v1/inserts/vehicle/${vehicleId}`,
    method: 'get',
    params: query // 支持 page, size 参数
  }, 'shwethe_drive_ops')
}

// 获取今天的所有insert记录
export function getTodayInserts(query) {
  return request({
    url: '/api/v1/inserts/today',
    method: 'get',
    params: query // 支持 page, size 参数
  }, 'shwethe_drive_ops')
}

// 获取所有insert记录（不限日期）
export function getAllInserts(query) {
  return request({
    url: '/api/v1/inserts',
    method: 'get',
    params: query // 支持 page, size 参数
  }, 'shwethe_drive_ops')
}

// 获取车辆的最新insert记录
export function getVehicleLatestInsert(vehicleId) {
  return request({
    url: `/api/v1/inserts/vehicle/${vehicleId}`,
    method: 'get',
    params: { page: 1, size: 1 } // 只获取最新的一条记录
  }, 'shwethe_drive_ops')
}

// 获取文档列表
export function getDocuments(query) {
  return request({
    url: '/api/v1/documents',
    method: 'get',
    params: query // 支持 page, size 参数
  }, 'shwethe_drive_ops')
}

// 健康检查
export function healthCheck() {
  return request({
    url: '/api/v1/health',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 获取系统信息
export function getSystemInfo() {
  return request({
    url: '/api/v1/info',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 车辆状态管理 API

// 获取状态选项
export function getVehicleStatusOptions() {
  return request({
    url: '/api/v1/vehicles/status-options',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 根据状态获取车辆
export function getVehiclesByStatus(query) {
  return request({
    url: '/api/v1/vehicles/status',
    method: 'get',
    params: query // 支持 status, page, size 参数
  }, 'shwethe_drive_ops')
}

// 更新车辆状态
export function updateVehicleStatus(vehicleId, data) {
  return request({
    url: `/api/v1/vehicles/${vehicleId}/status`,
    method: 'put',
    data
  }, 'shwethe_drive_ops')
}

// 根据insert记录更新车辆状态
export function updateVehicleStatusFromInsert(vehicleId, insertData) {
  // 状态映射：将insert记录中的状态映射为API接受的状态
  const statusMapping = {
    'pause': 'parked', // pause 状态转换为 parked
    'repair': 'maintenance', // repair 状态转换为 maintenance
    'broken': 'damaged', // broken 状态转换为 damaged
    'active_problem': 'active', // active_problem 状态转换为 active
    'active': 'active',
    'parked': 'parked',
    'maintenance': 'maintenance',
    'damaged': 'damaged'
  }

  const apiStatus = statusMapping[insertData.status] || insertData.status

  return request({
    url: `/api/v1/vehicles/${vehicleId}/status`,
    method: 'put',
    data: {
      status: apiStatus,
      remark: `从进入记录自动更新 (${insertData.datetime})`
    }
  }, 'shwethe_drive_ops')
}

// 获取状态统计
export function getVehicleStatusStats() {
  return request({
    url: '/api/v1/vehicles/status-stats',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 获取车辆统计汇总
export function getVehicleCountSummary() {
  return request({
    url: '/api/v1/vehicles/count-summary',
    method: 'get'
  }, 'shwethe_drive_ops')
}

// 获取交叉统计数据
export function getCrossStats(filterType, filterValue) {
  return request({
    url: '/api/v1/vehicles/cross-stats',
    method: 'get',
    params: {
      filter_type: filterType,
      filter_value: filterValue
    }
  }, 'shwethe_drive_ops')
}

// 获取双重筛选统计
export function getDoubleFilterStats(typeId, status) {
  return request({
    url: '/api/v1/vehicles/double-filter-stats',
    method: 'get',
    params: {
      type_id: typeId,
      status: status
    }
  }, 'shwethe_drive_ops')
}
