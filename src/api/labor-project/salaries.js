import laborRequest from './index'

/**
 * 薪资管理 API
 */

// ==================== 薪资汇总 API ====================

// 获取薪资汇总列表
export function getSalarySummaryList(params) {
  return laborRequest({
    url: '/api/v1/salary-summaries',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  })
}

// 获取薪资汇总详情
export function getSalarySummaryDetail(id) {
  return laborRequest({
    url: `/api/v1/salary-summaries/${id}`,
    method: 'get'
  })
}

// 创建薪资汇总
export function createSalarySummary(data) {
  return laborRequest({
    url: '/api/v1/salary-summaries',
    method: 'post',
    data
  })
}

// 更新薪资汇总
export function updateSalarySummary(id, data) {
  return laborRequest({
    url: `/api/v1/salary-summaries/${id}`,
    method: 'put',
    data
  })
}

// 删除薪资汇总
export function deleteSalarySummary(id) {
  return laborRequest({
    url: `/api/v1/salary-summaries/${id}`,
    method: 'delete'
  })
}

// 获取薪资详细分解
export function getSalaryBreakdown(id) {
  return laborRequest({
    url: `/salaries/${id}/breakdown`,
    method: 'get'
  })
}

// ==================== 工资类型 API ====================

// 获取工资类型列表
export function getSalaryTypeList(params) {
  return laborRequest({
    url: '/api/v1/salary-types',
    method: 'get',
    params
  })
}

// 获取工资类型详情
export function getSalaryTypeDetail(id) {
  return laborRequest({
    url: `/api/v1/salary-types/${id}`,
    method: 'get'
  })
}

// 创建工资类型
export function createSalaryType(data) {
  return laborRequest({
    url: '/api/v1/salary-types',
    method: 'post',
    data
  })
}

// 更新工资类型
export function updateSalaryType(id, data) {
  return laborRequest({
    url: `/api/v1/salary-types/${id}`,
    method: 'put',
    data
  })
}

// 删除工资类型
export function deleteSalaryType(id) {
  return laborRequest({
    url: `/api/v1/salary-types/${id}`,
    method: 'delete'
  })
}

// 获取工资类型的可用项目
export function getSalaryTypeEligibleItems(salaryTypeId) {
  return laborRequest({
    url: `/api/v1/salary-types/${salaryTypeId}/eligible-items`,
    method: 'get'
  })
}

// 批量更新工资类型项目关联
export function updateSalaryTypeItemEligibility(salaryTypeId, itemIds) {
  return laborRequest({
    url: `/api/v1/salary-types/${salaryTypeId}/eligible-items`,
    method: 'put',
    data: itemIds
  })
}

// ==================== 工资项目类型 API ====================

// 获取工资项目类型列表
export function getSalaryItemTypeList(params) {
  return laborRequest({
    url: '/api/v1/salary-item-types',
    method: 'get',
    params
  })
}

// 获取工资项目类型详情
export function getSalaryItemTypeDetail(id) {
  return laborRequest({
    url: `/api/v1/salary-item-types/${id}`,
    method: 'get'
  })
}

// 创建工资项目类型
export function createSalaryItemType(data) {
  return laborRequest({
    url: '/api/v1/salary-item-types',
    method: 'post',
    data
  })
}

// 更新工资项目类型
export function updateSalaryItemType(id, data) {
  return laborRequest({
    url: `/api/v1/salary-item-types/${id}`,
    method: 'put',
    data
  })
}

// 删除工资项目类型
export function deleteSalaryItemType(id) {
  return laborRequest({
    url: `/api/v1/salary-item-types/${id}`,
    method: 'delete'
  })
}

// ==================== 工资类型与薪资项目关联关系 API ====================

// 获取薪资项目类型的关联工资类型列表
export function getSalaryItemTypeEligibilities(itemId) {
  return laborRequest({
    url: `/api/v1/salary-item-types/${itemId}/eligibilities`,
    method: 'get'
  })
}

// 创建工资类型与薪资项目的关联关系
export function createSalaryTypeItemEligibility(data) {
  return laborRequest({
    url: '/api/v1/salary-type-item-eligibilities',
    method: 'post',
    data
  })
}

// 删除工资类型与薪资项目的关联关系
export function deleteSalaryTypeItemEligibility(itemId, salaryTypeId) {
  return laborRequest({
    url: `/api/v1/salary-type-item-eligibilities/${itemId}/${salaryTypeId}`,
    method: 'delete'
  })
}

// 批量更新薪资项目类型的关联工资类型
export function batchUpdateSalaryItemTypeEligibilities(itemId, salaryTypeIds) {
  return laborRequest({
    url: `/api/v1/salary-item-types/${itemId}/eligibilities/batch`,
    method: 'put',
    data: {
      salary_type_ids: salaryTypeIds
    }
  })
}

// ==================== 员工薪资模板 API ====================

// 获取员工薪资模板列表
export function getEmployeeSalaryTemplateList(params) {
  return laborRequest({
    url: '/api/v1/employee-salary-templates',
    method: 'get',
    params
  })
}

// 获取员工薪资模板详情 - 使用员工ID
export function getEmployeeSalaryTemplateDetail(employeeId) {
  return laborRequest({
    url: `/api/v1/employee-salary-templates/${employeeId}`,
    method: 'get'
  })
}

// 创建员工薪资模板
export function createEmployeeSalaryTemplate(data) {
  return laborRequest({
    url: '/api/v1/employee-salary-templates',
    method: 'post',
    data
  })
}

// 更新员工薪资模板 - 使用员工ID
export function updateEmployeeSalaryTemplate(employeeId, data) {
  return laborRequest({
    url: `/api/v1/employee-salary-templates/${employeeId}`,
    method: 'put',
    data
  })
}

// 删除员工薪资模板 - 使用员工ID
export function deleteEmployeeSalaryTemplate(employeeId) {
  return laborRequest({
    url: `/api/v1/employee-salary-templates/${employeeId}`,
    method: 'delete'
  })
}

// 根据员工ID获取薪资模板 - 这个API可能重复了，保持原样
export function getEmployeeSalaryTemplateByEmployeeId(employeeId) {
  return laborRequest({
    url: `/api/v1/employee-salary-templates/employee/${employeeId}`,
    method: 'get'
  })
}

// ==================== 薪资计算 API ====================

// 薪资试算
export function calculateSalary(data) {
  return laborRequest({
    url: '/api/v1/salary-calculation',
    method: 'post',
    data
  })
}

// 保存薪资计算结果
export function saveSalaryCalculation(data) {
  return laborRequest({
    url: '/api/v1/salary-calculation/save',
    method: 'post',
    data
  })
}

// 批量计算薪资
export function batchCalculateSalary(data) {
  return laborRequest({
    url: '/api/v1/salary-calculation/batch',
    method: 'post',
    data
  })
}

// ==================== 薪资统计 API ====================

// 获取薪资统计
export function getSalaryStats(params) {
  return laborRequest({
    url: '/api/v1/salary-stats',
    method: 'get',
    params
  })
}

// 获取月度薪资统计
export function getMonthlySalaryStats(year, month) {
  return laborRequest({
    url: `/api/v1/salary-stats/monthly/${year}/${month}`,
    method: 'get'
  })
}

// 获取部门薪资统计
export function getDepartmentSalaryStats(department) {
  return laborRequest({
    url: `/api/v1/salary-stats/department/${department}`,
    method: 'get'
  })
}

// ==================== 薪资导出 API ====================

// 导出薪资数据
export function exportSalaryData(params) {
  return laborRequest({
    url: '/api/v1/salary-export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出工资条
export function exportPayslip(params) {
  return laborRequest({
    url: '/api/v1/salary-export/payslip',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 批量导出工资条
export function batchExportPayslip(data) {
  return laborRequest({
    url: '/api/v1/salary-export/payslip/batch',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
