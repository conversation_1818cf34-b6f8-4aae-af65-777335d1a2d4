import laborRequest from './index'

/**
 * 请假管理 API
 * 注意：这些API目前为占位符，等待后端提供实际接口
 */

// 获取请假申请列表
export function getLeaveList(params) {
  return laborRequest({
    url: '/api/v1/leaves',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  })
}

// 获取请假申请详情
export function getLeaveDetail(id) {
  return laborRequest({
    url: `/api/v1/leaves/${id}`,
    method: 'get'
  })
}

// 创建请假申请
export function createLeave(data) {
  return laborRequest({
    url: '/api/v1/leaves',
    method: 'post',
    data
  })
}

// 更新请假申请
export function updateLeave(id, data) {
  return laborRequest({
    url: `/api/v1/leaves/${id}`,
    method: 'put',
    data
  })
}

// 删除请假申请
export function deleteLeave(id) {
  return laborRequest({
    url: `/api/v1/leaves/${id}`,
    method: 'delete'
  })
}

// 审批请假申请
export function approveLeave(id, data) {
  return laborRequest({
    url: `/api/v1/leaves/${id}/approve`,
    method: 'put',
    data
  })
}

// 拒绝请假申请
export function rejectLeave(id, data) {
  return laborRequest({
    url: `/api/v1/leaves/${id}/reject`,
    method: 'put',
    data
  })
}

// 撤销请假申请
export function cancelLeave(id, data) {
  return laborRequest({
    url: `/api/v1/leaves/${id}/cancel`,
    method: 'put',
    data
  })
}

// 获取请假类型列表
export function getLeaveTypeList() {
  return laborRequest({
    url: '/api/v1/leave-types',
    method: 'get'
  })
}

// 创建请假类型
export function createLeaveType(data) {
  return laborRequest({
    url: '/api/v1/leave-types',
    method: 'post',
    data
  })
}

// 更新请假类型
export function updateLeaveType(id, data) {
  return laborRequest({
    url: `/api/v1/leave-types/${id}`,
    method: 'put',
    data
  })
}

// 删除请假类型
export function deleteLeaveType(id) {
  return laborRequest({
    url: `/api/v1/leave-types/${id}`,
    method: 'delete'
  })
}

// 获取员工请假历史
export function getEmployeeLeaveHistory(employeeId, params) {
  return laborRequest({
    url: `/api/v1/leaves/employee/${employeeId}`,
    method: 'get',
    params
  })
}

// 获取请假统计
export function getLeaveStats(params) {
  return laborRequest({
    url: '/api/v1/leaves/stats',
    method: 'get',
    params
  })
}

// 获取员工请假余额
export function getEmployeeLeaveBalance(employeeId) {
  return laborRequest({
    url: `/api/v1/leaves/balance/${employeeId}`,
    method: 'get'
  })
}

// 批量审批请假申请
export function batchApproveLeaves(data) {
  return laborRequest({
    url: '/api/v1/leaves/batch-approve',
    method: 'post',
    data
  })
}

// 导出请假报表
export function exportLeaveReport(params) {
  return laborRequest({
    url: '/api/v1/leaves/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取请假审批流程
export function getLeaveApprovalProcess(leaveId) {
  return laborRequest({
    url: `/api/v1/leaves/${leaveId}/approval-process`,
    method: 'get'
  })
}

// 获取我的请假申请
export function getMyLeaveApplications(params) {
  return laborRequest({
    url: '/api/v1/leaves/my-applications',
    method: 'get',
    params
  })
}

// 获取待我审批的请假
export function getPendingLeaveApprovals(params) {
  return laborRequest({
    url: '/api/v1/leaves/pending-approvals',
    method: 'get',
    params
  })
}

// 获取请假规则
export function getLeaveRules() {
  return laborRequest({
    url: '/api/v1/leave-rules',
    method: 'get'
  })
}

// 更新请假规则
export function updateLeaveRules(data) {
  return laborRequest({
    url: '/api/v1/leave-rules',
    method: 'put',
    data
  })
}
