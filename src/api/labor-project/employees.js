import laborRequest from './index'

/**
 * 员工管理 API
 */

// 获取员工列表 (V1版本)
export function getEmployeeList(params) {
  return laborRequest({
    url: '/api/v1/employees',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  })
}

// 获取员工列表 (V2版本 - 支持更多筛选参数)
export function getEmployeeListV2(params = {}) {
  return laborRequest({
    url: '/api/v2/employees',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      name: params.name || undefined,
      gender: params.gender || undefined,
      department_id: params.department_id || undefined,
      position_id: params.position_id || undefined,
      status: params.status || undefined,
      salary_type_id: params.salary_type_id || undefined,
      ...params
    }
  })
}

// 获取员工详情 (V1版本)
export function getEmployeeDetail(id) {
  return laborRequest({
    url: `/api/v1/employees/${id}`,
    method: 'get'
  })
}

// 获取员工详情 (V2版本 - 获取更完整的员工信息，推荐用于编辑操作)
export function getEmployeeDetailV2(id) {
  return laborRequest({
    url: `/api/v2/employees/${id}`,
    method: 'get'
  })
}

// 创建员工
export function createEmployee(data) {
  return laborRequest({
    url: '/api/v2/employees',
    method: 'post',
    data
  })
}

// 更新员工信息
export function updateEmployee(id, data) {
  return laborRequest({
    url: `/api/v2/employees/${id}`,
    method: 'put',
    data
  })
}

// 删除员工（软删除）
export function deleteEmployee(id) {
  return laborRequest({
    url: `/api/v1/employees/${id}`,
    method: 'delete'
  })
}

// 批量删除员工
export function batchDeleteEmployees(ids) {
  return laborRequest({
    url: '/api/v1/employees/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 搜索员工
export function searchEmployees(params) {
  return laborRequest({
    url: '/api/v1/employees/search',
    method: 'get',
    params
  })
}

// 导出员工数据
export function exportEmployees(params) {
  return laborRequest({
    url: '/api/v1/employees/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取员工统计信息
export function getEmployeeStats() {
  return laborRequest({
    url: '/api/v1/employees/stats',
    method: 'get'
  })
}

// 根据部门获取员工
export function getEmployeesByDepartment(department) {
  return laborRequest({
    url: '/api/v1/employees',
    method: 'get',
    params: { department }
  })
}

// 获取员工选项（用于下拉选择）
export function getEmployeeOptions() {
  return laborRequest({
    url: '/api/v1/employees/options',
    method: 'get'
  })
}

/**
 * 使用示例：
 * 
 * 1. 获取所有员工（分页）
 * const result = await getEmployeeListV2({ page: 1, limit: 10 })
 * 
 * 2. 按姓名搜索员工
 * const result = await getEmployeeListV2({ name: '张三', page: 1, limit: 10 })
 * 
 * 3. 按部门筛选员工
 * const result = await getEmployeeListV2({ department_id: 12, page: 1, limit: 10 })
 * 
 * 4. 综合筛选
 * const result = await getEmployeeListV2({
 *   page: 1,
 *   limit: 10,
 *   gender: 'male',
 *   department_id: 12,
 *   status: 'active',
 *   salary_type_id: 1
 * })
 * 
 * 响应数据结构：
 * {
 *   "success": true,
 *   "message": "获取员工列表成功",
 *   "data": {
 *     "employees": [...],  // 员工列表数组
 *     "filter": {...},     // 查询过滤条件
 *     "pagination": {...}  // 分页信息
 *   }
 * }
 */
