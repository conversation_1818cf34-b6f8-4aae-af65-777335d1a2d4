import laborRequest from './index'

/**
 * 统计数据 API
 */

// 获取项目统计摘要
export function getProjectSummary() {
  return laborRequest({
    url: '/stats/project-summary',
    method: 'get'
  })
}

// 获取员工统计数据
export function getEmployeeStats(params) {
  return laborRequest({
    url: '/stats/employees',
    method: 'get',
    params
  })
}

// 获取薪资统计数据
export function getSalaryStats(params) {
  return laborRequest({
    url: '/stats/salaries',
    method: 'get',
    params
  })
}

// 获取部门统计数据
export function getDepartmentStats(params) {
  return laborRequest({
    url: '/stats/departments',
    method: 'get',
    params
  })
}

// 获取考勤统计数据
export function getAttendanceStats(params) {
  return laborRequest({
    url: '/stats/attendance',
    method: 'get',
    params
  })
}

// 获取请假统计数据
export function getLeaveStats(params) {
  return laborRequest({
    url: '/stats/leaves',
    method: 'get',
    params
  })
}

// 获取系统总览统计
export function getSystemOverview() {
  return laborRequest({
    url: '/stats/system-overview',
    method: 'get'
  })
}

// 获取月度统计数据
export function getMonthlyStats(year, month) {
  return laborRequest({
    url: `/stats/monthly/${year}/${month}`,
    method: 'get'
  })
}

// 获取年度统计数据
export function getYearlyStats(year) {
  return laborRequest({
    url: `/stats/yearly/${year}`,
    method: 'get'
  })
}

// 获取实时统计数据
export function getRealTimeStats() {
  return laborRequest({
    url: '/stats/realtime',
    method: 'get'
  })
}

// 获取性能统计数据
export function getPerformanceStats(params) {
  return laborRequest({
    url: '/stats/performance',
    method: 'get',
    params
  })
}

// 获取趋势统计数据
export function getTrendStats(params) {
  return laborRequest({
    url: '/stats/trends',
    method: 'get',
    params
  })
}
