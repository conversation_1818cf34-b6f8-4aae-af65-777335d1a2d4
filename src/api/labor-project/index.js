import request from '@/utils/request'

// 劳动项目API基础配置
// 直接使用 request.js 中的 shwethe_labor_project 配置
const LABOR_PROJECT_BASE_URL = 'http://shwethe-pb-ingress-g.shwethe.com/labor-project'
// const LABOR_PROJECT_BASE_URL = 'http://192.168.1.111:15678/labor-project'

// 使用统一的请求函数，指定使用 shwethe_labor_project 服务
const laborRequest = (config) => {
  return request(config, 'shwethe_labor_project')
}

export default laborRequest
export { LABOR_PROJECT_BASE_URL }
