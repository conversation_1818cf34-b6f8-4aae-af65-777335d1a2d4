import { getEmployeeList, createEmployee, getEmployeeDetail } from './employees'

/**
 * 劳动项目API测试文件
 * 用于在浏览器控制台测试API功能
 */

// 测试获取员工列表
export async function testGetEmployeeList() {
  try {
    console.log('🔍 测试获取员工列表...')
    const result = await getEmployeeList({ page: 1, limit: 10 })
    console.log('✅ 获取员工列表成功:', result)
    return result
  } catch (error) {
    console.error('❌ 获取员工列表失败:', error.message)
    throw error
  }
}

// 测试创建员工
export async function testCreateEmployee() {
  try {
    console.log('➕ 测试创建员工...')
    const employeeData = {
      name: '测试员工',
      email: '<EMAIL>',
      phone: '13800138888',
      department: '测试部',
      position: '测试工程师',
      hire_date: '2024-01-15',
      base_salary: 8000,
      salary_type_id: 1
    }
    const result = await createEmployee(employeeData)
    console.log('✅ 创建员工成功:', result)
    return result
  } catch (error) {
    console.error('❌ 创建员工失败:', error.message)
    throw error
  }
}

// 测试获取员工详情
export async function testGetEmployeeDetail(id = 1) {
  try {
    console.log(`🔍 测试获取员工详情 (ID: ${id})...`)
    const result = await getEmployeeDetail(id)
    console.log('✅ 获取员工详情成功:', result)
    return result
  } catch (error) {
    console.error('❌ 获取员工详情失败:', error.message)
    throw error
  }
}

// 运行所有测试
export async function runAllTests() {
  console.log('🚀 开始运行劳动项目API测试...')

  try {
    // 测试获取员工列表
    await testGetEmployeeList()

    // 测试创建员工
    const newEmployee = await testCreateEmployee()

    // 测试获取新创建员工的详情
    if (newEmployee && newEmployee.id) {
      await testGetEmployeeDetail(newEmployee.id)
    }

    console.log('🎉 所有测试完成！')
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message)
  }
}

// 在浏览器控制台中使用：
// import { runAllTests } from '@/api/labor-project/test'
// runAllTests()

// 单独测试：
// import { testGetEmployeeList } from '@/api/labor-project/test'
// testGetEmployeeList()

export default {
  testGetEmployeeList,
  testCreateEmployee,
  testGetEmployeeDetail,
  runAllTests
}
