import laborRequest from './index'

/**
 * 岗位管理 API
 */

// ==================== 岗位管理 API ====================

// 获取岗位列表
export function getPositionList(params) {
  return laborRequest({
    url: '/api/v1/positions',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  })
}

// 获取岗位详情
export function getPositionDetail(id) {
  return laborRequest({
    url: `/api/v1/positions/${id}`,
    method: 'get'
  })
}

// 创建岗位
export function createPosition(data) {
  return laborRequest({
    url: '/api/v1/positions',
    method: 'post',
    data
  })
}

// 更新岗位
export function updatePosition(id, data) {
  return laborRequest({
    url: `/api/v1/positions/${id}`,
    method: 'put',
    data
  })
}

// 删除岗位
export function deletePosition(id) {
  return laborRequest({
    url: `/api/v1/positions/${id}`,
    method: 'delete'
  })
}

// 根据编码获取岗位
export function getPositionByCode(code) {
  return laborRequest({
    url: `/api/v1/positions/code/${code}`,
    method: 'get'
  })
}

// 根据部门获取岗位列表
export function getPositionsByDepartment(departmentId) {
  return laborRequest({
    url: `/api/v1/positions/department/${departmentId}`,
    method: 'get'
  })
}

// 获取岗位统计信息
export function getPositionStats() {
  return laborRequest({
    url: '/api/v1/positions/stats',
    method: 'get'
  })
}

// ==================== 岗位薪资模板 API ====================

// 获取岗位薪资模板列表
export function getPositionSalaryTemplateList(params) {
  return laborRequest({
    url: '/api/v1/position-salary-templates',
    method: 'get',
    params
  })
}

// 获取岗位薪资模板详情
export function getPositionSalaryTemplateDetail(id) {
  return laborRequest({
    url: `/api/v1/position-salary-templates/${id}`,
    method: 'get'
  })
}

// 创建岗位薪资模板
export function createPositionSalaryTemplate(data) {
  return laborRequest({
    url: '/api/v1/position-salary-templates',
    method: 'post',
    data
  })
}

// 更新岗位薪资模板
export function updatePositionSalaryTemplate(id, data) {
  return laborRequest({
    url: `/api/v1/position-salary-templates/${id}`,
    method: 'put',
    data
  })
}

// 删除岗位薪资模板
export function deletePositionSalaryTemplate(id) {
  return laborRequest({
    url: `/api/v1/position-salary-templates/${id}`,
    method: 'delete'
  })
}

// 批量创建岗位薪资模板
export function batchCreatePositionSalaryTemplates(positionId, data) {
  return laborRequest({
    url: `/api/v1/positions/${positionId}/salary-templates`,
    method: 'post',
    data
  })
}

// 获取岗位选项列表（用于选择器）
export function getPositionOptions() {
  return laborRequest({
    url: '/api/v1/positions',
    method: 'get',
    params: {
      limit: 100 // 获取所有岗位
    }
  })
}
