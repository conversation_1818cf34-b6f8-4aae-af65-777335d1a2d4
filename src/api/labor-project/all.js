/**
 * 劳动管理系统 API 统一导出
 * 使用方式：
 * import { getEmployeeList, createEmployee } from '@/api/labor-project/all'
 * 或者
 * import * as LaborAPI from '@/api/labor-project/all'
 */

// 基础配置
export { default as laborRequest, LABOR_PROJECT_BASE_URL } from './index'

// 员工管理 API
export {
  getEmployeeList,
  getEmployeeDetail,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  batchDeleteEmployees,
  searchEmployees,
  exportEmployees,
  getEmployeeStats,
  getEmployeesByDepartment,
  getEmployeeOptions
} from './employees'

// 岗位管理 API
export {
  getPositionList,
  getPositionDetail,
  createPosition,
  updatePosition,
  deletePosition,
  getPositionByCode,
  getPositionsByDepartment,
  getPositionStats,
  getPositionSalaryTemplateList,
  getPositionSalaryTemplateDetail,
  createPositionSalaryTemplate,
  updatePositionSalaryTemplate,
  deletePositionSalaryTemplate,
  batchCreatePositionSalaryTemplates,
  getPositionOptions
} from './positions'

// 薪资管理 API
export {
  // 薪资汇总
  getSalarySummaryList,
  getSalarySummaryDetail,
  createSalarySummary,
  updateSalarySummary,
  deleteSalarySummary,
  getSalaryBreakdown,

  // 工资类型
  getSalaryTypeList,
  getSalaryTypeDetail,
  createSalaryType,
  updateSalaryType,
  deleteSalaryType,
  getSalaryTypeEligibleItems,
  updateSalaryTypeItemEligibility,

  // 工资项目类型
  getSalaryItemTypeList,
  getSalaryItemTypeDetail,
  createSalaryItemType,
  updateSalaryItemType,
  deleteSalaryItemType,

  // 工资类型与薪资项目关联关系
  getSalaryItemTypeEligibilities,
  createSalaryTypeItemEligibility,
  deleteSalaryTypeItemEligibility,
  batchUpdateSalaryItemTypeEligibilities,

  // 员工薪资模板
  getEmployeeSalaryTemplateList,
  getEmployeeSalaryTemplateDetail,
  createEmployeeSalaryTemplate,
  updateEmployeeSalaryTemplate,
  deleteEmployeeSalaryTemplate,
  getEmployeeSalaryTemplateByEmployeeId,

  // 薪资计算
  calculateSalary,
  saveSalaryCalculation,
  batchCalculateSalary,

  // 薪资统计
  getSalaryStats,
  getMonthlySalaryStats,
  getDepartmentSalaryStats,

  // 薪资导出
  exportSalaryData,
  exportPayslip,
  batchExportPayslip
} from './salaries'

// 考勤管理 API
export {
  getAttendanceList,
  getAttendanceDetail,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  clockIn,
  clockOut,
  getAttendanceStats,
  getEmployeeAttendanceHistory,
  importAttendanceRecords,
  exportAttendanceReport,
  getAttendanceRules,
  updateAttendanceRules,
  getAbnormalAttendance,
  handleAbnormalAttendance
} from './attendance'

// 请假管理 API
export {
  getLeaveList,
  getLeaveDetail,
  createLeave,
  updateLeave,
  deleteLeave,
  approveLeave,
  rejectLeave,
  cancelLeave,
  getLeaveTypeList,
  createLeaveType,
  updateLeaveType,
  deleteLeaveType,
  getEmployeeLeaveHistory,
  getLeaveStats,
  getEmployeeLeaveBalance,
  batchApproveLeaves,
  exportLeaveReport,
  getLeaveApprovalProcess,
  getMyLeaveApplications,
  getPendingLeaveApprovals,
  getLeaveRules,
  updateLeaveRules
} from './leave'

// 部门管理 API
export {
  getDepartmentTree,
  getDepartmentList,
  getDepartmentDetail,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentEmployees,
  moveDepartment,
  getDepartmentStats,
  getAllDepartmentStats,
  getDepartmentOptions,
  batchDeleteDepartments,
  toggleDepartmentStatus,
  getDepartmentPath,
  searchDepartments,
  exportDepartmentStructure,
  importDepartmentStructure,
  getDepartmentManagers,
  setDepartmentManager,
  getSubDepartments,
  validateDepartmentCode
} from './departments'

// 统计数据 API
export {
  getProjectSummary,
  getEmployeeStats as getStatsEmployees,
  getSalaryStats as getStatsSalaries,
  getDepartmentStats as getStatsDepartments,
  getAttendanceStats as getStatsAttendance,
  getLeaveStats as getStatsLeaves,
  getSystemOverview,
  getMonthlyStats,
  getYearlyStats,
  getRealTimeStats,
  getPerformanceStats,
  getTrendStats
} from './stats'

// API 分组对象，方便按模块使用
export const EmployeeAPI = {
  getEmployeeList,
  getEmployeeDetail,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  batchDeleteEmployees,
  searchEmployees,
  exportEmployees,
  getEmployeeStats,
  getEmployeesByDepartment,
  getEmployeeOptions
}

export const PositionAPI = {
  getPositionList,
  getPositionDetail,
  createPosition,
  updatePosition,
  deletePosition,
  getPositionByCode,
  getPositionsByDepartment,
  getPositionStats,
  getPositionSalaryTemplateList,
  getPositionSalaryTemplateDetail,
  createPositionSalaryTemplate,
  updatePositionSalaryTemplate,
  deletePositionSalaryTemplate,
  batchCreatePositionSalaryTemplates,
  getPositionOptions
}

export const SalaryAPI = {
  getSalarySummaryList,
  getSalarySummaryDetail,
  createSalarySummary,
  updateSalarySummary,
  deleteSalarySummary,
  getSalaryBreakdown,
  getSalaryTypeList,
  getSalaryTypeDetail,
  createSalaryType,
  updateSalaryType,
  deleteSalaryType,
  getSalaryTypeEligibleItems,
  updateSalaryTypeItemEligibility,
  getSalaryItemTypeList,
  getSalaryItemTypeDetail,
  createSalaryItemType,
  updateSalaryItemType,
  deleteSalaryItemType,
  getSalaryItemTypeEligibilities,
  createSalaryTypeItemEligibility,
  deleteSalaryTypeItemEligibility,
  batchUpdateSalaryItemTypeEligibilities,
  getEmployeeSalaryTemplateList,
  getEmployeeSalaryTemplateDetail,
  createEmployeeSalaryTemplate,
  updateEmployeeSalaryTemplate,
  deleteEmployeeSalaryTemplate,
  getEmployeeSalaryTemplateByEmployeeId,
  calculateSalary,
  saveSalaryCalculation,
  batchCalculateSalary,
  getSalaryStats,
  getMonthlySalaryStats,
  getDepartmentSalaryStats,
  exportSalaryData,
  exportPayslip,
  batchExportPayslip
}

export const AttendanceAPI = {
  getAttendanceList,
  getAttendanceDetail,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  clockIn,
  clockOut,
  getAttendanceStats,
  getEmployeeAttendanceHistory,
  importAttendanceRecords,
  exportAttendanceReport,
  getAttendanceRules,
  updateAttendanceRules,
  getAbnormalAttendance,
  handleAbnormalAttendance
}

export const LeaveAPI = {
  getLeaveList,
  getLeaveDetail,
  createLeave,
  updateLeave,
  deleteLeave,
  approveLeave,
  rejectLeave,
  cancelLeave,
  getLeaveTypeList,
  createLeaveType,
  updateLeaveType,
  deleteLeaveType,
  getEmployeeLeaveHistory,
  getLeaveStats,
  getEmployeeLeaveBalance,
  batchApproveLeaves,
  exportLeaveReport,
  getLeaveApprovalProcess,
  getMyLeaveApplications,
  getPendingLeaveApprovals,
  getLeaveRules,
  updateLeaveRules
}

export const DepartmentAPI = {
  getDepartmentTree,
  getDepartmentList,
  getDepartmentDetail,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentEmployees,
  moveDepartment,
  getDepartmentStats,
  getAllDepartmentStats,
  getDepartmentOptions,
  batchDeleteDepartments,
  toggleDepartmentStatus,
  getDepartmentPath,
  searchDepartments,
  exportDepartmentStructure,
  importDepartmentStructure,
  getDepartmentManagers,
  setDepartmentManager,
  getSubDepartments,
  validateDepartmentCode
}

export const StatsAPI = {
  getProjectSummary,
  getEmployeeStats,
  getSalaryStats,
  getDepartmentStats,
  getAttendanceStats,
  getLeaveStats,
  getSystemOverview,
  getMonthlyStats,
  getYearlyStats,
  getRealTimeStats,
  getPerformanceStats,
  getTrendStats
}
