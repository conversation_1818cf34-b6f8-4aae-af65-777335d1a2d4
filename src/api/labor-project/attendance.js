import laborRequest from './index'

/**
 * 考勤管理 API
 * 注意：这些API目前为占位符，等待后端提供实际接口
 */

// 获取考勤记录列表
export function getAttendanceList(params) {
  return laborRequest({
    url: '/api/v1/attendance',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  })
}

// 获取考勤记录详情
export function getAttendanceDetail(id) {
  return laborRequest({
    url: `/api/v1/attendance/${id}`,
    method: 'get'
  })
}

// 创建考勤记录
export function createAttendance(data) {
  return laborRequest({
    url: '/api/v1/attendance',
    method: 'post',
    data
  })
}

// 更新考勤记录
export function updateAttendance(id, data) {
  return laborRequest({
    url: `/api/v1/attendance/${id}`,
    method: 'put',
    data
  })
}

// 删除考勤记录
export function deleteAttendance(id) {
  return laborRequest({
    url: `/api/v1/attendance/${id}`,
    method: 'delete'
  })
}

// 员工打卡
export function clockIn(data) {
  return laborRequest({
    url: '/api/v1/attendance/clock-in',
    method: 'post',
    data
  })
}

// 员工打卡下班
export function clockOut(data) {
  return laborRequest({
    url: '/api/v1/attendance/clock-out',
    method: 'post',
    data
  })
}

// 获取考勤统计
export function getAttendanceStats(params) {
  return laborRequest({
    url: '/api/v1/attendance/stats',
    method: 'get',
    params
  })
}

// 获取员工考勤历史
export function getEmployeeAttendanceHistory(employeeId, params) {
  return laborRequest({
    url: `/api/v1/attendance/employee/${employeeId}`,
    method: 'get',
    params
  })
}

// 批量导入考勤记录
export function importAttendanceRecords(data) {
  return laborRequest({
    url: '/api/v1/attendance/import',
    method: 'post',
    data
  })
}

// 导出考勤报表
export function exportAttendanceReport(params) {
  return laborRequest({
    url: '/api/v1/attendance/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取考勤规则
export function getAttendanceRules() {
  return laborRequest({
    url: '/api/v1/attendance/rules',
    method: 'get'
  })
}

// 更新考勤规则
export function updateAttendanceRules(data) {
  return laborRequest({
    url: '/api/v1/attendance/rules',
    method: 'put',
    data
  })
}

// 获取异常考勤记录
export function getAbnormalAttendance(params) {
  return laborRequest({
    url: '/api/v1/attendance/abnormal',
    method: 'get',
    params
  })
}

// 处理异常考勤
export function handleAbnormalAttendance(id, data) {
  return laborRequest({
    url: `/api/v1/attendance/abnormal/${id}`,
    method: 'put',
    data
  })
}
