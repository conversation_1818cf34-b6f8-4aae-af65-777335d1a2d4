# 劳动管理系统 API 文档

## 📁 目录结构

```
src/api/labor-project/
├── index.js          # API基础配置和请求实例
├── employees.js      # 员工管理API
├── salaries.js       # 薪资管理API
├── attendance.js     # 考勤管理API
├── leave.js          # 请假管理API
├── departments.js    # 部门管理API
├── stats.js          # 统计数据API
├── all.js            # 统一导出文件
└── README.md         # 本文档
```

## 🔧 基础配置

### API 基础URL
```javascript
const LABOR_PROJECT_BASE_URL = 'http://localhost:15678/labor-project'
```

### 请求实例
所有API都使用统一的请求实例 `laborRequest`，已配置好：
- 基础URL
- 超时设置（10秒）
- 请求/响应拦截器
- 错误处理

## 🚀 使用方式

### 方式1: 单独导入
```javascript
import { getEmployeeList, createEmployee } from '@/api/labor-project/employees'
import { getSalaryStats } from '@/api/labor-project/salaries'

// 使用
const employees = await getEmployeeList({ page: 1, limit: 10 })
const newEmployee = await createEmployee(employeeData)
```

### 方式2: 统一导入
```javascript
import { getEmployeeList, createEmployee, getSalaryStats } from '@/api/labor-project/all'

// 使用
const employees = await getEmployeeList({ page: 1, limit: 10 })
```

### 方式3: 分组导入
```javascript
import { EmployeeAPI, SalaryAPI } from '@/api/labor-project/all'

// 使用
const employees = await EmployeeAPI.getEmployeeList({ page: 1, limit: 10 })
const salaryStats = await SalaryAPI.getSalaryStats()
```

### 方式4: 全量导入
```javascript
import * as LaborAPI from '@/api/labor-project/all'

// 使用
const employees = await LaborAPI.getEmployeeList({ page: 1, limit: 10 })
```

## 📊 API 状态

### ✅ 已对接后端API并完成前端整合
- **员工管理** - 完全对接，前端页面已整合
- **薪资管理** - 完全对接
- **统计数据** - 部分对接

### 🚧 等待后端API
- **考勤管理** - 占位符API，等待后端提供
- **请假管理** - 占位符API，等待后端提供
- **部门管理** - 占位符API，等待后端提供

## 🔄 最新更新 (2024-01-15)

### API配置更新
- 更新API基础URL为本地环境: `http://localhost:15678/labor-project`
- 修正API路径为: `/api/v1/employees`
- 更新响应拦截器处理后端响应格式: `{ success: true, message: "...", data: {...} }`

### 前端整合完成
- ✅ 员工管理页面(`/labor-project/employee`)已完全整合真实API
- ✅ 支持员工的增删改查、搜索、筛选等功能
- ✅ 完善错误处理和字段映射
- ✅ 添加API测试工具(`test.js`)

### 测试说明
确保后端服务运行在 `http://localhost:15678`，然后：
1. 访问前端员工管理页面
2. 尝试创建、编辑、删除员工
3. 使用搜索和筛选功能
4. 查看浏览器控制台确认API调用成功

## 📋 API 分类

### 1. 员工管理 (employees.js)
```javascript
// 基础CRUD
getEmployeeList(params)           // 获取员工列表
getEmployeeDetail(id)             // 获取员工详情
createEmployee(data)              // 创建员工
updateEmployee(id, data)          // 更新员工
deleteEmployee(id)                // 删除员工

// 扩展功能
batchDeleteEmployees(ids)         // 批量删除
searchEmployees(params)           // 搜索员工
exportEmployees(params)           // 导出员工
getEmployeeStats()                // 员工统计
getEmployeesByDepartment(dept)    // 按部门获取
getEmployeeOptions()              // 获取选项
```

### 2. 岗位管理 (positions.js)
```javascript
// 基础CRUD
getPositionList(params)           // 获取岗位列表
getPositionDetail(id)             // 获取岗位详情
createPosition(data)              // 创建岗位
updatePosition(id, data)          // 更新岗位
deletePosition(id)                // 删除岗位

// 扩展功能
getPositionByCode(code)           // 根据编码获取岗位
getPositionsByDepartment(deptId)  // 根据部门获取岗位
getPositionStats()                // 岗位统计
getPositionOptions()              // 获取岗位选项

// 岗位薪资模板
getPositionSalaryTemplateList(params)      // 获取岗位薪资模板列表
getPositionSalaryTemplateDetail(id)        // 获取岗位薪资模板详情
createPositionSalaryTemplate(data)         // 创建岗位薪资模板
updatePositionSalaryTemplate(id, data)     // 更新岗位薪资模板
deletePositionSalaryTemplate(id)           // 删除岗位薪资模板
batchCreatePositionSalaryTemplates(positionId, data)  // 批量创建岗位薪资模板
```

### 3. 薪资管理 (salaries.js)
```javascript
// 薪资汇总
getSalarySummaryList(params)      // 薪资汇总列表
createSalarySummary(data)         // 创建薪资汇总
getSalaryBreakdown(id)            // 薪资分解详情

// 工资类型
getSalaryTypeList(params)         // 工资类型列表
createSalaryType(data)            // 创建工资类型
getSalaryTypeEligibleItems(id)    // 可用工资项目

// 薪资计算
calculateSalary(data)             // 薪资试算
saveSalaryCalculation(data)       // 保存计算结果
batchCalculateSalary(data)        // 批量计算

// 薪资统计
getSalaryStats(params)            // 薪资统计
getMonthlySalaryStats(year, month)// 月度统计
getDepartmentSalaryStats(dept)    // 部门统计

// 薪资导出
exportSalaryData(params)          // 导出薪资数据
exportPayslip(params)             // 导出工资条
batchExportPayslip(data)          // 批量导出工资条
```

### 4. 考勤管理 (attendance.js)
```javascript
// 基础功能
getAttendanceList(params)         // 考勤记录列表
createAttendance(data)            // 创建考勤记录
clockIn(data)                     // 上班打卡
clockOut(data)                    // 下班打卡

// 统计和报表
getAttendanceStats(params)        // 考勤统计
getEmployeeAttendanceHistory(id)  // 员工考勤历史
exportAttendanceReport(params)    // 导出考勤报表

// 规则管理
getAttendanceRules()              // 获取考勤规则
updateAttendanceRules(data)       // 更新考勤规则
getAbnormalAttendance(params)     // 异常考勤
```

### 5. 请假管理 (leave.js)
```javascript
// 基础功能
getLeaveList(params)              // 请假申请列表
createLeave(data)                 // 创建请假申请
approveLeave(id, data)            // 审批请假
rejectLeave(id, data)             // 拒绝请假

// 请假类型
getLeaveTypeList()                // 请假类型列表
createLeaveType(data)             // 创建请假类型

// 统计和查询
getLeaveStats(params)             // 请假统计
getEmployeeLeaveBalance(id)       // 员工请假余额
getMyLeaveApplications(params)    // 我的请假申请
getPendingLeaveApprovals(params)  // 待审批请假
```

### 6. 部门管理 (departments.js)
```javascript
// 基础功能
getDepartmentTree(params)         // 部门树结构
getDepartmentList(params)         // 部门列表
createDepartment(data)            // 创建部门
updateDepartment(id, data)        // 更新部门

// 层级管理
moveDepartment(id, data)          // 移动部门
getSubDepartments(parentId)       // 获取子部门
getDepartmentPath(id)             // 获取部门路径

// 统计和查询
getDepartmentStats(id)            // 部门统计
getAllDepartmentStats()           // 所有部门统计
getDepartmentEmployees(id)        // 部门员工
getDepartmentManagers(id)         // 部门负责人
```

### 7. 统计数据 (stats.js)
```javascript
// 项目统计
getProjectSummary()               // 项目统计摘要
getSystemOverview()               // 系统总览

// 时间维度统计
getMonthlyStats(year, month)      // 月度统计
getYearlyStats(year)              // 年度统计
getRealTimeStats()                // 实时统计

// 业务统计
getEmployeeStats(params)          // 员工统计
getSalaryStats(params)            // 薪资统计
getAttendanceStats(params)        // 考勤统计
getLeaveStats(params)             // 请假统计
getDepartmentStats(params)        // 部门统计
```

## 🎯 使用示例

### 获取员工列表
```javascript
import { getEmployeeList } from '@/api/labor-project/employees'

async function fetchEmployees() {
  try {
    const result = await getEmployeeList({
      page: 1,
      limit: 10,
      department: '技术部',
      status: 'active'
    })
    console.log('员工列表:', result)
  } catch (error) {
    console.error('获取员工列表失败:', error)
  }
}
```

### 创建员工
```javascript
import { createEmployee } from '@/api/labor-project/employees'

async function addEmployee() {
  try {
    const newEmployee = await createEmployee({
      name: '张三',
      email: '<EMAIL>',
      department: '技术部',
      position: '前端开发',
      base_salary: 10000,
      hire_date: '2024-01-15'
    })
    console.log('创建成功:', newEmployee)
  } catch (error) {
    console.error('创建员工失败:', error)
  }
}
```

### 薪资计算
```javascript
import { calculateSalary } from '@/api/labor-project/salaries'

async function calculateEmployeeSalary() {
  try {
    const calculation = await calculateSalary({
      employee_id: 1,
      salary_month: '2024-01',
      use_template: true,
      item_values: {
        base_salary: 10000,
        performance_bonus: 2000,
        overtime_pay: 500
      }
    })
    console.log('薪资计算结果:', calculation)
  } catch (error) {
    console.error('薪资计算失败:', error)
  }
}
```

## 🔑 认证和权限

API调用会自动处理认证信息，您只需要确保用户已登录即可。如需自定义认证逻辑，请修改 `src/api/labor-project/index.js` 中的请求拦截器。

## 🐛 错误处理

所有API调用都会返回Promise，建议使用try-catch进行错误处理：

```javascript
try {
  const result = await getEmployeeList(params)
  // 处理成功结果
} catch (error) {
  // 处理错误
  console.error('API调用失败:', error)
  // 显示错误消息给用户
}
```

## 📝 注意事项

1. **API状态**: 部分API为占位符，等待后端实现
2. **参数验证**: 请确保传入正确的参数格式
3. **错误处理**: 始终处理API调用可能的错误
4. **性能优化**: 大量数据请求时注意分页和缓存
5. **类型安全**: 建议使用TypeScript增强类型安全

## 🔄 后续计划

- [ ] 完善考勤管理API
- [ ] 完善请假管理API
- [ ] 完善部门管理API
- [ ] 添加TypeScript类型定义
- [ ] 添加API缓存机制
- [ ] 添加离线支持 