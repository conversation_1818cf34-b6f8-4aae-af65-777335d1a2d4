import laborRequest from './index'

/**
 * 部门管理 API
 * 基于 Swagger 文档 http://47.236.155.72:15678/labor-project/swagger/index.html
 */

// 获取部门列表
export function getDepartmentList(params) {
  return laborRequest({
    url: '/api/v1/departments',
    method: 'get',
    params
  })
}

// 创建部门
export function createDepartment(data) {
  return laborRequest({
    url: '/api/v1/departments',
    method: 'post',
    data
  })
}

// 根据部门编码获取部门信息
export function getDepartmentByCode(deptCode) {
  return laborRequest({
    url: `/api/v1/departments/code/${deptCode}`,
    method: 'get'
  })
}

// 获取根部门
export function getRootDepartment() {
  return laborRequest({
    url: '/api/v1/departments/root',
    method: 'get'
  })
}

// 获取部门树形结构
export function getDepartmentTree(params) {
  return laborRequest({
    url: '/api/v1/departments/tree',
    method: 'get',
    params
  })
}

// 获取部门详情
export function getDepartmentDetail(id) {
  return laborRequest({
    url: `/api/v1/departments/${id}`,
    method: 'get'
  })
}

// 更新部门信息
export function updateDepartment(id, data) {
  return laborRequest({
    url: `/api/v1/departments/${id}`,
    method: 'put',
    data
  })
}

// 删除部门
export function deleteDepartment(id) {
  return laborRequest({
    url: `/api/v1/departments/${id}`,
    method: 'delete'
  })
}

// 获取部门下的员工
export function getDepartmentEmployees(departmentId, params) {
  return laborRequest({
    url: `/api/v1/departments/${departmentId}/employees`,
    method: 'get',
    params
  })
}

// 获取部门统计信息
export function getDepartmentStats(departmentId) {
  return laborRequest({
    url: `/api/v1/departments/${departmentId}/stats`,
    method: 'get'
  })
}

// 批量删除部门
export function batchDeleteDepartments(ids) {
  return laborRequest({
    url: '/api/v1/departments/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 启用/禁用部门
export function toggleDepartmentStatus(id, status) {
  return laborRequest({
    url: `/api/v1/departments/${id}/toggle-status`,
    method: 'put',
    data: { status }
  })
}

// 获取部门层级路径
export function getDepartmentPath(id) {
  return laborRequest({
    url: `/api/v1/departments/${id}/path`,
    method: 'get'
  })
}

// 搜索部门
export function searchDepartments(params) {
  return laborRequest({
    url: '/api/v1/departments/search',
    method: 'get',
    params
  })
}

// 导出部门结构
export function exportDepartmentStructure() {
  return laborRequest({
    url: '/api/v1/departments/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入部门结构
export function importDepartmentStructure(data) {
  return laborRequest({
    url: '/api/v1/departments/import',
    method: 'post',
    data
  })
}

// 获取部门选项（用于下拉选择）
export function getDepartmentOptions() {
  return laborRequest({
    url: '/api/v1/departments/options',
    method: 'get'
  })
}

// 验证部门编码是否唯一
export function validateDepartmentCode(code, excludeId) {
  return laborRequest({
    url: '/api/v1/departments/validate-code',
    method: 'post',
    data: { code, excludeId }
  })
}

// 移动部门（调整部门层级）
export function moveDepartment(id, data) {
  return laborRequest({
    url: `/api/v1/departments/${id}/move`,
    method: 'put',
    data
  })
}

// 获取部门负责人
export function getDepartmentManagers(departmentId) {
  return laborRequest({
    url: `/api/v1/departments/${departmentId}/managers`,
    method: 'get'
  })
}

// 设置部门负责人
export function setDepartmentManager(departmentId, data) {
  return laborRequest({
    url: `/api/v1/departments/${departmentId}/managers`,
    method: 'put',
    data
  })
}

// 获取子部门
export function getSubDepartments(parentId) {
  return laborRequest({
    url: `/api/v1/departments/${parentId}/children`,
    method: 'get'
  })
}

// 获取所有部门统计
export function getAllDepartmentStats() {
  return laborRequest({
    url: '/api/v1/departments/stats/all',
    method: 'get'
  })
}
