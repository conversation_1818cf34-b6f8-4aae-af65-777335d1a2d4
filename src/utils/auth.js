import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const RefreshTokenKey = 'Admin-Refresh-Token'

export function getToken() {
  // 首先尝试从Cookie获取
  const cookieToken = Cookies.get(TokenKey)
  if (cookieToken) {
    return cookieToken
  }

  // 如果Cookie中没有，尝试从localStorage获取
  return localStorage.getItem('auth-token')
}

export function setToken(token) {
  // 同时设置到Cookie和localStorage
  localStorage.setItem('auth-token', token)
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  // 同时从Cookie和localStorage中移除
  localStorage.removeItem('auth-token')
  return Cookies.remove(TokenKey)
}

// Refresh Token相关方法
export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey) || localStorage.getItem('refresh-token')
}

export function setRefreshToken(token) {
  localStorage.setItem('refresh-token', token)
  return Cookies.set(RefreshTokenKey, token)
}

export function removeRefreshToken() {
  localStorage.removeItem('refresh-token')
  return Cookies.remove(RefreshTokenKey)
}
