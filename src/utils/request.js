import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 定义不同服务的基础URL
const SERVICE_BASE_URLS = {
  // main: 'http://47.236.155.72:8083/auth',
  main: 'http://shwethe-pb-ingress-g.shwethe.com/auth',
  user: 'http://47.236.155.72:8084/user-service',
  product: 'http://47.236.155.72:8085/product-service',
  order: 'http://47.236.155.72:8086/order-service',
  payment: 'http://47.236.155.72:8087/payment-service',
  shwethe_data: 'http://shwethe-pb-ingress-g.shwethe.com/shwethe_data',
  shwethe_data_n: 'http://shwethe-pb-ingress-g.shwethe.com/shwethe_data_n',
  // shwethe_guiling: 'http://47.236.155.72:8092/shwethe_guiling',
  shwethe_guiling: 'http://shwethe-pb-ingress-g.shwethe.com/shwethe_guiling',
  shwethe_labor_project: 'http://shwethe-pb-ingress-g.shwethe.com/labor-project',
  // shwethe_labor_project: 'http://192.168.1.111:15678/labor-project',
  shwethe_drive_ops: 'http://pv-api.shwethe.com/drive_ops'
  // shwethe_data_n: 'http://47.236.155.72:8089/shwethe_data_n'
}

// 创建不同服务的axios实例
const createServiceRequest = (baseURL) => {
  const service = axios.create({
    baseURL,
    withCredentials: false,
    timeout: 5000
  })

  // 请求拦截器
  service.interceptors.request.use(
    config => {
      if (store.getters.token) {
        // 为所有请求添加token
        config.headers['Authorization'] = 'Bearer ' + getToken()
      }
      return config
    },
    error => {
      console.log(error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  service.interceptors.response.use(
    response => {
      const res = response.data
      console.log(res)

      // Determine success dynamically based on response fields
      let isSuccess = true
      if (Object.prototype.hasOwnProperty.call(res, 'code')) {
        isSuccess = [0, 200, 20000].includes(res.code)
      } else if (Object.prototype.hasOwnProperty.call(res, 'success')) {
        isSuccess = res.success === true || res.success === 1
      }

      if (!isSuccess) {
        Message({
          message: res.message || 'Error',
          type: 'error',
          duration: 5 * 1000
        })

        if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
          // 处理token过期
          const config = response.config

          if (!isRefreshing) {
            isRefreshing = true

            return store.dispatch('user/refreshToken')
              .then(token => {
                // 更新已过期的token
                config.headers['X-Token'] = token

                // 已经刷新了token，将所有队列中的请求进行重试
                requests.forEach(cb => cb(token))
                requests = []

                // 重试当前请求
                return service(config)
              })
              .catch(err => {
                console.error('刷新token失败:', err)
                // to re-login
                MessageBox.confirm('您已被登出，您可以取消继续留在该页面，或者重新登录', '确定登出', {
                  confirmButtonText: '重新登录',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  store.dispatch('user/resetToken').then(() => {
                    location.reload()
                  })
                })
                return Promise.reject(err)
              })
              .finally(() => {
                isRefreshing = false
              })
          } else {
            // 返回一个promise，等token刷新完成后再重试
            return new Promise((resolve) => {
              // 将resolve放入队列，用一个函数包装一下，等token刷新后直接执行
              requests.push((token) => {
                config.headers['X-Token'] = token
                resolve(service(config))
              })
            })
          }
        }
        return Promise.reject(new Error(res.message || 'Error'))
      } else {
        return res
      }
    },
    error => {
      // 处理401未授权错误，通常意味着token过期
      if (error.response && error.response.status === 401) {
        const config = error.config

        if (!isRefreshing) {
          isRefreshing = true

          return store.dispatch('user/refreshToken')
            .then(token => {
              // 更新已过期的token
              config.headers['X-Token'] = token

              // 已经刷新了token，将所有队列中的请求进行重试
              requests.forEach(cb => cb(token))
              requests = []

              // 重试当前请求
              return service(config)
            })
            .catch(err => {
              console.error('刷新token失败:', err)
              // to re-login
              MessageBox.confirm('您已被登出，您可以取消继续留在该页面，或者重新登录', '确定登出', {
                confirmButtonText: '重新登录',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              })
              return Promise.reject(err)
            })
            .finally(() => {
              isRefreshing = false
            })
        } else {
          // 返回一个promise，等token刷新完成后再重试
          return new Promise((resolve) => {
            // 将resolve放入队列，用一个函数包装一下，等token刷新后直接执行
            requests.push((token) => {
              config.headers['X-Token'] = token
              resolve(service(config))
            })
          })
        }
      }

      console.log('err' + error)
      Message({
        message: error.message,
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(error)
    }
  )

  return service
}

// 创建各个服务的请求实例
const serviceRequests = {}
Object.keys(SERVICE_BASE_URLS).forEach(serviceName => {
  serviceRequests[serviceName] = createServiceRequest(SERVICE_BASE_URLS[serviceName])
})

// 默认使用主服务
const defaultService = serviceRequests.main

// 请求函数，允许指定服务
const request = (config, serviceName = 'main') => {
  if (serviceRequests[serviceName]) {
    return serviceRequests[serviceName](config)
  } else {
    console.warn(`Service "${serviceName}" not found, using main service`)
    return defaultService(config)
  }
}

export default request
// 导出各个服务的基础URL，方便直接引用
export const serviceBaseUrls = SERVICE_BASE_URLS
