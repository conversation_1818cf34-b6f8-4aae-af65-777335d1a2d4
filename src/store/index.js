import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import customer from './modules/shwethe-n/customer'
import shwetheCompany from './modules/shwethe-company/shwetheCompany'

Vue.use(Vuex)

// 创建一个排除列表，包含我们已经手动导入的模块
const excludeModules = [
  './shwethe-company/shwetheCompany.js',
  './shwethe-n/customer.js'
]

// 自动导入其他模块，但排除已手动导入的
const modulesFiles = require.context('./modules', true, /\.js$/)
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // 检查是否在排除列表中
  if (excludeModules.some(path => modulePath.includes(path))) {
    return modules
  }

  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = new Vuex.Store({
  modules: {
    ...modules,
    customer,
    shwetheCompany
  },
  getters
})

export default store
