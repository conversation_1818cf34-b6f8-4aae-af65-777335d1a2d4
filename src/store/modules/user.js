import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken, getRefreshToken, setRefreshToken, removeRefreshToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  refreshToken: getRefreshToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: []
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_REFRESH_TOKEN: (state, refreshToken) => {
    state.refreshToken = refreshToken
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_FENDIAN_ID: (state, fendianId) => {
    state.fendianId = fendianId
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    // 检查是否直接传入token（GraphQL登录）
    if (userInfo.token) {
      console.log('使用GraphQL登录方式，直接设置token:', userInfo.token)
      commit('SET_TOKEN', userInfo.token)
      setToken(userInfo.token)

      // 处理refresh token
      if (userInfo.refreshToken) {
        commit('SET_REFRESH_TOKEN', userInfo.refreshToken)
        setRefreshToken(userInfo.refreshToken)
      }

      return Promise.resolve()
    }

    // 使用原有API登录方式
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        const { data } = response
        console.log('Login response:', data)
        const token = data.token || data
        commit('SET_TOKEN', token)
        setToken(token)

        // 处理refresh token如果有的话
        if (data.refreshToken) {
          commit('SET_REFRESH_TOKEN', data.refreshToken)
          setRefreshToken(data.refreshToken)
        }

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 刷新token
  refreshToken({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 这里需要创建一个刷新token的API请求
      const refreshToken = state.refreshToken

      if (!refreshToken) {
        reject(new Error('No refresh token'))
        return
      }

      // 构建GraphQL mutation
      import('@/apolloClient').then(({ auth_url_client }) => {
        import('graphql-tag').then(({ default: gql }) => {
          const REFRESH_TOKEN_MUTATION = gql`
            mutation {
              refreshToken(refreshToken: "${refreshToken}") {
                success
                message
                token
                refreshToken
                tokenType
                expiresIn
              }
            }
          `

          auth_url_client.mutate({
            mutation: REFRESH_TOKEN_MUTATION
          }).then(response => {
            const refreshData = response.data.refreshToken

            if (refreshData.success) {
              commit('SET_TOKEN', refreshData.token)
              setToken(refreshData.token)

              if (refreshData.refreshToken) {
                commit('SET_REFRESH_TOKEN', refreshData.refreshToken)
                setRefreshToken(refreshData.refreshToken)
              }

              resolve(refreshData.token)
            } else {
              reject(new Error(refreshData.message || 'Failed to refresh token'))
            }
          }).catch(error => {
            console.error('刷新token失败:', error)
            reject(error)
          })
        }).catch(error => {
          reject(error)
        })
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        const { data } = response
        console.log('GetInfo response:', data)

        if (!data) {
          reject('验证失败，请重新登录.')
        }

        const { roles, name, avatar, introduction, user_id, fen_dian_id } = data

        // roles must be a non-empty array
        if (!roles || roles.length <= 0) {
          reject('getInfo: roles必须是非空数组!')
        }

        commit('SET_ROLES', roles)
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        commit('SET_INTRODUCTION', introduction)
        commit('SET_FENDIAN_ID', fen_dian_id)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_REFRESH_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        removeRefreshToken()
        resetRouter()

        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, { root: true })

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_REFRESH_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      removeRefreshToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
