import gql from 'graphql-tag'
import { companyClient } from '@/apolloClient'

const state = {
  dutyHeads: [],
  dutyDocuments: [],
  loading: false,
  documentsLoading: false,
  dutyDocumentAutoId: 0,
  pagination: {
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  }
}

const mutations = {
  SET_DUTY_HEADS(state, dutyHeads) {
    state.dutyHeads = dutyHeads
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_PAGINATION(state, pagination) {
    state.pagination = pagination
  },
  UPDATE_DUTY_HEAD(state, updatedDutyHead) {
    const index = state.dutyHeads.findIndex(item => item.autoId === updatedDutyHead.autoId)
    if (index !== -1) {
      state.dutyHeads.splice(index, 1, updatedDutyHead)
    }
  },
  DELETE_DUTY_HEAD(state, autoId) {
    state.dutyHeads = state.dutyHeads.filter(item => item.autoId !== autoId)
  },
  SET_DUTY_DOCUMENTS(state, documents) {
    state.dutyDocuments = documents
  },
  SET_DOCUMENTS_LOADING(state, loading) {
    state.documentsLoading = loading
  },
  SET_DUTY_DOCUMENT_AUTO_ID(state, dutyDocumentAutoId) {
    state.dutyDocumentAutoId = dutyDocumentAutoId
  }
}

const actions = {
  fetchDutyHeads({ commit }, { page, pageSize, forceRefresh = false }) {
    console.log('fetchDutyHeads 被调用', { page, pageSize, forceRefresh })

    if (!forceRefresh &&
        page === state.pagination.currentPage &&
        pageSize === state.pagination.pageSize &&
        state.dutyHeads.length > 0) {
      console.log('页码未变化且未强制刷新，使用现有数据')
      return Promise.resolve({
        items: state.dutyHeads,
        pageInfo: state.pagination
      })
    }

    commit('SET_LOADING', true)

    return companyClient.query({
      query: gql`
        query GetDutyHeads($page: Int!, $pageSize: Int!) {
          statistics {
            getDutyHeadsPaginated(page: $page, pageSize: $pageSize) {
              items {
                apiId
                apiTable
                autoId
                companyId
                createDatetime
                yearRange
              }
              pageInfo {
                currentPage
                hasNextPage
                hasPreviousPage
                pageSize
                totalItems
                totalPages
              }
            }
          }
        }
      `,
      variables: {
        page,
        pageSize
      },
      fetchPolicy: 'network-only'
    })
      .then(response => {
        console.log('GraphQL 查询成功', response)

        const result = response.data.statistics.getDutyHeadsPaginated

        commit('SET_DUTY_HEADS', result.items)
        commit('SET_PAGINATION', {
          currentPage: result.pageInfo.currentPage,
          pageSize: result.pageInfo.pageSize,
          totalItems: result.pageInfo.totalItems,
          totalPages: result.pageInfo.totalPages
        })

        commit('SET_LOADING', false)

        return result
      })
      .catch(error => {
        console.error('获取数据出错:', error)
        commit('SET_LOADING', false)
        throw error
      })
  },

  updateDutyHead({ commit }, dutyHead) {
    commit('UPDATE_DUTY_HEAD', dutyHead)
    return Promise.resolve({ success: true })
  },

  deleteDutyHead({ commit }, autoId) {
    commit('DELETE_DUTY_HEAD', autoId)
    return Promise.resolve({ success: true })
  },

  createDutyHead({ commit, dispatch }, companyId) {
    return companyClient.mutate({
      mutation: gql`
        mutation CreateDutyHead($companyId: String!) {
          dutyHead {
            createDutyHead(
              companyId: $companyId
            ) {
              apiId
              apiTable
              autoId
              companyId
              createDatetime
              yearRange
            }
          }
        }
      `,
      variables: {
        companyId
      }
    })
      .then(response => {
        const result = response.data.dutyHead.createDutyHead

        dispatch('fetchDutyHeads', {
          page: 1,
          pageSize: state.pagination.pageSize,
          forceRefresh: true
        })

        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('创建数据出错:', error)
        return { success: false, error }
      })
  },

  insertDutyDocument({ commit }, { documentId, dutyHeadId, expiryDate, documentFee }) {
    return companyClient.mutate({
      mutation: gql`
        mutation InsertDutyDocument($documentId: String!, $dutyHeadId: Int!, $expiryDate: String!, $documentFee: Float!) {
          dutyHead {
            insertDutyDocument(dutyDocument: {
              documentId: $documentId, 
              dutyHeadId: $dutyHeadId,
              expiryDate: $expiryDate,
              documentFee: $documentFee
            }) {
              documentId
              dutyHeadId
            }
          }
        }
      `,
      variables: {
        documentId: String(documentId),
        dutyHeadId: parseInt(dutyHeadId),
        expiryDate: String(expiryDate),
        documentFee: parseFloat(documentFee)
      }
    })
      .then(response => {
        const result = response.data.dutyHead.insertDutyDocument
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('添加文档出错:', error)
        return { success: false, error }
      })
  },

  fetchDutyDocuments({ commit }, dutyHeadId) {
    commit('SET_DOCUMENTS_LOADING', true)

    return companyClient.query({
      query: gql`
        query GetDutyDocuments($dutyHeadId: Int!) {
          statistics {
            getDutyDocumentsByHeadId(dutyHeadId: $dutyHeadId) {
              autoId
              documentId
              dutyHeadId
              expiryDate
              documentFee
            }
          }
        }
      `,
      variables: {
        dutyHeadId: parseInt(dutyHeadId)
      },
      fetchPolicy: 'network-only'
    })
      .then(response => {
        const documents = response.data.statistics.getDutyDocumentsByHeadId
        commit('SET_DUTY_DOCUMENTS', documents)
        commit('SET_DOCUMENTS_LOADING', false)
        return documents
      })
      .catch(error => {
        console.error('获取文档列表出错:', error)
        commit('SET_DOCUMENTS_LOADING', false)
        return []
      })
  },

  insertDutySub({ commit }, { dutyHeadId, goodsId, qty, dutyDocumentId }) {
    return companyClient.mutate({
      mutation: gql`
        mutation InsertDutySub($dutyHeadId: Int!, $goodsId: String!, $qty: Float!, $dutyDocumentId: String!) {
          dutyHead {
            insertDutySub(
              dutyHeadId: $dutyHeadId, 
              goodsId: $goodsId, 
              qty: $qty, 
              dutyDocumentId: $dutyDocumentId
            ) {
              apiId
              apiSelie
              apiSubId
              autoId
              createDatetime
              dutyHeadId
              goodsId
              qty
              dutyDocumentId
            }
          }
        }
      `,
      variables: {
        dutyHeadId,
        goodsId,
        qty,
        dutyDocumentId
      }
    })
      .then(response => {
        const result = response.data.dutyHead.insertDutySub
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('添加商品出错:', error)
        return { success: false, error }
      })
  },

  fetchDutySubs({ commit }, { dutyHeadId, page = 1, pageSize = 10 }) {
    return companyClient.query({
      query: gql`
        query GetDutySubs($dutyHeadId: Int!, $page: Int!, $pageSize: Int!) {
          statistics {
            getDutySubsByHeadId(dutyHeadId: $dutyHeadId, page: $page, pageSize: $pageSize) {
              items {
                autoId
                dutyHeadId
                goodsId
                qty
                dutyDocumentId
                createDatetime
                apiId
                apiSelie
                apiSubId
              }
              total
              page
              pageSize
            }
          }
        }
      `,
      variables: {
        dutyHeadId: parseInt(dutyHeadId),
        page,
        pageSize
      },
      fetchPolicy: 'network-only'
    })
      .then(response => {
        const result = response.data.statistics.getDutySubsByHeadId
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('获取商品列表出错:', error)
        return { success: false, error }
      })
  },

  updateDutySub({ commit }, { autoId, dutyHeadId, goodsId, qty, dutyDocumentId }) {
    return companyClient.mutate({
      mutation: gql`
        mutation UpdateDutySub($autoId: Int!, $dutyHeadId: Int!, $goodsId: String!, $qty: Float!, $dutyDocumentId: String!) {
          dutyHead {
            updateDutySub(
              autoId: $autoId,
              dutyHeadId: $dutyHeadId,
              goodsId: $goodsId,
              qty: $qty,
              dutyDocumentId: $dutyDocumentId
            ) {
              autoId
              dutyHeadId
              goodsId
              qty
              dutyDocumentId
              createDatetime
              apiId
              apiSelie
              apiSubId
            }
          }
        }
      `,
      variables: {
        autoId,
        dutyHeadId,
        goodsId,
        qty,
        dutyDocumentId
      }
    })
      .then(response => {
        const result = response.data.dutyHead.updateDutySub
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('更新商品出错:', error)
        return { success: false, error }
      })
  },

  deleteDutySub({ commit }, autoId) {
    return companyClient.mutate({
      mutation: gql`
        mutation DeleteDutySub($autoId: Int!) {
          dutyHead {
            deleteDutySub(autoId: $autoId)
          }
        }
      `,
      variables: {
        autoId
      }
    })
      .then(response => {
        const result = response.data.dutyHead.deleteDutySub
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('删除商品出错:', error)
        return { success: false, error }
      })
  },

  fetchDutySubsByHeadId({ commit }, { dutyHeadId }) {
    return companyClient.query({
      query: gql`
        query GetDutySubByHeadId($dutyHeadId: Int!) {
          statistics {
            getDutySubByHeadId(dutyHeadId: $dutyHeadId) {
              apiId
              apiSelie
              apiSubId
              autoId
              createDatetime
              dutyDocumentId
              dutyHeadId
              goodsId
              qty
              goodsIdInfo {
                  dName	
                  id
                  idname
                  mmName
                  thName
                }
            }
          }
        }
      `,
      variables: {
        dutyHeadId: parseInt(dutyHeadId)
      },
      fetchPolicy: 'network-only'
    })
      .then(response => {
        const result = response.data.statistics.getDutySubByHeadId
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('获取商品列表出错:', error)
        return { success: false, error }
      })
  },

  fetchDutySubsByDocumentId({ commit }, { dutyDocumentId }) {
    return companyClient.query({
      query: gql`
        query GetDutySubByDocumentId($dutyDocumentId: Int!) {
          statistics {
            getDutySubByDocumentId(dutyDocumentId: $dutyDocumentId) {
              apiId
              apiSelie
              apiSubId
              autoId
              createDatetime
              dutyDocumentId
              dutyHeadId
              goodsId
              qty
              goodsIdInfo {
                  dName	
                  id
                  idname
                  mmName
                  thName
                }
            }
          }
        }   
      `,
      variables: {
        dutyDocumentId: parseInt(dutyDocumentId)
      },
      fetchPolicy: 'network-only'
    })
      .then(response => {
        const result = response.data.statistics.getDutySubByDocumentId
        return {
          success: true,
          data: result
        }
      })
      .catch(error => {
        console.error('获取商品列表出错:', error)
        return { success: false, error }
      })
  }
}

const getters = {
  dutyHeads: state => state.dutyHeads,
  loading: state => state.loading,
  pagination: state => state.pagination,
  dutyDocuments: state => state.dutyDocuments,
  documentsLoading: state => state.documentsLoading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
