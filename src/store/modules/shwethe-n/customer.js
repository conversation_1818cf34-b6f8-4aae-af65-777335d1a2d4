import { gql } from 'graphql-tag'
import { infoClient } from '@/apolloClient'
import { searchJiaYiText } from '@/api/search'

// 项目类型映射
const PROJECT_TYPE_MAP = {
  1: 900,  // 店家
  2: 910,  // 工地
  3: 920   // အဝယ်တော်
}

// 获取项目类型
function getProjectType(type) {
  return PROJECT_TYPE_MAP[type] || 900
}

// 项目状态映射
const PROJECT_STATUS_MAP = {
  19: 30001,  // 作业暂停
  20: 30002,  // 作业结束
  21: 30003   // 作业进行中
}

// 获取项目状态
function getProjectStatus(status) {
  return PROJECT_STATUS_MAP[status] || 30003  // 默认为进行中
}

const state = {
  currentCustomerId: null,

  // 列表相关
  customers: [],
  totalCount: 0,
  listLoading: false,
  
  // 分页相关
  currentPage: 1,
  pageSize: 10,
  
  // 排序相关
  sortField: 'credit_day',
  sortDirection: 'desc',
  
  // 搜索相关
  searchForm: {
    idname: '',
    mmName: ''
  },

  // 表单相关
  formVisible: false,
  formType: 'create', // create or edit
  currentCustomer: null,
  formLoading: false,
  
  // 项目选项
  projectOptions: [],
  projectSearchLoading: false,
  projectSearchResults: [],

  // 项目表单相关
  projectFormVisible: false,
  projectFormType: 'create',
  currentProject: null,
}

const mutations = {
  SET_CURRENT_CUSTOMER_ID: (state, id) => {
    state.currentCustomerId = id
  },
  SET_CUSTOMERS: (state, customers) => {
    state.customers = customers
  },
  SET_TOTAL_COUNT: (state, count) => {
    state.totalCount = count
  },
  SET_LIST_LOADING: (state, loading) => {
    state.listLoading = loading
  },
  SET_CURRENT_PAGE: (state, page) => {
    state.currentPage = page
  },
  SET_PAGE_SIZE: (state, size) => {
    state.pageSize = size
  },
  SET_SORT: (state, { field, direction }) => {
    state.sortField = field
    state.sortDirection = direction
  },
  SET_SEARCH_FORM: (state, form) => {
    state.searchForm = form
  },
  SET_FORM_VISIBLE: (state, visible) => {
    state.formVisible = visible
  },
  SET_FORM_TYPE: (state, type) => {
    state.formType = type
  },
  SET_CURRENT_CUSTOMER: (state, customer) => {
    state.currentCustomer = customer
  },
  SET_FORM_LOADING: (state, loading) => {
    state.formLoading = loading
  },
  SET_PROJECT_OPTIONS: (state, options) => {
    state.projectOptions = options
  },
  RESET_SEARCH_FORM: (state) => {
    state.searchForm = {
      idname: '',
      mmName: ''
    }
  },
  SET_PROJECT_FORM_VISIBLE: (state, visible) => {
    state.projectFormVisible = visible
  },
  SET_PROJECT_FORM_TYPE: (state, type) => {
    state.projectFormType = type
  },
  SET_CURRENT_PROJECT: (state, project) => {
    state.currentProject = project
  },
  SET_PROJECT_SEARCH_LOADING: (state, loading) => {
    state.projectSearchLoading = loading
  },
  SET_PROJECT_SEARCH_RESULTS: (state, results) => {
    state.projectSearchResults = results
  }
}

const actions = {
  // 获取客户列表
  async fetchCustomers({ commit, state }) {
    const GET_CUSTOMERS = gql`
      query GetCustomers(
        $page: Int!
        $pageSize: Int!
        $sortField: String!
        $sortDirection: String!
        $search: String
      ) {
        customers(
          page: $page
          limit: $pageSize
          sort: {field: $sortField, direction: $sortDirection}
          search: $search
        ) {
          items {
            contact_info_info {
              id
              idname
              mmName
            }
            creditDay
            creditScore
            phone
            projectsInfo {
              autoId
              project_id_info {
                id
                idname
                mmName
              }
            }
            defaultStatus
            contactInfo
          }
          totalCount
        }
      }
    `

    try {
      commit('SET_LIST_LOADING', true)
      const { data } = await infoClient.query({
        query: GET_CUSTOMERS,
        variables: {
          page: state.currentPage,
          pageSize: state.pageSize,
          sortField: state.sortField,
          sortDirection: state.sortDirection,
          search: state.searchForm.idname || state.searchForm.mmName || ''
        },
        fetchPolicy: 'no-cache'
      })
      
      commit('SET_CUSTOMERS', data.customers.items)
      commit('SET_TOTAL_COUNT', data.customers.totalCount)
    } catch (error) {
      console.error('获取客户数据失败:', error)
      throw error
    } finally {
      commit('SET_LIST_LOADING', false)
    }
  },

  // 获取项目选项
  async fetchProjects({ commit }) {
    const GET_PROJECTS = gql`
      query GetProjects {
        projectsTb {
          project_id
          project_idname
          project_mm_name
        }
      }
    `
    try {
      const { data } = await infoClient.query({
        query: GET_PROJECTS,
        fetchPolicy: 'no-cache'
      })
      // 转换字段名以匹配组件中使用的格式
      const formattedProjects = data.projectsTb.map(project => ({
        id: project.project_id,
        idname: project.project_idname,
        mmName: project.project_mm_name
      }))
      commit('SET_PROJECT_OPTIONS', formattedProjects)
    } catch (error) {
      console.error('获取项目列表失败:', error)
      throw error
    }
  },

  // 创建客户
  async createCustomer({ commit, rootState }, formData) {
    const CREATE_CUSTOMER = gql`
      mutation CreateCustomer($input: CustomerCreateInput!) {
        createCustomer(input: $input) {
          autoId
          customerId
          phone
          contactInfo
          creditScore
          __typename
        }
      }
    `
    try {
      commit('SET_FORM_LOADING', true)
      const userId = rootState.user?.id || 10
        
      await infoClient.mutate({
        mutation: CREATE_CUSTOMER,
        variables: {
          input: {
            phone: formData.phone || '',
            contactInfo: formData.contactInfo || '',
            creditScore: formData.creditScore,
            changedBy: userId
          }
        }
      })
    } finally {
      commit('SET_FORM_LOADING', false)
    }
  },

  // 更新客户
  async updateCustomer({ commit, rootState }, { id, formData }) {
    const UPDATE_CUSTOMER = gql`
      mutation UpdateCustomer($input: CustomerUpdateInput!) {
        updateCustomer(input: $input) {
          autoId
          customerId
          phone
          contactInfo
          creditScore
          __typename
        }
      }
    `
    try {
      commit('SET_FORM_LOADING', true)
      const userId = rootState.user?.id || 10

      await infoClient.mutate({
        mutation: UPDATE_CUSTOMER,
        variables: {
          input: {
            customerId: parseInt(id),
            phone: formData.phone || '',
            contactInfo: formData.contactInfo || '',
            creditScore: formData.creditScore,
            changedBy: userId
          }
        }
      })
    } finally {
      commit('SET_FORM_LOADING', false)
    }
  },

  // 删除客户
  async deleteCustomer({ commit }, id) {
    const DELETE_CUSTOMER = gql`
      mutation DeleteCustomer($id: ID!) {
        deleteCustomer(id: $id) {
          success
        }
      }
    `
    await infoClient.mutate({
      mutation: DELETE_CUSTOMER,
      variables: { id }
    })
  },

  // 打开新增表单
  openCreateForm({ commit }) {
    commit('SET_FORM_TYPE', 'create')
    commit('SET_CURRENT_CUSTOMER', null)
    commit('SET_FORM_VISIBLE', true)
  },

  // 打开编辑表单
  openEditForm({ commit }, customer) {
    commit('SET_FORM_TYPE', 'edit')
    commit('SET_CURRENT_CUSTOMER', customer)
    commit('SET_FORM_VISIBLE', true)
  },

  // 关闭表单
  closeForm({ commit }) {
    commit('SET_FORM_VISIBLE', false)
  },

  // 重置搜索
  resetSearch({ commit, dispatch }) {
    commit('RESET_SEARCH_FORM')
    commit('SET_CURRENT_PAGE', 1)
    return dispatch('fetchCustomers')
  },

  // 打开新增项目表单
  openCreateProjectForm({ commit }) {
    commit('SET_PROJECT_FORM_TYPE', 'create')
    commit('SET_CURRENT_PROJECT', null)
    commit('SET_PROJECT_FORM_VISIBLE', true)
  },

  // 打开编辑项目表单
  openEditProjectForm({ commit }, project) {
    commit('SET_PROJECT_FORM_TYPE', 'edit')
    commit('SET_CURRENT_PROJECT', project)
    commit('SET_PROJECT_FORM_VISIBLE', true)
  },

  // 关闭项目表单
  closeProjectForm({ commit }) {
    commit('SET_PROJECT_FORM_VISIBLE', false)
  },

  // 创建项目
  async createProject({ commit, rootState }, { customerId, ...formData }) {
    console.log('Creating project with:', {
      customerId,
      formData
    })

    console.log('customerId', state.currentCustomerId)

    
    const CREATE_PROJECT = gql`
      mutation UpsertProject(
        $projectId: Int!
        $customerId: Int!
        $location: String!
        $type: Int!
        $status: Int!
        $startDate: String!
      ) {
        upsertProject(
          input: {
            projectId: $projectId
            customerId: $customerId
            location: $location
            type: $type
            status: $status
            startDate: $startDate
          }
        ) {
          autoId
          projectId
          customerId
          location
          startDate
          completionDate
          status
          type
          project_id_info {
            id
            idname
            mmName
          }
        }
      }
    `
    try {
      commit('SET_FORM_LOADING', true)

      
      // 从搜索选择的项目中获取 jiaYiId 作为 projectId
      const projectId = parseInt(formData.jiaYiId)
      // 从 contact_info_info.id 获取 customerId
      const customerIdNum = parseInt(customerId)
      
      console.log('Parsed IDs:', {
        projectId,
        customerIdNum,
        originalCustomerId: customerId,
        originalJiaYiId: formData.jiaYiId
      })

      if (!projectId || !state.currentCustomerId) {
        throw new Error(projectId ? '无效的客户ID' : '请选择有效的项目')
      }

      await infoClient.mutate({
        mutation: CREATE_PROJECT,
        variables: {
          projectId: projectId,
          customerId: state.currentCustomerId,
          location: formData.location || '',
          type: parseInt(formData.type) || 10,
          status: parseInt(formData.status) || 10,
          startDate: formData.startDate || '',
        }
      })
    } finally {
      commit('SET_FORM_LOADING', false)
    }
  },

  // 搜索项目
  async searchProjects({ commit }, text) {
    if (!text) {
      commit('SET_PROJECT_SEARCH_RESULTS', [])
      return
    }
    
    try {
      commit('SET_PROJECT_SEARCH_LOADING', true)
      const response = await searchJiaYiText(text)
      
      if (response.code !== 20000) {
        throw new Error(response.message || 'Search failed')
      }
      
      // 转换搜索结果格式
      const formattedResults = response.data.map(item => ({
        id: item.jiaYiId,
        idname: item.jiaYiIdname,
        mmName: item.jiaYiMmName,
        type: getProjectType(item.type),
        status: getProjectStatus(item.status),
        date: item.ri_qi
      }))
      
      commit('SET_PROJECT_SEARCH_RESULTS', formattedResults)
    } catch (error) {
      console.error('搜索项目失败:', error)
      commit('SET_PROJECT_SEARCH_RESULTS', [])
      throw error
    } finally {
      commit('SET_PROJECT_SEARCH_LOADING', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
