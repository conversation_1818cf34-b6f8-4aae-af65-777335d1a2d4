import { wlClient } from '@/apolloClient'
import gql from 'graphql-tag'

const GET_BRICK_BATCHES = gql`
  query GetBrickBatches($startDate: Date!) {
    getBrickBatches(startDate: $startDate) {
      autoId
      operatorId
      productionDate
      productionLineId
      qualityGrade
      quantity
      remarks
      templateId
      materialUsages {
        autoId
        quantityUsed
        brickBatchId
        rawMaterialId
        usageDate
        price
      }
    }
  }
`

const UPDATE_MATERIAL_PRICE = gql`
  mutation UpdateMaterialUsage(
    $usageId: Int!
    $price: Decimal!
  ) {
    updateMaterialUsage(
      usageId: $usageId
      price: $price
    ) {
      autoId
      price
    }
  }
`

const state = {
  brickBatches: [],
  listLoading: false,
  currentPage: 1,
  pageSize: 10,
  totalCount: 0,
  searchForm: {
    startDate: new Date().toISOString().split('T')[0]
  },
  materialDialogVisible: false,
  currentMaterialUsages: []
}

const mutations = {
  SET_BRICK_BATCHES: (state, brickBatches) => {
    state.brickBatches = brickBatches
  },
  SET_LIST_LOADING: (state, loading) => {
    state.listLoading = loading
  },
  SET_CURRENT_PAGE: (state, page) => {
    state.currentPage = page
  },
  SET_PAGE_SIZE: (state, size) => {
    state.pageSize = size
  },
  SET_TOTAL_COUNT: (state, total) => {
    state.totalCount = total
  },
  SET_SEARCH_FORM: (state, form) => {
    state.searchForm = form
  },
  SET_MATERIAL_DIALOG_VISIBLE: (state, visible) => {
    state.materialDialogVisible = visible
  },
  SET_CURRENT_MATERIAL_USAGES: (state, usages) => {
    state.currentMaterialUsages = usages.map(usage => ({
      ...usage,
      editing: false
    }))
  },
  UPDATE_MATERIAL_PRICE: (state, { batchId, usageId, price }) => {
    state.brickBatches = state.brickBatches.map(batch => {
      if (batch.autoId === batchId) {
        return {
          ...batch,
          materialUsages: batch.materialUsages.map(usage => {
            if (usage.autoId === usageId) {
              return {
                ...usage,
                price
              }
            }
            return usage
          })
        }
      }
      return batch
    })

    state.currentMaterialUsages = state.currentMaterialUsages.map(usage => {
      if (usage.autoId === usageId) {
        return {
          ...usage,
          price,
          editing: false
        }
      }
      return usage
    })
  }
}

const actions = {
  async fetchBrickBatches({ commit, state }) {
    try {
      commit('SET_LIST_LOADING', true)
      const { data } = await wlClient.query({
        query: GET_BRICK_BATCHES,
        variables: {
          startDate: state.searchForm.startDate
        }
      })

      const brickBatches = data.getBrickBatches || []
      commit('SET_BRICK_BATCHES', brickBatches)
      commit('SET_TOTAL_COUNT', brickBatches.length)
    } catch (error) {
      console.error('获取砖块批次数据失败:', error)
      throw error
    } finally {
      commit('SET_LIST_LOADING', false)
    }
  },

  resetSearch({ commit }) {
    const today = new Date().toISOString().split('T')[0]
    commit('SET_SEARCH_FORM', {
      startDate: today
    })
  },

  async updateMaterialPrice({ commit }, { autoId, price, row }) {
    try {
      const { data } = await wlClient.mutate({
        mutation: UPDATE_MATERIAL_PRICE,
        variables: {
          usageId: parseInt(row.autoId),
          price: parseFloat(price)
        }
      })

      commit('UPDATE_MATERIAL_PRICE', {
        batchId: row.brickBatchId,
        usageId: row.autoId,
        price: parseFloat(price)
      })

      return data.updateMaterialUsage
    } catch (error) {
      console.error('更新价格失败:', error)
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
