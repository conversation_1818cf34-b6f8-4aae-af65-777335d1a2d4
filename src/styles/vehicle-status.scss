// 车辆状态管理系统样式

// 状态颜色定义
$status-colors: (
  'active': #52c41a,
  'inactive': #faad14,
  'maintenance': #1890ff,
  'parked': #722ed1,
  'in_transit': #13c2c2,
  'reserved': #eb2f96,
  'scrapped': #8c8c8c,
  'sold': #52c41a,
  'lost': #f5222d,
  'damaged': #ff4d4f
);

// 状态标签样式
.vehicle-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  i {
    font-size: 14px;
  }
}

// 为每个状态生成样式
@each $status, $color in $status-colors {
  .status-#{$status} {
    background-color: #{$color};
    color: white;
  }
}

// 状态卡片样式
.vehicle-status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .status-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .status-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 20px;
        color: white;
      }
    }
    
    .status-title {
      h3 {
        margin: 0 0 5px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }
  
  .status-count {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin: 10px 0;
  }
  
  .status-description {
    font-size: 13px;
    color: #999;
    text-align: center;
    line-height: 1.4;
  }
}

// 状态统计网格
.status-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

// 状态管理器样式
.vehicle-status-manager {
  .status-display {
    .status-tag {
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    }
    
    .status-remark {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .status-edit {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .el-button {
      padding: 5px 8px;
    }
  }
}

// 状态筛选器样式
.status-filter {
  .filter-item {
    margin-right: 10px;
    margin-bottom: 10px;
  }
  
  .status-filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    
    .status-tag {
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

// 状态更新对话框样式
.status-update-dialog {
  .el-dialog__body {
    padding: 20px;
  }
  
  .current-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 15px;
    
    .status-icon {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      i {
        font-size: 16px;
        color: white;
      }
    }
  }
  
  .status-options {
    .el-option {
      padding: 8px 20px;
      
      .option-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .option-name {
          font-weight: 500;
        }
        
        .option-description {
          color: #8492a6;
          font-size: 12px;
        }
      }
    }
  }
}

// 状态历史记录样式
.status-history {
  .history-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
    
    .status-change {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .old-status,
      .new-status {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        color: white;
      }
      
      .arrow {
        color: #666;
        font-size: 12px;
      }
    }
    
    .change-info {
      flex: 1;
      margin-left: 15px;
      
      .change-time {
        font-size: 12px;
        color: #999;
      }
      
      .change-remark {
        font-size: 13px;
        color: #666;
        margin-top: 2px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .vehicle-status-card {
    padding: 15px;
    
    .status-header {
      .status-icon {
        width: 40px;
        height: 40px;
        
        i {
          font-size: 16px;
        }
      }
      
      .status-title h3 {
        font-size: 16px;
      }
    }
    
    .status-count {
      font-size: 24px;
    }
  }
  
  .status-filter {
    .filter-item {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}

// 动画效果
@keyframes statusPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.status-updating {
  animation: statusPulse 1s infinite;
}

// 状态图标映射
.status-icon-active::before { content: "\e6e1"; } // check-circle
.status-icon-inactive::before { content: "\e6e3"; } // pause-circle
.status-icon-maintenance::before { content: "\e6e5"; } // tools
.status-icon-parked::before { content: "\e6e7"; } // car
.status-icon-in_transit::before { content: "\e6e9"; } // truck
.status-icon-reserved::before { content: "\e6eb"; } // calendar
.status-icon-scrapped::before { content: "\e6ed"; } // delete
.status-icon-sold::before { content: "\e6ef"; } // money
.status-icon-lost::before { content: "\e6f1"; } // warning
.status-icon-damaged::before { content: "\e6f3"; } // close 