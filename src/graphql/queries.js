import gql from 'graphql-tag'

export const GET_DUTY_HEAD_ALL = gql`
  query GetDutyHeadAll($page: Int, $pageSize: Int) {
    statistics {
      getDutyHeadAll(page: $page, pageSize: $pageSize) {
        items {
          apiId
          apiTable
          autoId
          companyId
          createDatetime
          rate
          ratePo
          yearRange
          dutyDocuments {
            autoId
            documentFee
            documentId
            dutyHeadId
            expiryDate
            dutySub {
              apiId
              apiSelie
              apiSubId
              autoId
              createDatetime
              delete
              dutyDocumentId
              dutyGoodsInfoId
              dutyHeadId
              goodsId
              qty
              dutyGoodsInfo {
                autoId
                av
                bank
                commercial
                createDatetime
                custom
                goodsId
                income
                po
                goodsIdInfo {
                  dName
                  idname
                  id
                  mmName
                  thName
                }
              }
              goodsIdInfo {
                dName
                id
                idname
                mmName
                thName
              }
            }
          }
        }
        pageInfo {
          currentPage
          hasNextPage
          hasPreviousPage
          pageSize
          totalPages
          totalItems
        }
      }
    }
  }
`

export const GET_INPUT_USER_STATS = gql`
  query GetInputUserStats($sortBy: String, $sortOrder: SortOrder) {
    getInputUserIdInsertToInsertOrder(sortBy: $sortBy, sortOrder: $sortOrder) {
      count
      inputUserId
      yearMonth
      translation {
        d_name
        id
        idname
        mm_name
        ri_qi
      }
    }
  }
`

export const GET_INPUT_USER_STATS_FOR_SUCCESS = gql`
  query GetInputUserStatsForSuccess($sortBy: String, $sortOrder: SortOrder) {
    getInputUserIdInsertToInsertOrderForSuccess(sortBy: $sortBy, sortOrder: $sortOrder) {
      count
      inputUserId
      yearMonth
      translation {
        d_name
        id
        idname
        mm_name
        ri_qi
      }
    }
  }
`
