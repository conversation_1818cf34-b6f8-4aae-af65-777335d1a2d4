<template>
  <div class="grid-container">
    <!-- 第一个表格 -->
    <div class="grid-item">
      <el-table
        v-loading="loading1"
        :data="tableData1"
        style="width: 100%;"
        border
        @sort-change="handleSortChange1"
      >
        <el-table-column
          prop="inputUserId"
          label="用户ID"
          width="120"
          sortable="custom"
        />
        <el-table-column
          prop="yearMonth"
          label="年月"
          width="120"
          sortable="custom"
        />
        <el-table-column
          prop="count"
          label="数量"
          width="120"
          align="right"
          sortable="custom"
        />
        <el-table-column label="翻译信息" min-width="400">
          <template slot-scope="scope">
            <div v-if="scope.row.translation">
              <div>ID: {{ scope.row.translation.id }}</div>
              <div>名称: {{ scope.row.translation.idname }}</div>
              <div>中文名: {{ scope.row.translation.d_name }}</div>
              <div>缅文名: {{ scope.row.translation.mm_name }}</div>
              <div>日期: {{ scope.row.translation.ri_qi }}</div>
            </div>
            <span v-else>暂无翻译信息</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 第二个表格 -->
    <div class="grid-item">
      <el-table
        v-loading="loading2"
        :data="tableData2"
        style="width: 100%;"
        border
        @sort-change="handleSortChange2"
      >
        <el-table-column
          prop="inputUserId"
          label="用户ID"
          width="120"
          sortable="custom"
        />
        <el-table-column
          prop="yearMonth"
          label="年月"
          width="120"
          sortable="custom"
        />
        <el-table-column
          prop="count"
          label="数量"
          width="120"
          align="right"
          sortable="custom"
        />
        <el-table-column label="翻译信息" min-width="400">
          <template slot-scope="scope">
            <div v-if="scope.row.translation">
              <div>ID: {{ scope.row.translation.id }}</div>
              <div>名称: {{ scope.row.translation.idname }}</div>
              <div>中文名: {{ scope.row.translation.d_name }}</div>
              <div>缅文名: {{ scope.row.translation.mm_name }}</div>
              <div>日期: {{ scope.row.translation.ri_qi }}</div>
            </div>
            <span v-else>暂无翻译信息</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { staticsClient } from '@/apolloClient'
import { GET_INPUT_USER_STATS, GET_INPUT_USER_STATS_FOR_SUCCESS } from '@/graphql/queries'

export default {
  name: 'StatisticsTable',
  data() {
    return {
      tableData1: [],
      tableData2: [],
      loading1: false,
      loading2: false,
      sortBy1: 'yearMonth',
      sortOrder1: 'DESC',
      sortBy2: 'yearMonth',
      sortOrder2: 'DESC'
    }
  },
  created() {
    this.fetchData1()
    this.fetchData2()
  },
  methods: {
    async fetchData1() {
      this.loading1 = true
      try {
        const response = await staticsClient.query({
          query: GET_INPUT_USER_STATS,
          variables: {
            sortBy: this.sortBy1,
            sortOrder: this.sortOrder1
          },
          fetchPolicy: 'network-only'
        })
        this.tableData1 = response.data.getInputUserIdInsertToInsertOrder
      } catch (error) {
        this.$message({
          type: 'error',
          message: '获取数据失败，请稍后重试'
        })
        console.error('GraphQL Error:', error)
      } finally {
        this.loading1 = false
      }
    },
    async fetchData2() {
      this.loading2 = true
      try {
        const response = await staticsClient.query({
          query: GET_INPUT_USER_STATS_FOR_SUCCESS,
          variables: {
            sortBy: this.sortBy2,
            sortOrder: this.sortOrder2
          },
          fetchPolicy: 'network-only'
        })
        this.tableData2 = response.data.getInputUserIdInsertToInsertOrderForSuccess
      } catch (error) {
        this.$message({
          type: 'error',
          message: '获取数据失败，请稍后重试'
        })
        console.error('GraphQL Error:', error)
      } finally {
        this.loading2 = false
      }
    },
    handleSortChange1({ prop, order }) {
      const sortByMap = {
        'inputUserId': 'inputUserId',
        'yearMonth': 'yearMonth',
        'count': 'count'
      }
      const sortOrderMap = {
        'ascending': 'ASC',
        'descending': 'DESC',
        null: 'DESC'
      }
      this.sortBy1 = sortByMap[prop] || 'yearMonth'
      this.sortOrder1 = sortOrderMap[order]
      this.fetchData1()
    },
    handleSortChange2({ prop, order }) {
      const sortByMap = {
        'inputUserId': 'inputUserId',
        'yearMonth': 'yearMonth',
        'count': 'count'
      }
      const sortOrderMap = {
        'ascending': 'ASC',
        'descending': 'DESC',
        null: 'DESC'
      }
      this.sortBy2 = sortByMap[prop] || 'yearMonth'
      this.sortOrder2 = sortOrderMap[order]
      this.fetchData2()
    }
  }
}
</script>

<style scoped>
.grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 20px;
}

.grid-item {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

