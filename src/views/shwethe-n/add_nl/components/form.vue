<template>
  <el-card class="form-card" shadow="hover">
    <div slot="header" class="clearfix">
      <span>{{ editingData ? '编辑数据' : '添加数据' }}</span>
    </div>
    <el-form
      ref="dataForm"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="demo-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目ID" prop="project_id">
            <el-input v-model.number="form.project_id" placeholder="请输入项目ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="NL类型" prop="type_nl">
            <el-select v-model="form.type_nl" placeholder="请选择NL类型" style="width: 100%;">
              <el-option label="默认" value="default" />
              <!-- 可以根据需要添加更多选项 -->
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="添加用户数" prop="add_user">
            <el-input-number
              v-model="form.add_user"
              :min="1"
              :max="100"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="允许用户数" prop="allow_user">
            <el-input-number
              v-model="form.allow_user"
              :min="1"
              :max="100"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="是否已使用" prop="used">
        <el-switch
          v-model="form.used"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">
          {{ editingData ? '更新' : '添加' }}
        </el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button v-if="editingData" @click="cancelEdit">取消编辑</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { createNlUser } from '@/api/shwethe-data-n'

export default {
  name: 'AddForm',
  props: {
    // 编辑数据，如果有值则为编辑模式
    editingData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      form: {
        project_id: '',
        type_nl: 'default',
        add_user: 1,
        allow_user: 1,
        used: false
      },
      // 表单验证规则
      rules: {
        project_id: [
          { required: true, message: '请输入项目ID', trigger: 'blur' },
          { type: 'number', message: '项目ID必须为数字', trigger: 'blur' }
        ],
        type_nl: [
          { required: true, message: '请选择NL类型', trigger: 'change' }
        ],
        add_user: [
          { required: true, message: '请输入添加用户数', trigger: 'blur' },
          { type: 'number', message: '添加用户数必须为数字', trigger: 'blur' }
        ],
        allow_user: [
          { required: true, message: '请输入允许用户数', trigger: 'blur' },
          { type: 'number', message: '允许用户数必须为数字', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    // 监听编辑数据变化
    editingData: {
      handler(newVal) {
        if (newVal) {
          // 编辑模式，填充表单数据
          this.form = { ...newVal }
        } else {
          // 新增模式，重置表单
          this.resetForm()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 提交表单
    async submitForm() {
      this.$refs.dataForm.validate(async(valid) => {
        if (valid) {
          try {
            const formData = { ...this.form }

            if (this.editingData) {
              // 更新数据
              this.$emit('update-data', {
                ...this.editingData,
                ...formData
              })
            } else {
              // 添加数据
              const response = await createNlUser(formData)
              if (response.data) {
                this.$message.success(response.message || '创建成功')
                this.$emit('add-data', response.data)
                this.resetForm()
              }
            }
          } catch (error) {
            this.$message.error('操作失败：' + error.message)
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
      this.form = {
        project_id: '',
        type_nl: 'default',
        add_user: 1,
        allow_user: 1,
        used: false
      }
    },

    // 取消编辑
    cancelEdit() {
      this.$emit('cancel-edit')
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.form-card {
  margin-bottom: 20px;
}

.demo-form {
  max-width: 100%;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-input-number {
  width: 100%;
}
</style>
