<template>
  <el-card class="table-card" shadow="hover">
    <div slot="header" class="clearfix">
      <span>NL用户列表</span>
      <div style="float: right;">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索项目ID"
          style="width: 250px; margin-right: 10px;"
          @input="handleSearch"
        >
          <i slot="prefix" class="el-input__icon el-icon-search" />
        </el-input>
      </div>
    </div>

    <el-table
      :data="filteredTableData"
      style="width: 100%"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="auto_id" label="ID" width="80" />
      <el-table-column prop="project_id" label="项目ID" width="120" />
      <el-table-column prop="type_nl" label="NL类型" width="120">
        <template slot-scope="scope">
          <el-tag :type="getNlTypeTagType(scope.row.type_nl)">
            {{ getNlTypeName(scope.row.type_nl) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="add_user" label="添加用户数" width="120" />
      <el-table-column prop="allow_user" label="允许用户数" width="120" />
      <el-table-column prop="used" label="使用状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.used ? 'success' : 'info'">
            {{ scope.row.used ? '已使用' : '未使用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="datetime" label="创建时间" width="180" />
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="editRow(scope.row)">
            编辑
          </el-button>
          <el-button size="mini" type="danger" @click="deleteRow(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 批量操作按钮 -->
    <div class="batch-actions" style="margin-top: 20px;">
      <el-button
        :disabled="selectedRows.length === 0"
        type="danger"
        @click="batchDelete"
      >
        批量删除 ({{ selectedRows.length }})
      </el-button>
    </div>

    <!-- 分页 -->
    <el-pagination
      style="margin-top: 20px; text-align: center;"
      background
      :current-page="pagination.current"
      :page-sizes="[10, 20, 50]"
      :page-size="pagination.size"
      :total="totalCount"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-card>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 搜索关键词
      searchKeyword: '',
      // 选中的行
      selectedRows: [],
      // 分页
      pagination: {
        current: 1,
        size: 10
      }
    }
  },
  computed: {
    // 搜索过滤后的数据
    searchFilteredData() {
      if (!this.searchKeyword) {
        return this.tableData
      }

      const keyword = this.searchKeyword.toLowerCase()
      return this.tableData.filter(item =>
        String(item.project_id).toLowerCase().includes(keyword)
      )
    },

    // 总数量（用于分页）
    totalCount() {
      return this.searchFilteredData.length
    },

    // 过滤后的表格数据（包含分页）
    filteredTableData() {
      const start = (this.pagination.current - 1) * this.pagination.size
      const end = start + this.pagination.size
      return this.searchFilteredData.slice(start, end)
    }
  },
  methods: {
    // 编辑行
    editRow(row) {
      this.$emit('edit-row', row)
    },

    // 删除行
    deleteRow(row) {
      this.$confirm('确定要删除这条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-row', row)
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 选择改变
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 批量删除
    batchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('batch-delete', this.selectedRows)
        this.$message.success(`成功删除 ${this.selectedRows.length} 条数据`)
        this.selectedRows = []
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 搜索处理
    handleSearch() {
      this.pagination.current = 1 // 搜索时重置到第一页
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
    },

    // 获取NL类型名称
    getNlTypeName(value) {
      const map = {
        default: '默认'
        // 可以根据需要添加更多类型
      }
      return map[value] || value
    },

    // 获取NL类型标签样式
    getNlTypeTagType(value) {
      const map = {
        default: 'primary'
        // 可以根据需要添加更多类型
      }
      return map[value] || ''
    }
  }
}
</script>

<style scoped>
.table-card {
  /* 表格卡片样式 */
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.batch-actions {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}
</style>
