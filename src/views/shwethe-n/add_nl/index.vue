<template>
  <div class="app-container">
    <!-- 表单组件 -->
    <AddForm
      :editing-data="editingData"
      @add-data="handleAddData"
      @update-data="handleUpdateData"
      @cancel-edit="handleCancelEdit"
    />

    <!-- 表格组件 -->
    <DataTable
      :table-data="tableData"
      @edit-row="handleEditRow"
      @delete-row="handleDeleteRow"
      @batch-delete="handleBatchDelete"
    />
  </div>
</template>

<script>
import AddForm from './components/form.vue'
import DataTable from './components/table.vue'
import { createNlUser } from '@/api/shwethe-data-n'

export default {
  name: 'AddNlIndex',
  components: {
    AddForm,
    DataTable
  },
  data() {
    return {
      // 表格数据
      tableData: [],
      // 编辑数据
      editingData: null
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      // 模拟一些初始数据
      this.tableData = []
    },

    // 处理添加数据
    async handleAddData(formData) {
      try {
        const response = await createNlUser(formData)
        if (response.data) {
          // 添加到表格数据中
          this.tableData.unshift(response.data)
          this.$message.success(response.message || '创建成功')
        }
      } catch (error) {
        this.$message.error('创建失败：' + error.message)
      }
    },

    // 处理更新数据
    handleUpdateData(updatedData) {
      const index = this.tableData.findIndex(item => item.auto_id === updatedData.auto_id)
      if (index > -1) {
        this.tableData.splice(index, 1, updatedData)
        this.$message.success('更新成功')
      }
      this.editingData = null
    },

    // 处理取消编辑
    handleCancelEdit() {
      this.editingData = null
    },

    // 处理编辑行
    handleEditRow(row) {
      this.editingData = { ...row }
    },

    // 处理删除行
    handleDeleteRow(row) {
      const index = this.tableData.findIndex(item => item.auto_id === row.auto_id)
      if (index > -1) {
        this.tableData.splice(index, 1)
        // 如果正在编辑被删除的行，取消编辑
        if (this.editingData && this.editingData.auto_id === row.auto_id) {
          this.editingData = null
        }
      }
    },

    // 处理批量删除
    handleBatchDelete(selectedRows) {
      selectedRows.forEach(row => {
        const index = this.tableData.findIndex(item => item.auto_id === row.auto_id)
        if (index > -1) {
          this.tableData.splice(index, 1)
        }
      })

      // 如果正在编辑的行在被删除的行中，取消编辑
      if (this.editingData && selectedRows.some(row => row.auto_id === this.editingData.auto_id)) {
        this.editingData = null
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
