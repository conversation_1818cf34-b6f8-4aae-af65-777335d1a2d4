<template>
  <div class="app-container">
    <!-- 头部搜索和操作区 -->
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-user header-icon"></i>
          <span class="header-title">客户管理</span>
        </div>
        <div class="header-controls">
          <el-button 
            type="primary" 
            size="small"
            class="control-button"
            @click="handleAdd"
          >
            <i class="el-icon-plus" /> 新增客户
          </el-button>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="客户编号">
            <el-input
              v-model="searchForm.idname"
              placeholder="请输入客户编号"
              clearable
              @clear="handleSearch"
            />
          </el-form-item>
          <el-form-item label="客户名称">
            <el-input
              v-model="searchForm.mmName"
              placeholder="请输入客户名称"
              clearable
              @clear="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        :data="customers"
        border
        style="width: 100%">
        <el-table-column
          prop="contact_info_info.idname"
          label="客户编号"
          width="120">
        </el-table-column>
        <el-table-column
          prop="contact_info_info.mmName"
          label="客户名称"
          width="180">
        </el-table-column>
        <el-table-column
          prop="creditDay"
          label="信用天数"
          width="100">
        </el-table-column>
        <el-table-column
          prop="creditScore" 
          label="信用分数"
          width="100">
        </el-table-column>
        <el-table-column
          prop="phone"
          label="电话号码"
          width="120">
        </el-table-column>
        <el-table-column
          label="项目信息"
          min-width="300">
          <template slot-scope="scope">
            <div class="project-info">
              <div class="project-list">
                <el-collapse v-if="scope.row.projectsInfo.length">
                  <el-collapse-item>
                    <template slot="title">
                      <div class="project-title">
                        <span class="project-count">{{ scope.row.projectsInfo.length }} 个项目</span>
                        <el-button 
                          type="text"
                          size="mini"
                          @click.stop="handleProjectManage(scope.row)"
                        >
                          管理项目
                        </el-button>
                      </div>
                    </template>
                    <div v-for="project in scope.row.projectsInfo" 
                      :key="project.autoId" 
                      class="project-item"
                    >
                      <div class="project-header">
                        <span class="project-id">{{ project.project_id_info.idname }}</span>
                        <el-tooltip :content="project.project_id_info.mmName" placement="top">
                          <el-tag size="mini" type="info">
                            {{ project.project_id_info.mmName.length > 20 
                              ? project.project_id_info.mmName.slice(0, 20) + '...' 
                              : project.project_id_info.mmName }}
                          </el-tag>
                        </el-tooltip>
                        <div class="project-actions-inline">
                          <el-button 
                            type="text" 
                            size="mini"
                            @click.stop="handleEditProject(scope.row, project)"
                          >
                            编辑
                          </el-button>
                          <el-button 
                            type="text" 
                            size="mini"
                            class="danger-text"
                            @click.stop="handleRemoveProject(scope.row, project)"
                          >
                            移除
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
                <div v-else class="no-project-container">
                  <span class="no-project">暂无项目</span>
                  <el-button
                    type="text"
                    size="mini"
                    @click.stop="handleAddProject(scope.row)"
                  >
                    添加项目
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="contactInfo"
          label="联系方式"
          width="120">
        </el-table-column>
        <el-table-column 
          label="操作" 
          width="200" 
          align="center"
          fixed="right"
        >
          <template slot-scope="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-edit"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount">
        </el-pagination>
      </div>
    </div>

    <!-- 项目表单弹窗 -->
    <form-project-input
      :visible.sync="projectFormVisible"
      :type="projectFormType"
      :customer-id="currentCustomerId"
      :project-data="currentProject"
      @success="handleProjectSuccess"
    />

    <!-- 客户表单弹窗 -->
    <form-project
      :visible.sync="formVisible"
      :type="formType"
      :customer-data="currentCustomer"
      @success="handleFormSuccess"
    />

    <!-- 项目管理弹窗 -->
    <project-manage-dialog
      v-if="currentCustomer"
      :visible.sync="projectManageVisible"
      :customer="currentCustomer"
      @success="handleProjectSuccess"
    />
  </div>
</template>

<script>
import FormProject from './form-project.vue'
import FormProjectInput from './form-project-input.vue'
import ProjectManageDialog from './project-manage-dialog.vue'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'CustomerTable',
  components: {
    FormProject,
    FormProjectInput,
    ProjectManageDialog
  },
  computed: {
    ...mapState('customer', [
      'customers',
      'listLoading',
      'currentPage',
      'pageSize', 
      'totalCount',
      'searchForm',
      'formType',
      'currentCustomer',
      'projectFormType',
      'currentProject'
    ]),
    formVisible: {
      get() {
        return this.$store.state.customer.formVisible
      },
      set(value) {
        this.$store.commit('customer/SET_FORM_VISIBLE', value)
      }
    },
    projectFormVisible: {
      get() {
        return this.$store.state.customer.projectFormVisible
      },
      set(value) {
        this.$store.commit('customer/SET_PROJECT_FORM_VISIBLE', value)
      }
    },
    projectManageVisible: {
      get() {
        return this.$store.state.customer.projectManageVisible
      },
      set(value) {
        this.$store.commit('customer/SET_PROJECT_MANAGE_VISIBLE', value)
      }
    }
  },
  data() {
    return {
      currentCustomerId: null,
      projectManageVisible: false,
      currentCustomer: null
    }
  },
  created() {
    this.getCustomers()
  },
  methods: {
    ...mapActions('customer', [
      'fetchCustomers',
      'openCreateForm',
      'openEditForm',
      'deleteCustomer',
      'resetSearch',
      'openCreateProjectForm',
      'openEditProjectForm',
      'fetchProjects'
    ]),
    async getCustomers() {
      try {
        await this.fetchCustomers()
      } catch (error) {
        this.$message.error('获取客户数据失败')
      }
    },
    handleSizeChange(val) {
      this.$store.commit('customer/SET_PAGE_SIZE', val)
      this.$store.commit('customer/SET_CURRENT_PAGE', 1)
      this.$store.dispatch('customer/fetchCustomers')
    },
    handleCurrentChange(val) {
      this.$store.commit('customer/SET_CURRENT_PAGE', val)
      this.$store.dispatch('customer/fetchCustomers')
    },
    handleAdd() {
      this.$store.dispatch('customer/openCreateForm')
    },
    handleEdit(row) {
      this.$store.dispatch('customer/openEditForm', row)
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该客户?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.$store.dispatch('customer/deleteCustomer', row.contact_info_info.id)
        this.$message.success('删除客户成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除客户失败:', error)
          this.$message.error('删除失败')
        }
      }
    },
    handleSearch() {
      this.$store.commit('customer/SET_CURRENT_PAGE', 1)
      this.$store.dispatch('customer/fetchCustomers')
    },
    handleFormSuccess() {
      this.getCustomers()
    },
    handleAddProject(row) {
      console.log('Adding project for customer:', row)
      if (!row.contact_info_info?.id) {
        this.$message.error('无效的客户ID')
        return
      }
      this.$store.commit('customer/SET_CURRENT_CUSTOMER_ID', row.contact_info_info.id)
      this.currentCustomerId = row.contact_info_info.id
      this.$store.dispatch('customer/openCreateProjectForm')
    },
    handleProjectSuccess() {
      this.getCustomers()
    },
    handleProjectManage(customer) {
      console.log('customer', customer)

      this.$store.commit('customer/SET_CURRENT_CUSTOMER_ID', customer.contact_info_info.id)
      this.currentCustomer = customer
      this.projectManageVisible = true
    },
    async handleEditProject(customer, project) {
      this.currentCustomerId = customer.contact_info_info.id
      this.$store.dispatch('customer/openEditProjectForm', project)
    },
    async handleRemoveProject(customer, project) {
      try {
        await this.$confirm('确认移除该项目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.$store.dispatch('customer/removeProject', {
          customerId: customer.contact_info_info.id,
          projectId: project.project_id_info.id
        })
        this.$message.success('移除项目成功')
        this.getCustomers()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('移除项目失败:', error)
          this.$message.error('移除失败')
        }
      }
    },
    resetSearch() {
      this.$store.dispatch('customer/resetSearch')
      this.$store.commit('customer/SET_CURRENT_PAGE', 1)
      this.$store.dispatch('customer/fetchCustomers')
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
}

.box-card :deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-icon {
  font-size: 24px;
  color: #409EFF;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.search-container {
  padding: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.table-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.05);
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.project-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.project-list {
  flex: 1;
  padding-right: 10px;
}

.project-actions {
  margin-left: 10px;
}

.project-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.project-count {
  font-size: 13px;
  color: #606266;
}

.project-item {
  padding: 8px;
  border-bottom: 1px solid #EBEEF5;
  transition: background-color 0.3s;
}

.project-item:hover {
  background-color: #f5f7fa;
}

.project-header {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;
}

.project-header .el-tag {
  flex: 1;
  overflow: hidden;
}

.project-actions-inline {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.project-item:hover .project-actions-inline {
  opacity: 1;
}

.danger-text {
  color: #F56C6C;
}

.danger-text:hover {
  color: #f78989;
}

.no-project-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
}

.no-project {
  color: #909399;
  font-size: 13px;
}
</style>
