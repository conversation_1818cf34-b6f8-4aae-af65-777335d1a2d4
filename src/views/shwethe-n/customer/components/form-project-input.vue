<template>
  <el-dialog
    :title="type === 'create' ? 'စီမံကိန်း အသစ်ထည့်ရန်' : 'စီမံကိန်း ပြင်ဆင်ရန်'"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleDialogClose"
  >
    <el-form
      ref="projectForm"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="project-form"
    >
      <el-form-item 
        label="စီမံကိန်းနံပါတ်" 
        prop="idname"
      >
        <el-select
          v-model="formData.idname"
          filterable
          remote
          reserve-keyword
          :remote-method="handleSearch"
          :loading="projectSearchLoading"
          placeholder="စီမံကိန်းနံပါတ် ရိုက်ထည့်ပါ"
        >
          <el-option
            v-for="item in projectSearchResults"
            :key="item.id"
            :label="item.idname"
            :value="item.idname"
            @click.native="handleSelect(item)"
          >
            <span style="float: left">{{ item.idname }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.mmName }}
              <el-tag size="mini" :type="item.type === 2 ? 'success' : ''">
                {{ getTypeLabel(item.type) }}
              </el-tag>
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item 
        label="စီမံကိန်းအမည်" 
        prop="mmName"
      >
        <el-input 
          v-model="formData.mmName"
          placeholder="စီမံကိန်းအမည် ရိုက်ထည့်ပါ"
        />
      </el-form-item>

      <el-form-item 
        label="အင်္ဂလိပ်အမည်" 
        prop="enName"
      >
        <el-input 
          v-model="formData.enName"
          placeholder="အင်္ဂလိပ်အမည် ရိုက်ထည့်ပါ"
        />
      </el-form-item>

      <el-form-item 
        label="အသေးစိတ်" 
        prop="description"
      >
        <el-input 
          type="textarea"
          v-model="formData.description"
          :rows="3"
          placeholder="အသေးစိတ် ရိုက်ထည့်ပါ"
        />
      </el-form-item>

      <el-form-item 
        label="စီမံကိန်းအမျိုးအစား" 
        prop="type"
      >
        <el-select
          v-model="formData.type"
          placeholder="စီမံကိန်းအမျိုးအစား ရွေးချယ်ပါ"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item 
        label="အခြေအနေ" 
        prop="status"
      >
        <el-select
          v-model="formData.status"
          placeholder="အခြေအနေ ရွေးချယ်ပါ"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.mmLabel"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item 
        label="တည်နေရာ" 
        prop="location"
      >
        <el-input 
          v-model="formData.location"
          placeholder="တည်နေရာ ရိုက်ထည့်ပါ"
        />
      </el-form-item>

      <el-form-item 
        label="စတင်သည့်ရက်" 
        prop="startDate"
      >
        <el-date-picker
          v-model="formData.startDate"
          type="date"
          placeholder="စတင်သည့်ရက် ရွေးချယ်ပါ"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

      <el-form-item 
        label="ပြီးဆုံးရက်" 
        prop="completionDate"
      >
        <el-date-picker
          v-model="formData.completionDate"
          type="date"
          placeholder="ပြီးဆုံးရက် ရွေးချယ်ပါ"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">ပယ်ဖျက်မည်</el-button>
      <el-button 
        type="primary" 
        :loading="loading"
        @click="handleSubmit"
      >
        သိမ်းဆည်းမည်
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ProjectForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'create' // create or edit
    },
    customerId: {
      type: [Number, String],
      default: null
    },
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: this.visible,
      formData: {
        idname: '',
        mmName: '',
        enName: '',
        description: '',
        status: 30003,
        type: 900,
        location: '',
        startDate: '',
        completionDate: ''
      },
      rules: {
        idname: [
          { required: true, message: 'စီမံကိန်းနံပါတ် ရိုက်ထည့်ပါ', trigger: 'blur' }
        ],
        mmName: [
          { required: true, message: 'စီမံကိန်းအမည် ရိုက်ထည့်ပါ', trigger: 'blur' }
        ],
        type: [
          { required: true, message: 'စီမံကိန်းအမျိုးအစား ရွေးချယ်ပါ', trigger: 'change' }
        ],
        status: [
          { required: true, message: 'အခြေအနေ ရွေးချယ်ပါ', trigger: 'change' }
        ]
      },
      typeOptions: [
        { label: '店家', value: 900 },
        { label: '工地', value: 910 },
        { label: 'အဝယ်တော်', value: 920 }
      ],
      statusOptions: [
        { 
          label: '作业结束',
          mmLabel: 'လုပ်ငန်းပြီးဆုံး',
          value: 30002 
        },
        { 
          label: '作业进行中',
          mmLabel: 'လုပ်ငန်းဆောင်ရွက်နေဆဲ',
          value: 30003 
        },
        { 
          label: '作业暂停',
          mmLabel: 'လုပ်ငန်းရပ်နားထား',
          value: 30001 
        }
      ]
    }
  },
  computed: {
    ...mapState('customer', [
      'formLoading',
      'projectSearchLoading',
      'projectSearchResults'
    ]),
    dialogTitle() {
      return this.type === 'create' ? '新增项目' : '编辑项目'
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    ...mapActions('customer', [
      'createProject',
      'updateProject',
      'searchProjects'
    ]),
    initForm() {
      if (this.type === 'edit' && this.projectData) {
        this.formData = {
          idname: this.projectData.project_idname || this.projectData.idname || '',
          mmName: this.projectData.project_mm_name || this.projectData.mmName || '',
          enName: this.projectData.project_en_name || this.projectData.enName || '',
          description: this.projectData.description || '',
          status: this.projectData.status || 30003,
          type: this.projectData.type || 900,
          location: this.projectData.location || '',
          startDate: this.projectData.startDate || '',
          completionDate: this.projectData.completionDate || ''
        }
      } else {
        this.formData = {
          idname: '',
          mmName: '',
          enName: '',
          description: '',
          status: 30003,
          type: 900,
          location: '',
          startDate: '',
          completionDate: ''
        }
      }
    },
    handleDialogClose(done) {
      if (this.loading) return
      done()
    },
    handleCancel() {
      this.dialogVisible = false
    },
    async handleSubmit() {
      try {
        await this.$refs.projectForm.validate()
        this.loading = true
        
        if(this.type === 'create') {
          await this.createProject({
            ...this.formData,
            customerId: this.customerId
          })
        } else {
          await this.updateProject({
            id: this.projectData.project_id,
            formData: this.formData
          })
        }
        
        this.$message.success(this.type === 'create' ? '新增成功' : '更新成功')
        this.dialogVisible = false
        this.$emit('success')
      } catch (error) {
        if (error !== false) {
          console.error('保存项目失败:', error)
          this.$message.error('保存失败')
        }
      } finally {
        this.loading = false
      }
    },
    handleSearch(query) {
      if (query) {
        this.searchProjects(query)
      }
    },
    handleSelect(item) {
      this.formData = {
        ...this.formData,
        jiaYiId: item.id,
        idname: item.idname,
        mmName: item.mmName,
        type: item.type,
        status: item.status,
        startDate: item.date ? item.date.split(' ')[0] : ''
      }
    },
    getTypeLabel(type) {
      const option = this.typeOptions.find(t => t.value === type)
      return option ? option.label : ''
    }
  }
}
</script>

<style scoped>
.project-form {
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.el-select-dropdown__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.el-select-dropdown__item span {
  margin: 0 10px;
}
</style>
