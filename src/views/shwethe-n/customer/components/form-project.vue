<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleDialogClose"
  >
    <el-form
      ref="customerForm"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="customer-form"
    >
      <el-form-item 
        label="客户编号" 
        prop="idname"
      >
        <el-input 
          v-model="formData.idname"
          placeholder="请输入客户编号"
        />
      </el-form-item>

      <el-form-item 
        label="客户名称" 
        prop="mmName"
      >
        <el-input 
          v-model="formData.mmName"
          placeholder="请输入客户名称"
        />
      </el-form-item>

      <el-form-item 
        label="信用天数" 
        prop="creditDay"
      >
        <el-input-number
          v-model="formData.creditDay"
          :min="0"
          :max="365"
          placeholder="请输入信用天数"
        />
      </el-form-item>

      <el-form-item 
        label="信用分数" 
        prop="creditScore"
      >
        <el-input-number
          v-model="formData.creditScore"
          :min="0"
          :max="100"
          placeholder="请输入信用分数"
        />
      </el-form-item>

      <el-form-item 
        label="电话号码" 
        prop="phone"
      >
        <el-input 
          v-model="formData.phone"
          placeholder="请输入电话号码"
        />
      </el-form-item>

      <el-form-item 
        label="联系方式" 
        prop="contactInfo"
      >
        <el-input 
          v-model="formData.contactInfo"
          placeholder="请输入联系方式"
        />
      </el-form-item>

      <el-form-item 
        label="项目信息" 
        prop="projectsInfo"
      >
        <el-select
          v-model="formData.projectsInfo"
          multiple
          filterable
          placeholder="请选择项目"
        >
          <el-option
            v-for="item in projectOptions"
            :key="item.id"
            :label="item.mmName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button 
        type="primary" 
        :loading="loading"
        @click="handleSubmit"
      >
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'CustomerForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'create' // create or edit
    },
    customerData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: this.visible,
      formData: {
        idname: '',
        mmName: '',
        creditDay: 0,
        creditScore: 0,
        phone: '',
        contactInfo: '',
        projectsInfo: []
      },
      rules: {
        idname: [
          { required: true, message: '请输入客户编号', trigger: 'blur' }
        ],
        mmName: [
          { required: true, message: '请输入客户名称', trigger: 'blur' }
        ],
        creditDay: [
          { required: true, message: '请输入信用天数', trigger: 'blur' }
        ],
        creditScore: [
          { required: true, message: '请输入信用分数', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入电话号码', trigger: 'blur' }
        ],
        contactInfo: [
          { required: true, message: '请输入联系方式', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState('customer', [
      'formLoading',
      'projectOptions'
    ]),
    dialogTitle() {
      return this.type === 'create' ? '新增客户' : '编辑客户'
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
        this.fetchProjects()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    ...mapActions('customer', [
      'fetchProjects',
      'createCustomer',
      'updateCustomer',
      'closeForm'
    ]),
    initForm() {
      if (this.type === 'edit' && this.customerData) {
        const { contact_info_info, creditDay, creditScore, phone, contactInfo, projectsInfo } = this.customerData
        this.formData = {
          idname: contact_info_info?.idname || '',
          mmName: contact_info_info?.mmName || '',
          creditDay: creditDay || 0,
          creditScore: creditScore || 0,
          phone: phone || '',
          contactInfo: contactInfo || '',
          projectsInfo: projectsInfo?.map(p => p.project_id_info.id) || []
        }
      } else {
        this.formData = {
          idname: '',
          mmName: '',
          creditDay: 0,
          creditScore: 0,
          phone: '',
          contactInfo: '',
          projectsInfo: []
        }
      }
    },
    handleDialogClose(done) {
      if (this.loading) return
      done()
    },
    handleCancel() {
      this.dialogVisible = false
    },
    async handleSubmit() {
      try {
        await this.$refs.customerForm.validate()
        this.loading = true
        
        if(this.type === 'create') {
          await this.createCustomer(this.formData)
        } else {
          await this.updateCustomer({
            id: this.customerData.contact_info_info.id,
            formData: this.formData
          })
        }
        
        this.$message.success(this.type === 'create' ? '新增成功' : '更新成功')
        this.dialogVisible = false
        this.$emit('success')
      } catch (error) {
        if (error !== false) {
          console.error('保存客户失败:', error)
          this.$message.error('保存失败')
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.customer-form {
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}
</style>
