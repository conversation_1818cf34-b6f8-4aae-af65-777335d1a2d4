<template>
  <el-dialog
    title="စီမံကိန်းများ စီမံခန့်ခွဲခြင်း"
    :visible.sync="dialogVisible"
    width="70%"
  >
    <div class="manage-container">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          v-model="searchText"
          placeholder="စီမံကိန်း ရှာဖွေရန်"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
        >
          <el-select
            v-model="searchType"
            slot="append"
            style="width: 120px"
          >
            <el-option label="စီမံကိန်းနံပါတ်" value="idname" />
            <el-option label="စီမံကိန်းအမည်" value="mmName" />
          </el-select>
        </el-input>
      </div>

      <!-- 项目列表 -->
      <div class="project-list">
        <el-table
          :data="filteredProjects"
          v-loading="loading"
          style="width: 100%"
        >
          <el-table-column 
            label="စီမံကိန်းနံပါတ်"
            prop="project_id_info.idname" 
            width="120" 
          />
          <el-table-column 
            label="စီမံကိန်းအမည်"
            prop="project_id_info.mmName" 
            min-width="200" 
          />
          <el-table-column 
            label="အခြေအနေ"
            width="120"
          >
            <template slot-scope="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="လုပ်ဆောင်ချက်" width="150" align="center">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                @click="handleEdit(row)"
              >
                ပြင်ဆင်ရန်
              </el-button>
              <el-button
                type="text"
                size="mini"
                class="danger-text"
                @click="handleRemove(row)"
              >
                ဖယ်ရှားရန်
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 批量操作区域 -->
      <div class="batch-actions">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          စီမံကိန်း အသစ်ထည့်ရန်
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ProjectManageDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    customer: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      searchText: '',
      searchType: 'idname',
      loading: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    filteredProjects() {
      if (!this.searchText) return this.customer.projectsInfo || []
      
      return (this.customer.projectsInfo || []).filter(project => {
        const searchValue = this.searchType === 'idname' 
          ? project.project_id_info.idname 
          : project.project_id_info.mmName
        return searchValue.toLowerCase().includes(this.searchText.toLowerCase())
      })
    }
  },
  methods: {
    ...mapActions('customer', [
      'openCreateProjectForm',
      'openEditProjectForm',
      'removeProject'
    ]),
    handleSearch() {
      // 实时搜索，无需额外处理
    },
    handleAdd() {
      this.openCreateProjectForm()
    },
    handleEdit(project) {
      // 转换项目数据格式以匹配表单需要的格式
      const formattedProject = {
        project_id: project.project_id_info.id,
        project_idname: project.project_id_info.idname,
        project_mm_name: project.project_id_info.mmName,
        project_en_name: project.project_id_info.enName || '',
        status: project.status || 30003,
        type: project.type || 900,
        location: project.location || '',
        startDate: project.startDate || '',
      }
      this.openEditProjectForm(formattedProject)
    },
    async handleRemove(project) {
      try {
        await this.$confirm('ဤစီမံကိန်းကို ဖယ်ရှားမှာ သေချာပါသလား?', 'သတိပေးချက်', {
          confirmButtonText: 'သေချာပါသည်',
          cancelButtonText: 'ပယ်ဖျက်မည်',
          type: 'warning'
        })
        await this.removeProject({
          customerId: this.customer.contact_info_info.id,
          projectId: project.project_id_info.id
        })
        this.$emit('success')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('ဖယ်ရှားခြင်း မအောင်မြင်ပါ')
        }
      }
    },
    getStatusType(status) {
      const types = {
        30001: 'warning',  // 作业暂停
        30002: 'success',  // 作业结束
        30003: 'primary'   // 作业进行中
      }
      return types[status] || 'info'
    },
    getStatusLabel(status) {
      const labels = {
        30001: 'လုပ်ငန်းရပ်နားထား',
        30002: 'လုပ်ငန်းပြီးဆုံး',
        30003: 'လုပ်ငန်းဆောင်ရွက်နေဆဲ'
      }
      return labels[status] || 'မသိရှိသောအခြေအနေ'
    }
  }
}
</script>

<style scoped>
.manage-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-area {
  padding: 0 20px;
}

.project-list {
  padding: 0 20px;
  max-height: 500px;
  overflow-y: auto;
}

.batch-actions {
  padding: 20px;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: flex-end;
}

.danger-text {
  color: #F56C6C;
}

.danger-text:hover {
  color: #f78989;
}
</style> 