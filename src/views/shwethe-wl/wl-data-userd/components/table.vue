<template>
  <div class="app-container">
    <!-- 头部搜索和操作区 -->
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-data-analysis header-icon" />
          <span class="header-title">砖块批次管理</span>
        </div>
        <div class="header-controls">
          <el-button
            type="primary"
            size="small"
            class="control-button"
            @click="handleAdd"
          >
            <i class="el-icon-plus" /> 新增批次
          </el-button>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="生产日期">
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              @change="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        :data="brickBatches"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          prop="autoId"
          label="批次ID"
          width="100"
          align="center"
        />
        <el-table-column
          prop="productionDate"
          label="生产日期"
          width="120"
          align="center"
        >
          <template slot-scope="{row}">
            {{ row.productionDate | parseTime('{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="productionLineId"
          label="生产线"
          width="120"
          align="center"
        />
        <el-table-column
          prop="qualityGrade"
          label="质量等级"
          width="100"
          align="center"
        />
        <el-table-column
          prop="quantity"
          label="数量"
          width="100"
          align="center"
        />
        <el-table-column
          prop="operatorId"
          label="操作员"
          width="120"
          align="center"
        />
        <el-table-column
          prop="remarks"
          label="备注"
          min-width="150"
          align="center"
        />
        <el-table-column
          label="原料使用"
          width="120"
          align="center"
        >
          <template slot-scope="{row}">
            <el-button
              type="text"
              @click="showMaterialUsages(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200"
          align="center"
          fixed="right"
        >
          <template slot-scope="{row}">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 原料使用详情对话框 -->
    <el-dialog
      title="原料使用详情"
      :visible.sync="materialDialogVisible"
      width="800px"
      custom-class="material-dialog"
    >
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>原料使用记录</h3>
          <p class="dialog-desc">可以点击价格进行修改</p>
        </div>

        <el-table
          :data="currentMaterialUsages"
          border
          fit
          style="width: 100%"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontWeight: 600
          }"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            prop="rawMaterialId"
            label="原料ID"
            width="120"
            align="center"
          />
          <el-table-column
            prop="quantityUsed"
            label="使用数量"
            width="120"
            align="center"
          />
          <el-table-column
            prop="price"
            label="价格"
            width="150"
            align="center"
          >
            <template slot-scope="{row}">
              <el-input
                v-if="row.editing"
                v-model.number="row.price"
                size="mini"
                class="price-input"
                @blur="handlePriceUpdate(row)"
                @keyup.enter.native="handlePriceUpdate(row)"
              >
                <template slot="prepend">¥</template>
              </el-input>
              <div v-else class="price-cell" @click="startEditing(row)">
                <span class="price-value">¥{{ row.price || 0 }}</span>
                <el-tooltip content="点击修改价格" placement="top">
                  <i class="el-icon-edit edit-icon" />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="usageDate"
            label="使用日期"
            width="180"
            align="center"
          >
            <template slot-scope="{row}">
              {{ row.usageDate | parseTime('{y}-{m}-{d}') }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="materialDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'BrickBatchTable',
  computed: {
    ...mapState('brickBatch', [
      'brickBatches',
      'listLoading',
      'currentPage',
      'pageSize',
      'totalCount',
      'searchForm',
      'currentMaterialUsages'
    ]),
    materialDialogVisible: {
      get() {
        return this.$store.state.brickBatch.materialDialogVisible
      },
      set(value) {
        this.$store.commit('brickBatch/SET_MATERIAL_DIALOG_VISIBLE', value)
      }
    }
  },
  data() {
    return {
      editingMaterial: null
    }
  },
  created() {
    this.getBrickBatches()
  },
  methods: {
    ...mapActions('brickBatch', [
      'fetchBrickBatches',
      'resetSearch',
      'updateMaterialPrice'
    ]),
    async getBrickBatches() {
      try {
        await this.fetchBrickBatches()
      } catch (error) {
        this.$message.error('获取砖块批次数据失败')
      }
    },
    handleSizeChange(val) {
      this.$store.commit('brickBatch/SET_PAGE_SIZE', val)
      this.$store.commit('brickBatch/SET_CURRENT_PAGE', 1)
      this.getBrickBatches()
    },
    handleCurrentChange(val) {
      this.$store.commit('brickBatch/SET_CURRENT_PAGE', val)
      this.getBrickBatches()
    },
    handleSearch() {
      this.$store.commit('brickBatch/SET_CURRENT_PAGE', 1)
      this.getBrickBatches()
    },
    showMaterialUsages(row) {
      this.$store.commit('brickBatch/SET_CURRENT_MATERIAL_USAGES', row.materialUsages || [])
      this.materialDialogVisible = true
    },
    handleAdd() {
      this.$message.info('新增功能待实现')
    },
    handleEdit(row) {
      this.$message.info('编辑功能待实现')
    },
    handleDelete(row) {
      this.$confirm('确认删除该批次记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.info('删除功能待实现')
      }).catch(() => {})
    },
    startEditing(row) {
      row.editing = true
      this.$nextTick(() => {
        const input = this.$el.querySelector('.el-input__inner')
        if (input) input.focus()
      })
    },
    async handlePriceUpdate(row) {
      try {
        const price = parseFloat(row.price)
        if (isNaN(price) || price < 0) {
          this.$message.error('请输入有效的价格')
          row.price = 0
          return
        }

        await this.$store.dispatch('brickBatch/updateMaterialPrice', {
          autoId: row.autoId,
          price,
          row
        })

        this.$message.success('价格更新成功')

        // 可以選擇是否重新獲取數據
        // await this.getBrickBatches()
      } catch (error) {
        this.$message.error('价格更新失败')
        // 恢復原價格
        const originalUsage = this.currentMaterialUsages.find(u => u.autoId === row.autoId)
        if (originalUsage) {
          row.price = originalUsage.price
        }
      } finally {
        row.editing = false
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.editing) {
        return 'editing-row'
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;

  .header-icon {
    font-size: 20px;
    margin-right: 8px;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
  }
}

.search-container {
  padding: 18px 0;
}

.table-container {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  th {
    background-color: #f5f7fa;
  }

  .el-table__row:hover > td {
    background-color: #f5f7fa;
  }
}

.control-button {
  padding: 8px 16px;
  border-radius: 4px;
}

.price-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .edit-icon {
    margin-left: 5px;
    font-size: 12px;
    color: #409EFF;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    .edit-icon {
      opacity: 1;
    }
  }
}

.el-input.el-input--mini {
  width: 100px;
}

.material-dialog {
  .dialog-content {
    padding: 0 20px;
  }

  .dialog-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 10px;
      font-size: 18px;
      color: #303133;
    }

    .dialog-desc {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }

  .price-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background-color: #ecf5ff;

      .edit-icon {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .price-value {
      font-weight: 600;
      color: #606266;
    }

    .edit-icon {
      margin-left: 8px;
      font-size: 14px;
      color: #409EFF;
      opacity: 0;
      transform: translateX(-5px);
      transition: all 0.3s;
    }
  }

  .price-input {
    width: 120px;

    :deep(.el-input__inner) {
      text-align: center;
    }

    :deep(.el-input-group__prepend) {
      background-color: #f5f7fa;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px 0;
}

:deep(.el-dialog__header) {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

:deep(.el-dialog__footer) {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-table) {
  .hover-row {
    background-color: #f5f7fa;
  }

  .el-table__row {
    transition: all 0.3s;

    &:hover {
      background-color: #ecf5ff;
    }
  }
}
</style>
