<template>
  <div class="app-container">

    <!-- 头部搜索区域 -->
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-goods header-icon"></i>
          <span class="header-title">ကုန်ပစ္စည်းအမျိုးအစားစီမံခန့်ခွဲမှု</span>
        </div>
        <div class="header-controls">
          <div class="level-selectors">
            <el-select
              v-model="selectedLevel1"
              placeholder="ပထမအဆင့်အမျိုးအစား"
              size="small"
              clearable
              class="selector-item"
              @change="handleLevel1Change"
            >
              <el-option
                v-for="item in level1Options"
                :key="item.categoryId"
                :label="item.mmName"
                :value="item.categoryId"
              />
            </el-select>

            <el-select
              v-model="selectedLevel2"
              placeholder="ဒုတိယအဆင့်အမျိုးအစား"
              size="small"
              clearable
              :disabled="!selectedLevel1"
              class="selector-item"
              @change="handleLevel2Change"
            >
              <el-option
                v-for="item in level2Options"
                :key="item.categoryId"
                :label="item.mmName"
                :value="item.categoryId"
              />
            </el-select>

            <el-select
              v-model="selectedLevel3"
              placeholder="တတိယအဆင့်အမျိုးအစား"
              size="small"
              clearable
              :disabled="!selectedLevel2"
              class="selector-item"
              @change="handleFilterChange"
            >
              <el-option
                v-for="item in level3Options"
                :key="item.categoryId"
                :label="item.mmName"
                :value="item.categoryId"
              />
            </el-select>
          </div>

          <el-button-group class="header-buttons">
            <el-button 
              type="primary" 
              size="small"
              class="control-button"
              @click="expandAll"
            >
              <i class="el-icon-zoom-in" /> အားလုံးချဲ့ရန်
            </el-button>
            <el-button 
              type="info" 
              size="small"
              class="control-button"
              @click="collapseAll"
            >
              <i class="el-icon-zoom-out" /> အားလုံးကျုံ့ရန်
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 商品详情对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="goodsForm"
        :model="currentGoods"
        :rules="rules"
        label-width="120px"
        class="goods-form"
      >
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品名称(缅文)" prop="goodsInfo.productMmName">
                  <el-input v-model="currentGoods.goodsInfo.productMmName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品名称(英文)" prop="goodsInfo.productIdName">
                  <el-input v-model="currentGoods.goodsInfo.productIdName" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品价格" prop="goodsPrice">
                  <el-input-number
                    v-model="currentGoods.goodsPrice"
                    :precision="2"
                    :step="0.1"
                    :min="0"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="库存数量" prop="goodsQty">
                  <el-input-number
                    v-model="currentGoods.goodsQty"
                    :min="0"
                    :precision="0"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="品牌" prop="brandId">
                  <el-select 
                    v-model="currentGoods.brandId" 
                    placeholder="请选择品牌"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="brand in brandOptions"
                      :key="brand.brandId"
                      :label="brand.brandMmName"
                      :value="brand.brandId"
                    >
                      <span style="float: left">{{ brand.brandMmName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        {{ brand.brandEnName }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="规格参数" name="specs">
            <div class="specs-container">
              <!-- 规格选择区域 -->
              <div class="specs-selection">
                <!-- 销售规格 -->
                <div class="spec-card" v-if="salesSpecs.length">
                  <div class="section-title">
                    <i class="el-icon-goods" />
                    销售规格
                  </div>
                  <el-form :inline="true" class="spec-form">
                    <el-form-item
                      v-for="spec in salesSpecs"
                      :key="spec.specId"
                      :label="spec.specName"
                      class="spec-form-item"
                    >
                      <!-- 单选 -->
                      <el-select
                        v-if="spec.inputType === 1"
                        v-model="selectedSpecOptions[spec.specId]"
                        :placeholder="spec.specName"
                        size="small"
                        class="spec-input"
                        @change="handleSpecChange(spec.specId)"
                      >
                        <el-option
                          v-for="option in spec.options"
                          :key="option.optionId"
                          :label="option.optionValue"
                          :value="option.optionId"
                        />
                      </el-select>

                      <!-- 多选 -->
                      <el-select
                        v-else-if="spec.inputType === 2"
                        v-model="selectedSpecOptions[spec.specId]"
                        :placeholder="spec.specName"
                        multiple
                        size="small"
                        class="spec-input"
                        collapse-tags
                        @change="handleSpecChange(spec.specId)"
                      >
                        <el-option
                          v-for="option in spec.options"
                          :key="option.optionId"
                          :label="option.optionValue"
                          :value="option.optionId"
                        />
                      </el-select>

                      <!-- 颜色选择器 -->
                      <el-color-picker
                        v-else-if="spec.inputType === 4"
                        v-model="selectedSpecOptions[spec.specId]"
                        show-alpha
                        size="small"
                        class="spec-input"
                        @change="handleSpecChange(spec.specId)"
                      />
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 展示参数 -->
                <div class="spec-card" v-if="displaySpecs.length">
                  <div class="section-title">
                    <i class="el-icon-document" />
                    展示参数
                  </div>
                  <el-form :inline="true" class="spec-form">
                    <el-form-item
                      v-for="spec in displaySpecs"
                      :key="spec.specId"
                      :label="spec.specName"
                      class="spec-form-item"
                    >
                      <!-- 输入框 -->
                      <el-input
                        v-if="spec.inputType === 3"
                        v-model="selectedSpecOptions[spec.specId]"
                        :placeholder="'请输入' + spec.specName"
                        size="small"
                        class="spec-input"
                        @input="handleSpecChange(spec.specId)"
                      >
                        <template #append v-if="spec.specName.includes('(')">
                          {{ spec.specName.match(/\((.*?)\)/)[1] }}
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>

              <!-- 规格预览 -->
              <div class="spec-preview">
                <div class="section-title">
                  <i class="el-icon-view" />
                  规格预览
                </div>
                <el-input
                  v-model="currentGoods.guiGeParam"
                  type="textarea"
                  :rows="4"
                  readonly
                  class="preview-textarea"
                  placeholder="规格预览将在这里显示"
                />
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="商品图片" name="images">
            <el-form-item label="商品图片">
              <el-upload
                action="#"
                list-type="picture-card"
                :auto-upload="true"
                :file-list="imageList"
                :http-request="handleImageUpload"
                :on-remove="handleRemoveImage"
                :multiple="true"
                accept="image/*"
                :limit="5"
                :on-exceed="handleExceed"
                :before-upload="beforeImageUpload"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          保存
        </el-button>
      </div>
    </el-dialog>

    <div class="table-container">
      <el-table
        v-loading="listLoading"
        :data="skuList"
        border
        fit
        highlight-current-row
        class="data-table"
        @sort-change="handleSortChange"
      >
        <el-table-column 
          label="SKU ID" 
          prop="skuId" 
          align="center" 
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            <span class="id-column">{{ row.skuId }}</span>
          </template>
        </el-table-column>

        <el-table-column 
          label="商品名称" 
          min-width="180" 
          align="center"
          sortable="custom"
          prop="goodsInfo.productMmName"
        >
          <template #default="{ row }">
            <div class="product-name">
              <span class="mm-name">{{ row.goodsInfo.productMmName }}</span>
              <span class="en-name">{{ row.goodsInfo.productIdName }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column 
          label="库存数量" 
          width="100" 
          align="center"
          sortable="custom"
          prop="goodsQty"
        >
          <template #default="{ row }">
            <span :class="['qty-tag', row.goodsQty <= 10 ? 'low' : row.goodsQty <= 50 ? 'medium' : 'high']">
              {{ row.goodsQty }}
            </span>
          </template>
        </el-table-column>

        <el-table-column 
          label="品牌" 
          width="180" 
          align="center"
          sortable="custom"
          prop="brandId"
        >
          <template #default="{ row }">
            <div v-if="row.brandId" class="brand-info">
              <template v-if="getBrandInfo(row.brandId)">
                <div class="brand-name">
                  <span class="mm-name">{{ getBrandInfo(row.brandId).brandMmName }}</span>
                  <span class="en-name">{{ getBrandInfo(row.brandId).brandEnName }}</span>
                </div>
              </template>
              <span v-else class="no-brand">{{ row.brandId }}</span>
            </div>
            <span v-else class="no-brand">-</span>
          </template>
        </el-table-column>

        <el-table-column 
          label="最后更新时间" 
          width="160" 
          align="center"
          sortable="custom"
          prop="lastUpdate"
        >
          <template #default="{ row }">
            <div class="update-time">
              <i class="el-icon-time" />
              <span>{{ row.lastUpdate ? new Date(row.lastUpdate).toLocaleString() : '-' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="产品ID" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.goodsInfo.productId }}</span>
          </template>
        </el-table-column>

        <el-table-column 
          label="图片" 
          width="120" 
          align="center"
        >
          <template #default="{ row }">
            <div class="image-preview">
              <img
                v-if="row.images && row.images.image_p && row.images.image_p.length"
                :src="row.images.image_p[0]"
                class="product-image"
                alt="商品图片"
              >
              <el-empty v-else description="无图片" :image-size="32" />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="SPG ID" width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.spgId || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="商品分类" width="200" align="center">
          <template #default="{ row }">
            <div v-if="row.goodsInfo">
              <p>ID: {{ row.goodsInfo.productId || '-' }}</p>
              <p>缅甸名: {{ row.goodsInfo.productMmName || '-' }}</p>
              <p>泰语名: {{ row.goodsInfo.productDName || '-' }}</p>
            </div>
            <span v-else>无分类信息</span>
          </template>
        </el-table-column>

        <el-table-column 
          label="操作" 
          width="160" 
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-edit"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="info"
                size="mini"
                icon="el-icon-view"
                @click="handleDetail(row)"
              >
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :current-page="listQuery.page"
      :page-size="listQuery.limit"
      :total="total"
      style="margin-top: 20px;"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-alert
      v-if="error"
      :title="error.message"
      type="error"
      show-icon
      style="margin-top: 20px;"
    />
  </div>
</template>

<script>
import { goodsClient } from '@/apolloClient'
import gql from 'graphql-tag'

export default {
  name: 'GoodsList',
  data() {
    return {
      categoryTree: [],
      selectedLevel1: null,
      selectedLevel2: null,
      selectedLevel3: null,
      skuList: [],
      listLoading: false,
      error: null,
      listQuery: {
        page: 1,
        limit: 10,
        sort: {
          order: 'desc',
          field: 'goodsQty'
        }
      },
      total: 0,
      expandAll: false,
      collapseAll: false,
      dialogVisible: false,
      dialogTitle: '编辑商品',
      activeTab: 'basic',
      currentGoods: {
        goodsInfo: {
          productMmName: '',
          productIdName: '',
          productDName: ''
        },
        goodsPrice: '0',
        goodsQty: '0',
        goodsParam: '',
        guiGeParam: '',
        images: {
          image_p: []
        },
        skuId: '',
        spuId: '',
        goodsId: 0,
        spgId: 0,
        brandId: 0
      },
      imageList: [],
      saving: false,
      rules: {
        'goodsInfo.productMmName': [
          { required: true, message: '请输入缅文商品名称', trigger: 'blur' }
        ],
        'goodsInfo.productIdName': [
          { required: true, message: '请输入英文商品名称', trigger: 'blur' }
        ],
        goodsPrice: [
          { required: true, message: '请输入商品价格', trigger: 'blur' }
        ],
        goodsQty: [
          { required: true, message: '请输入库存数量', trigger: 'blur' }
        ],
        brandId: [
          { required: true, message: '请选择品牌', trigger: 'change' }
        ]
      },
      categorySpecOptions: [],
      selectedSpecOptions: {},
      salesSpecs: [],
      displaySpecs: [],
      brandOptions: []
    }
  },
  computed: {
    level1Options() {
      return this.categoryTree
    },
    level2Options() {
      return this.selectedLevel1
        ? this.categoryTree.find(c => c.categoryId === this.selectedLevel1)?.children || []
        : []
    },
    level3Options() {
      return this.selectedLevel2
        ? this.level2Options.find(c => c.categoryId === this.selectedLevel2)?.children || []
        : []
    },
    salesSpecs() {
      return this.categorySpecOptions.filter(spec => spec.specType === 1)
    },
    displaySpecs() {
      return this.categorySpecOptions.filter(spec => spec.specType === 2)
    }
  },
  created() {
    this.fetchCategories()
    this.fetchBrands()
    this.fetchSkus()
  },
  methods: {
    async fetchCategories() {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query GetCategories($page: Int, $pageSize: Int) {
              goodsCategories(page: $page, pageSize: $pageSize) {
                items {
                  categoryId
                  mmName
                  parentId
                }
                page
                pageSize
                total
              }
            }
          `,
          variables: {
            page: 1,
            pageSize: 1000
          }
        })
        this.categoryTree = this.buildCategoryTree(data.goodsCategories.items)
      } catch (error) {
        console.error('获取分类失败:', error)
      }
    },
    buildCategoryTree(categories) {
      const tree = []
      const map = {}

      categories.forEach(category => {
        map[category.categoryId] = { ...category, children: [] }
      })

      categories.forEach(category => {
        if (category.parentId && map[category.parentId]) {
          map[category.parentId].children.push(map[category.categoryId])
        } else {
          tree.push(map[category.categoryId])
        }
      })

      return tree
    },
    handleLevel1Change() {
      this.selectedLevel2 = null
      this.selectedLevel3 = null
      this.applyCategoryFilter()
    },
    handleLevel2Change() {
      this.selectedLevel3 = null
      this.applyCategoryFilter()
    },
    handleFilterChange() {
      this.applyCategoryFilter()
    },
    applyCategoryFilter() {
      this.listQuery.page = 1
      this.fetchSkus()
    },
    async fetchSkus() {
      try {
        this.listLoading = true
        this.error = null

        const { data } = await goodsClient.query({
          query: gql`
            query GetSkus(
              $page: Int!, 
              $limit: Int!,
              $filter: GoodsSkuFilter,
              $sort: SkuSortInput
            ) {
              allSkus(
                page: $page, 
                limit: $limit,
                filter: $filter,
                sort: $sort
              ) {
                items {
                  autoId
                  skuId
                  brandId
                  goodsId
                  goodsParam
                  goodsPrice
                  goodsQty
                  guiGeParam
                  images
                  spgId
                  spuId
                  specData
                  lastUpdate
                  goodsInfo {
                    productId
                    productDName
                    productIdName
                    productMmName
                  }
                }
                pagination {
                  page
                  totalPages
                  total
                  pageSize
                }
              }
            }
          `,
          variables: {
            page: this.listQuery.page,
            limit: this.listQuery.limit,
            filter: {
              spgId: this.selectedLevel3 ? parseInt(this.selectedLevel3) : null
            },
            sort: {
              field: this.listQuery.sort.field,
              order: this.listQuery.sort.order.toUpperCase()
            }
          },
          fetchPolicy: 'no-cache',
          errorPolicy: 'all'
        })

        this.skuList = data.allSkus.items.filter(item =>
          item.goodsInfo &&
          item.goodsInfo.productId
        )
        this.total = data.allSkus.pagination.total

        console.log('获取到的商品数据:', this.skuList)
      } catch (err) {
        console.error('获取商品数据失败:', err)
        this.error = err
        this.$notify.error({
          title: '错误',
          message: '商品数据加载失败'
        })
      } finally {
        this.listLoading = false
      }
    },
    handleDetail(row) {
      this.$router.push({
        name: 'GoodsDetail',
        params: { id: row.autoId }
      })
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.fetchSkus()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchSkus()
    },
    handleSortChange({ prop, order }) {
      // 处理字段映射
      const fieldMap = {
        'goodsInfo.productMmName': 'productMmName',
        'goodsQty': 'goodsQty',
        'brandId': 'brandId',
        'skuId': 'skuId',
        'lastUpdate': 'lastUpdate'
      }

      this.listQuery.sort = {
        field: fieldMap[prop] || prop,
        order: order === 'ascending' ? 'asc' : 'desc'
      }

      // 重置到第一页
      this.listQuery.page = 1
      this.fetchSkus()
    },
    async fetchBrands() {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query AllBrands {
              allBrands {
                brandId
                brandMmName
                brandEnName
                logoUrl
                status
              }
            }
          `
        })
        this.brandOptions = data.allBrands
      } catch (error) {
        console.error('获取品牌列表失败:', error)
      }
    },
    async handleEdit(row) {
      try {
        await this.fetchBrands() // 获取品牌列表
        // 确保images是数组类型
        let images = {
          image_p: []
        }
        try {
          if (row.images?.image_p && Array.isArray(row.images.image_p)) {
            images = row.images
          } else if (typeof row.images === 'string') {
            const parsedImages = JSON.parse(row.images)
            images = {
              image_p: Array.isArray(parsedImages) ? parsedImages : parsedImages.image_p || []
            }
          } else if (Array.isArray(row.images)) {
            images = {
              image_p: row.images
            }
          }
        } catch (e) {
          console.warn('解析图片数据失败:', e)
          images = { image_p: [] }
        }

        // 获取分类规格选项
        if (row.spgId) {
          const { data } = await goodsClient.query({
            query: gql`
              query GetCategorySpecs($categoryId: Int!) {
                getCategorySpecs(categoryId: $categoryId) {
                  categoryId
                  createdAt
                  isRequired
                  sortOrder
                  specId
                  specName
                  specType
                  inputType
                  GetCategorySpecOptionsInfo {
                    categoryCnName
                    categoryId
                    categoryName
                    optionId
                    optionValue
                    specId
                  }
                }
              }
            `,
            variables: {
              categoryId: parseInt(row.spgId)
            }
          })

          // 重新组织数据结构
          this.categorySpecOptions = data.getCategorySpecs
            .map(spec => ({
              specId: spec.specId,
              specName: spec.specName || '',
              specType: spec.specType || 1,
              inputType: spec.inputType || 1,
              isRequired: spec.isRequired,
              sortOrder: spec.sortOrder,
              options: spec.GetCategorySpecOptionsInfo.map(option => ({
                optionId: option.optionId,
                optionValue: option.optionValue
              }))
            }))
            .sort((a, b) => a.sortOrder - b.sortOrder) // 按 sortOrder 排序

          console.log('分类规格选项:', this.categorySpecOptions)

          this.salesSpecs = this.categorySpecOptions.filter(spec => spec.specType === 1)
          this.displaySpecs = this.categorySpecOptions.filter(spec => spec.specType === 2)

          // 解析已有的规格参数
          this.selectedSpecOptions = {}
          if (row.specData) {
            const specData = typeof row.specData === 'string'
              ? JSON.parse(row.specData)
              : row.specData

            // 设置已选择的规格选项
            specData.specs.forEach(spec => {
              const specOption = this.categorySpecOptions.find(s => s.specId === spec.specId)
              if (specOption?.inputType === 3) {
                // 对于输入框类型，直接保存输入值
                this.selectedSpecOptions[spec.specId] = spec.value
              } else if (specOption?.inputType === 1) {
                // 对于单选类型，保存单个选项ID
                this.selectedSpecOptions[spec.specId] = spec.options[0]
              } else if (specOption?.inputType === 2) {
                // 对于多选类型，保存选项ID数组
                this.selectedSpecOptions[spec.specId] = spec.options
              } else if (specOption?.inputType === 4) {
                // 对于颜色选择器，直接保存颜色值
                this.selectedSpecOptions[spec.specId] = spec.value
              }
            })

            this.currentGoods.guiGeParam = specData.display || ''
          } else if (typeof row.goodsParam === 'string' && row.goodsParam) {
            try {
              // 尝试解析 JSON 格式的规格参数
              const params = JSON.parse(row.goodsParam)
              Object.entries(params).forEach(([specId, values]) => {
                if (Array.isArray(values)) {
                  this.selectedSpecOptions[specId] = values
                }
              })
            } catch (e) {
              // 如果不是 JSON 格式，尝试解析文本格式
              const params = row.goodsParam.split('\n')
              params.forEach(param => {
                const [specName, values] = param.split(':').map(s => s.trim())
                const spec = this.categorySpecOptions.find(s => s.specName === specName)
                if (spec) {
                  const optionIds = values.split(',')
                    .map(v => v.trim())
                    .map(value => {
                      const option = spec.options?.find(o => o.optionValue === value)
                      return option?.optionId
                    })
                    .filter(Boolean)
                  if (optionIds.length) {
                    this.selectedSpecOptions[spec.specId] = optionIds
                  }
                }
              })
            }
          } else if (row.goodsParam && typeof row.goodsParam === 'object') {
            // 如果已经是对象格式，直接使用
            this.selectedSpecOptions = { ...row.goodsParam }
          }
        }

        this.currentGoods = {
          ...row,
          images: images,
          goodsInfo: {
            productMmName: row.goodsInfo?.productMmName || '',
            productIdName: row.goodsInfo?.productIdName || '',
            productDName: row.goodsInfo?.productDName || ''
          },
          goodsPrice: String(row.goodsPrice || '0'),
          goodsQty: String(row.goodsQty || '0'),
          brandId: parseInt(row.brandId) || null
        }

        // 处理图片列表显示
        this.imageList = images.image_p.map(url => ({
          url,
          name: url.split('/').pop()
        }))

        console.log('当前图片列表:', this.imageList)
        console.log('当前商品图片:', this.currentGoods.images)

        this.dialogVisible = true
        this.dialogTitle = '编辑商品'
      } catch (error) {
        console.error('编辑商品失败:', error)
        this.$notify.error({
          title: '错误',
          message: '加载商品信息失败'
        })
      }
    },
    handleDialogClose(done) {
      if (this.saving) {
        return
      }
      this.$confirm('确认关闭？未保存的数据将会丢失')
        .then(() => {
          done()
          this.resetForm()
        })
        .catch(() => {})
    },
    resetForm() {
      this.currentGoods = {
        goodsInfo: {
          productMmName: '',
          productIdName: '',
          productDName: ''
        },
        goodsPrice: '0',
        goodsQty: '0',
        goodsParam: '',
        guiGeParam: '',
        images: {
          image_p: []
        },
        skuId: '',
        spuId: '',
        goodsId: 0,
        spgId: 0,
        brandId: null
      }
      this.imageList = []
      this.activeTab = 'basic'
      if (this.$refs.goodsForm) {
        this.$refs.goodsForm.resetFields()
      }
    },
    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    async handleImageUpload({ file }) {
      console.log('上传的文件:', file)
      try {
        this.saving = true
        const formData = new FormData()
        formData.append('file', file)

        // 调用文件上传API
        const response = await fetch('http://192.168.1.11:8091/shwethe_file_path/api/v1/file_path/test', {
          method: 'POST',
          body: formData
        })

        if (!response.ok) {
          throw new Error('图片上传失败')
        }

        const data = await response.json()
        const imageUrl = data.url

        console.log('上传的图片URL:', imageUrl)

        // 确保images对象存在
        if (!this.currentGoods.images || !this.currentGoods.images.image_p) {
          this.currentGoods.images = {
            image_p: []
          }
        }

        // 更新图片列表
        this.currentGoods.images.image_p.push(imageUrl)

        // 更新显示列表
        this.imageList = this.currentGoods.images.image_p.map(url => ({
          url,
          name: url.split('/').pop()
        }))

        console.log('当前商品图片:', this.currentGoods.images)

        this.$notify({
          title: '成功',
          message: '图片上传成功',
          type: 'success'
        })

        // 返回上传结果给 el-upload 组件
        return {
          url: imageUrl,
          name: imageUrl.split('/').pop()
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        this.$notify.error({
          title: '错误',
          message: '图片上传失败: ' + error.message
        })
        throw error
      } finally {
        this.saving = false
      }
    },
    handleRemoveImage(file) {
      const index = this.imageList.findIndex(item => item.url === file.url)
      if (index > -1) {
        this.currentGoods.images.image_p.splice(index, 1)
        this.imageList.splice(index, 1)
      }
    },
    handleExceed() {
      this.$notify.warning({
        title: '警告',
        message: '最多只能上传5张图片'
      })
    },
    async handleSave() {
      try {
        await this.$refs.goodsForm.validate()
        this.saving = true

        const mutation = gql`
          mutation CreateSku($input: GoodsSkuInput!) {
            createSku(skuData: $input) {
              autoId
              skuId
              spgId
              goodsId
              brandId
              specData
              images
            }
          }
        `

        // 确保发送到服务器的images是正确的格式
        const images = {
          image_p: this.currentGoods.images?.image_p || []
        }

        // 处理规格数据
        const specData = {
          specs: Object.entries(this.selectedSpecOptions).map(([specId, value]) => {
            const spec = this.categorySpecOptions.find(s => s.specId === parseInt(specId))
            if (!spec) return null

            if (spec.inputType === 3) {
              // 输入框类型
              return {
                specId: parseInt(specId),
                specType: spec.specType,
                value: value
              }
            } else {
              // 选择器类型
              return {
                specId: parseInt(specId),
                specType: spec.specType,
                options: Array.isArray(value) ? value : [value]
              }
            }
          }).filter(Boolean),
          display: this.currentGoods.guiGeParam
        }

        await goodsClient.mutate({
          mutation,
          variables: {
            input: {
              skuId: this.currentGoods.skuId || '',
              spuId: this.currentGoods.spuId || '',
              goodsId: parseInt(this.currentGoods.goodsId || 0),
              spgId: parseInt(this.currentGoods.spgId || 0),
              goodsPrice: parseFloat(this.currentGoods.goodsPrice),
              goodsQty: parseFloat(this.currentGoods.goodsQty),
              brandId: parseInt(this.currentGoods.brandId),
              specData: specData,
              images: images
            }
          }
        })

        this.$message.success('保存成功')
        this.dialogVisible = false
        this.fetchSkus() // 刷新列表
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },
    handleExpandAll() {
      this.expandAll = true
      this.collapseAll = false
    },
    handleCollapseAll() {
      this.expandAll = false
      this.collapseAll = true
    },
    isOptionSelected(specId, optionId) {
      return this.selectedSpecOptions[specId]?.includes(optionId)
    },
    toggleOption(specId, optionId) {
      if (!this.selectedSpecOptions[specId]) {
        this.$set(this.selectedSpecOptions, specId, [])
      }

      const index = this.selectedSpecOptions[specId].indexOf(optionId)
      if (index > -1) {
        this.selectedSpecOptions[specId].splice(index, 1)
      } else {
        this.selectedSpecOptions[specId].push(optionId)
      }

      // 更新规格参数文本
      this.updateSpecParams()
    },
    updateSpecParams() {
      // 直接保存为 JSON 格式
      this.currentGoods.goodsParam = JSON.stringify(this.selectedSpecOptions)

      // 同时更新规格值显示
      const specValues = Object.entries(this.selectedSpecOptions).map(([specId, value]) => {
        const spec = this.categorySpecOptions.find(s => s.specId === parseInt(specId))
        if (!spec) return null

        let displayValue
        if (spec.inputType === 1 || spec.inputType === 2) {
          // 对于单选和多选，需要查找选项的值
          const options = Array.isArray(value) ? value : [value]
          displayValue = options.map(optionId => {
            const option = spec.options?.find(o => o.optionId === optionId)
            return option?.optionValue
          }).filter(Boolean).join(', ')
        } else {
          // 对于输入框和颜色选择器，直接使用值
          displayValue = value
        }

        return `${spec.specName}: ${displayValue}`
      }).filter(Boolean)

      this.currentGoods.guiGeParam = specValues.join('\n')
    },
    handleSpecChange(specId) {
      this.updateSpecParams()
    },
    getBrandInfo(brandId) {
      return this.brandOptions.find(brand => brand.brandId === brandId)
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
}

.box-card :deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-icon {
  font-size: 24px;
  color: #409EFF;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selector-item {
  min-width: 180px;
}

.selector-item :deep(.el-input__inner) {
  border-radius: 4px;
}

.control-button {
  padding: 8px 16px;
  border-radius: 4px;
}

.table-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.05);
}

.data-table {
  margin: 0;
}

.data-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.data-table :deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
  padding: 12px 0;
}

.data-table :deep(.el-table__body td) {
  padding: 12px 0;
}

.data-table :deep(.el-table--border) {
  border-radius: 4px;
  overflow: hidden;
}

.data-table :deep(.el-table__row:hover > td) {
  background-color: #f5f7fa;
}

.id-column {
  color: #909399;
  font-family: monospace;
}

.product-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mm-name {
  font-weight: 500;
  color: #303133;
}

.en-name {
  font-size: 12px;
  color: #909399;
}

.qty-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.qty-tag.low {
  background-color: #fef0f0;
  color: #f56c6c;
}

.qty-tag.medium {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.qty-tag.high {
  background-color: #f0f9eb;
  color: #67c23a;
}

.image-preview {
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  padding: 4px;
}

.update-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  padding: 6px 12px;
}

.brand-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.brand-name {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.brand-name .mm-name {
  font-weight: 500;
  color: #303133;
}

.brand-name .en-name {
  font-size: 12px;
  color: #909399;
}

.no-brand {
  color: #909399;
  font-size: 12px;
}
</style>

