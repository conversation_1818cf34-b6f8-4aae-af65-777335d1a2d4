<template>
  <div class="app-container">
    <goods-list />
  </div>
</template>

<script>
import GoodsList from './components/goodsList.vue'
export default {
  name: 'EmployeeIndex',
  components: {
    GoodsList
  },
  data() {
    return {
      activeTab: 'employee', // 当前激活的标签页
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
    }
  },
  methods: {
    handleFilter(query) {
      this.listQuery = { ...query }
      this.getList()
    },
    // 新增标签切换处理
    handleTabClick(tab) {
      // 切换标签时重置查询条件
      this.listQuery = {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
      this.getList()
    },
    async getList() {
      // 根据当前激活的标签调用不同的API
      if (this.activeTab === 'benefit') {
        // 调用福利管理接口
      } else {
        // 调用员工管理接口
      }
    }
  }
}
</script>
