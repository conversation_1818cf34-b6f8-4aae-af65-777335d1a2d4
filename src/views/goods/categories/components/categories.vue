<template>
  <div class="app-container">

    <!-- 添加规格选项选择区域 -->

    <!-- 新增表单卡片 -->
    <el-card class="box-card form-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="header-title">{{ formTitle }}</span>
      </div>

      <el-form
        ref="categoryForm"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="中文名称" prop="cnName">
              <el-input v-model="form.cnName" placeholder="中文名称ထည့်ပါ" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="缅甸语名称" prop="mmName">
              <el-input v-model="form.mmName" placeholder="缅甸语名称ထည့်ပါ" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="上级分类" prop="parentId">
          <el-cascader
            v-model="form.parentId"
            :options="categoryTree"
            :props="cascaderProps"
            :show-all-levels="false"
            clearable
            placeholder="上级分类ကိုရွေးပါ"
          />
        </el-form-item>

        <el-form-item label="是否父级" prop="ifParent">
          <el-switch
            v-model="form.ifParent"
            active-text="ဟုတ်ကဲ့"
            inactive-text="မဟုတ်ပါ"
            active-value="1"
            inactive-value="0"
          />
        </el-form-item>

        <el-form-item class="form-actions">
          <el-button type="primary" @click="submitForm">သိမ်းဆည်းပါ</el-button>
          <el-button @click="resetForm">ပြန်လည်သတ်မှတ်ပါ</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="box-card form-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="header-title">ကုန်ပစ္စည်းအမျိုးအစားသတ်မှတ်ချက်စီမံခန့်ခွဲမှု</span>
        <el-button
          type="primary"
          size="mini"
          @click="handleAddSpec"
        >
          <i class="el-icon-plus" /> သတ်မှတ်ချက်အသစ်ထည့်ပါ
        </el-button>
      </div>

      <el-table
        :data="specList"
        border
        style="width: 100%"
      >
        <el-table-column
          prop="specName"
          label="သတ်မှတ်ချက်အမည်"
          width="180"
        />
        <el-table-column
          label="ရွေးချယ်စရာများ"
          min-width="200"
        >
          <template slot-scope="{row}">
            <el-tag
              v-for="option in row.options"
              :key="option.optionId"
              size="mini"
              :type="option.isDefault ? 'success' : ''"
              style="margin-right: 5px; margin-bottom: 5px"
            >
              {{ option.optionValue }}
              <span v-if="option.isDefault" style="margin-left: 4px">(ပုံသေ)</span>
            </el-tag>
            <el-tag
              v-if="!row.options || row.options.length === 0"
              type="info"
              size="mini"
            >
              ရွေးချယ်စရာမရှိသေးပါ
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="unit"
          label="သတ်မှတ်ချက်တန်ဖိုးများ"
        >
          <template slot-scope="{row}">
            <el-tag
              v-for="(value, index) in row.unit"
              :key="index"
              size="mini"
              style="margin-right: 5px"
            >
              {{ value }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="လုပ်ဆောင်ချက်များ"
          width="120"
        >
          <template slot-scope="{row}">
            <el-button
              type="text"
              size="mini"
              @click="showDetail(row)"
            >အသေးစိတ်ကြည့်ရန်</el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleAddOption(row)"
            >ရွေးချယ်စရာစီမံခန့်ခွဲမှု</el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleEditSpec(row)"
            >ပြင်ဆင်ပါ</el-button>
            <el-button
              type="text"
              size="mini"
              style="color: #F56C6C"
              @click="handleDeleteSpec(row.specId)"
            >ဖျက်ပါ</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="specPagination.page"
        :page-size="specPagination.pageSize"
        :total="specPagination.total"
        layout="prev, pager, next, sizes, total"
        :page-sizes="[10, 20, 50]"
        style="margin-top: 20px;"
        @current-change="handleSpecPageChange"
        @size-change="handleSpecSizeChange"
      />
    </el-card>

    <el-dialog
      :title="currentSpec.specName ? 'သတ်မှတ်ချက်ပြင်ဆင်ခြင်း' : 'သတ်မှတ်ချက်အသစ်ထည့်ခြင်း'"
      :visible.sync="specDialogVisible"
      width="600px"
    >
      <el-form ref="specForm" :model="currentSpec" label-width="160px">
        <el-form-item
          label="သတ်မှတ်ချက်အမည်"
          prop="specName"
          :rules="[{ required: true, message: 'သတ်မှတ်ချက်အမည်ထည့်ပါ', trigger: 'blur' }]"
        >
          <el-input v-model="currentSpec.specName" placeholder="ဥပမာ - အရောင်၊ အရွယ်အစား" />
        </el-form-item>

        <el-form-item label="သတ်မှတ်ချက်အမျိုးအစား" prop="specType">
          <el-select v-model="currentSpec.specType" placeholder="သတ်မှတ်ချက်အမျိုးအစားကိုရွေးပါ">
            <el-option
              v-for="item in specTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="ထည့်သွင်းမှုအမျိုးအစား" prop="inputType">
          <el-select v-model="currentSpec.inputType" placeholder="ထည့်သွင်းမှုအမျိုးအစားကိုရွေးပါ">
            <el-option
              v-for="item in inputTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="တန်ဖိုးအမျိုးအစား" prop="valueType">
          <el-select v-model="currentSpec.valueType" placeholder="တန်ဖိုးအမျိုးအစားကိုရွေးပါ">
            <el-option
              v-for="item in valueTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="ယူနစ်" prop="unit">
          <el-select v-model="currentSpec.unit" multiple placeholder="ယူနစ်ကိုရွေးပါ">
            <el-option
              v-for="item in unitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="သတ်မှတ်ချက်တန်ဖိုးများ"
          prop="values"
          :rules="[{ validator: validateValues, trigger: 'blur' }]"
        >
          <div v-for="(value, index) in currentSpec.values" :key="index" class="value-item">
            <el-input
              v-model="currentSpec.values[index]"
              :placeholder="`သတ်မှတ်ချက်တန်ဖိုး ${index + 1}`"
              style="width: 80%; margin-right: 10px"
            />
            <el-button
              type="danger"
              icon="el-icon-delete"
              circle
              size="mini"
              @click="removeValue(index)"
            />
          </div>
          <el-button
            type="primary"
            plain
            size="small"
            @click="addValue"
          >
            တန်ဖိုးထည့်ပါ
          </el-button>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="specDialogVisible = false">ပယ်ဖျက်ပါ</el-button>
        <el-button type="primary" @click="saveSpec">သိမ်းဆည်းပါ</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="规格详情"
      :visible.sync="detailDialogVisible"
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="规格ID">{{ currentDetail.specId }}</el-descriptions-item>
        <el-descriptions-item label="规格名称">{{ currentDetail.specName }}</el-descriptions-item>
        <el-descriptions-item label="规格类型">{{ currentDetail.specType }}</el-descriptions-item>
        <el-descriptions-item label="输入类型">{{ currentDetail.inputType }}</el-descriptions-item>
        <el-descriptions-item label="值类型">{{ currentDetail.valueType }}</el-descriptions-item>
        <el-descriptions-item label="单位">
          <el-tag
            v-for="(unit, index) in currentDetail.unit"
            :key="index"
            size="mini"
            style="margin-right: 5px"
          >
            {{ unit }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentDetail.createdAt }}</el-descriptions-item>
      </el-descriptions>

      <span slot="footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 添加选项管理对话框 -->
    <el-dialog
      :title="currentOption.optionId ? 'ရွေးချယ်စရာပြင်ဆင်ခြင်း' : 'ရွေးချယ်စရာအသစ်ထည့်ခြင်း'"
      :visible.sync="optionDialogVisible"
      width="500px"
    >
      <el-form ref="optionForm" :model="currentOption" label-width="120px">
        <el-form-item
          label="ရွေးချယ်စရာတန်ဖိုး"
          prop="optionValue"
          :rules="[{ required: true, message: 'ရွေးချယ်စရာတန်ဖိုးထည့်ပါ', trigger: 'blur' }]"
        >
          <el-input
            v-model="currentOption.optionValue"
            placeholder="ဥပမာ - XL၊ အနီရောင်"
          />
        </el-form-item>

        <el-form-item label="အစီအစဉ်" prop="sortOrder">
          <el-input-number
            v-model="currentOption.sortOrder"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="ပုံသေတန်ဖိုး" prop="isDefault">
          <el-switch
            v-model="currentOption.isDefault"
            active-text="ဟုတ်ကဲ့"
            inactive-text="မဟုတ်ပါ"
          />
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="optionDialogVisible = false">ပယ်ဖျက်ပါ</el-button>
        <el-button type="primary" @click="saveOption">သိမ်းဆည်းပါ</el-button>
      </span>
    </el-dialog>

    <el-pagination
      background
      :current-page="categoryPagination.page"
      :page-size="categoryPagination.pageSize"
      :total="categoryPagination.total"
      layout="prev, pager, next, sizes, total"
      :page-sizes="[100, 200, 500, 1000]"
      style="margin-top: 20px;"
      @current-change="handleCategoryPageChange"
      @size-change="handleCategorySizeChange"
    />

    <!-- 添加调试信息显示区块（可选） -->
    <div v-if="selectedLevel3" class="debug-info">
      <h4>调试信息（分类ID: {{ selectedLevel3 }}）</h4>
      <pre>{{ categorySpecOptions }}</pre>
    </div>
  </div>
</template>

<script>
import { goodsClient } from '@/apolloClient'
import gql from 'graphql-tag'

export default {
  name: 'GoodsCategories',
  data() {
    return {
      loading: false,
      categoryTree: [],
      defaultProps: {
        children: 'children',
        label: 'cnName'
      },
      isExpanded: true,
      selectedLevel1: null,
      selectedLevel2: null,
      selectedLevel3: null,
      form: {
        categoryId: null,
        cnName: '',
        mmName: '',
        parentId: [],
        ifParent: '0'
      },
      rules: {
        cnName: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
          { max: 50, message: '长度不超过50个字符', trigger: 'blur' }
        ],
        mmName: [
          { required: true, message: '请输入缅甸语名称', trigger: 'blur' },
          { max: 100, message: '长度不超过100个字符', trigger: 'blur' }
        ]
      },
      cascaderProps: {
        value: 'categoryId',
        label: (item) => `${item.cnName} / ${item.mmName}`,
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      specList: [],
      specDialogVisible: false,
      currentSpec: {
        specName: '',
        specType: 'SALES_SPEC',
        inputType: 'SINGLE_SELECT',
        valueType: 'TEXT',
        unit: [],
        values: []
      },
      specTypeOptions: [
        { value: 'SALES_SPEC', label: 'ရောင်းချသတ်မှတ်ချက်' },
        { value: 'DISPLAY_PARAM', label: 'ပြသသတ်မှတ်ချက်' }
      ],
      inputTypeOptions: [
        { value: 'SINGLE_SELECT', label: 'တစ်ခုရွေးချယ်ပါ' },
        { value: 'MULTIPLE_SELECT', label: 'အများကြီးရွေးချယ်ပါ' },
        { value: 'INPUT_BOX', label: 'ထည့်သွင်းသတ်မှတ်ချက်' },
        { value: 'COLOR_SELECT', label: 'အရောင်ရွေးချယ်ပါ' }
      ],
      valueTypeOptions: [
        { value: 'TEXT', label: 'စာသား' },
        { value: 'NUMBER', label: 'နံပါတ်' },
        { value: 'DATE', label: 'ရက်စွဲ' }
      ],
      unitOptions: [
        { value: 'KG', label: 'ကီလိုဂရမ်' },
        { value: 'G', label: 'ဂရမ်' },
        { value: 'L', label: 'လီတာ' },
        { value: 'ML', label: 'မီလီလီတာ' },
        { value: 'CM', label: 'စင်တီမီတာ' },
        { value: 'M', label: 'မီတာ' }
      ],
      specPagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      specFilter: {
        specType: 'SALES_SPEC',
        searchTerm: ''
      },
      detailDialogVisible: false,
      currentDetail: {},
      optionDialogVisible: false,
      currentOption: {
        specId: null,
        optionId: null,
        optionValue: '',
        sortOrder: 1,
        isDefault: false
      },
      selectedOptions: {}, // 存储每个规格的选中选项
      savingOptions: false,
      categorySpecOptions: [], // 存储分类已关联的规格选项
      categoryPagination: {
        page: 1,
        pageSize: 1000,
        total: 0
      }
    }
  },
  computed: {
    level1Options() {
      return this.categoryTree
    },
    level2Options() {
      if (!this.selectedLevel1) return []
      const level1 = this.categoryTree.find(
        item => item.categoryId === this.selectedLevel1
      )
      return level1?.children || []
    },
    level3Options() {
      if (!this.selectedLevel2) return []
      const level2 = this.level2Options.find(
        item => item.categoryId === this.selectedLevel2
      )
      return level2?.children || []
    },
    formTitle() {
      return this.form.categoryId ? '编辑分类' : '新增分类'
    }
  },
  watch: {
    selectedLevel3(newVal) {
      if (newVal) {
        this.selectedOptions = {} // 移除这行，因为我们会从接口加载数据
        this.fetchSpecsPaginated()
        this.fetchCategorySpecOptions(newVal)
      } else {
        this.categorySpecOptions = []
        this.selectedOptions = {}
      }
    }
  },
  created() {
    this.fetchCategories()
    this.fetchSpecsPaginated()
  },
  methods: {
    async fetchCategories() {
      try {
        this.loading = true
        const { data } = await goodsClient.query({
          query: gql`
            query GetCategories($page: Int, $pageSize: Int) {
              goodsCategories(page: $page, pageSize: $pageSize) {
                items {
                  categoryId
                  mmName
                  ifParent
                  parentId
                    cnName
                }
                page
                pageSize
                total
              }
            }
          `,
          variables: {
            page: 1,
            pageSize: 1000 // 获取足够多的分类以构建树形结构
          }
        })

        if (data.goodsCategories) {
          this.categoryTree = this.buildCategoryTree(data.goodsCategories.items)
          this.categoryPagination = {
            page: data.goodsCategories.page,
            pageSize: data.goodsCategories.pageSize,
            total: data.goodsCategories.total
          }
        }
      } catch (error) {
        console.error('获取分类数据失败:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'အမျိုးအစားစာရင်းရယူခြင်းမအောင်မြင်ပါ'
        })
      } finally {
        this.loading = false
      }
    },

    expandAll() {
      this.isExpanded = true
    },

    collapseAll() {
      this.isExpanded = false
    },

    filterNode(value, data) {
      if (this.selectedLevel1 === null && this.selectedLevel2 === null && this.selectedLevel3 === null) return true
      return (
        (this.selectedLevel1 === null || data.categoryId === this.selectedLevel1) &&
        (this.selectedLevel2 === null || data.categoryId === this.selectedLevel2) &&
        (this.selectedLevel3 === null || data.categoryId === this.selectedLevel3)
      )
    },

    handleLevel1Change(val) {
      this.selectedLevel2 = null
      this.selectedLevel3 = null
    },
    handleLevel2Change(val) {
      this.selectedLevel3 = null
    },
    async handleFilterChange() {
      if (this.selectedLevel3) {
        await this.fetchCategorySpecOptions(this.selectedLevel3)
      }
    },
    submitForm() {
      this.$refs.categoryForm.validate(async valid => {
        if (!valid) return

        try {
          const mutation = this.form.categoryId
            ? gql`mutation UpdateCategory($input: UpdateGoodsCategoryInput!) {
                updateGoodsCategory(input: $input) {
                  categoryId
                }
              }`
            : gql`mutation CreateCategory($input: CreateGoodsCategoryInput!) {
                createGoodsCategory(input: $input) {
                  categoryId
                }
              }`

          const { data } = await goodsClient.mutate({
            mutation,
            variables: {
              input: {
                ...this.form,
                parentId: this.form.parentId?.length
                  ? this.form.parentId[this.form.parentId.length - 1]
                  : 0
              }
            }
          })

          this.$notify.success({
            title: '成功',
            message: '操作成功'
          })

          this.resetForm()
          await this.fetchCategories()
        } catch (error) {
          console.error('操作失败:', error)
          this.$notify.error({
            title: '错误',
            message: '操作失败'
          })
        }
      })
    },

    resetForm() {
      this.$refs.categoryForm.resetFields()
      this.form = {
        categoryId: null,
        cnName: '',
        mmName: '',
        parentId: [],
        ifParent: '0'
      }
    },

    handleEdit(nodeData) {
      this.form = {
        categoryId: nodeData.categoryId,
        cnName: nodeData.cnName,
        mmName: nodeData.mmName,
        parentId: this.getParentIds(nodeData),
        ifParent: nodeData.ifParent ? '1' : '0'
      }
    },

    getParentIds(node) {
      // 需要根据数据结构实现获取父级ID路径的逻辑
    },

    handleAddSpec() {
      this.currentSpec = { specName: '', values: [] }
      this.specDialogVisible = true
    },

    validateValues(rule, value, callback) {
      if (this.currentSpec.values.length === 0) {
        callback(new Error('至少需要添加一个规格值'))
      } else if (this.currentSpec.values.some(v => !v.trim())) {
        callback(new Error('规格值不能为空'))
      } else {
        callback()
      }
    },

    addValue() {
      this.currentSpec.values.push('')
    },

    removeValue(index) {
      this.currentSpec.values.splice(index, 1)
    },

    async getSpecById(specId) {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query GetSpecById($specId: Int!) {
              getSpecById(specId: $specId) {
                createdAt
                inputType
                isSku
                specId
                specName
                specType
                unit
                valueType
              }
            }
          `,
          variables: {
            specId: specId
          }
        })

        if (data.getSpecById) {
          return {
            ...data.getSpecById,
            // 确保unit字段是数组格式
            unit: data.getSpecById.unit ? JSON.parse(data.getSpecById.unit) : []
          }
        }
        return null
      } catch (error) {
        console.error('获取规格详情失败:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'သတ်မှတ်ချက်အသေးစိတ်ရယူခြင်းမအောင်မြင်ပါ'
        })
        return null
      }
    },

    async handleEditSpec(row) {
      try {
        const specDetail = await this.getSpecById(row.specId)
        if (specDetail) {
          this.currentSpec = {
            ...specDetail,
            values: [...row.values] // 保持原有值列表
          }
          this.specDialogVisible = true
        }
      } catch (error) {
        console.error('编辑规格失败:', error)
      }
    },

    async handleDeleteSpec(specId) {
      try {
        await this.$confirm('သတ်မှတ်ချက်ကိုဖျက်မှာသေချာပါသလား？', 'သတိပေးချက်', {
          type: 'warning'
        })

        const { data } = await goodsClient.mutate({
          mutation: gql`
            mutation DeleteSpec($specId: Int!) {
              deleteSpec(specId: $specId) {
                success
              }
            }
          `,
          variables: { specId }
        })

        if (data.deleteSpec.success) {
          this.$notify.success({
            title: 'အောင်မြင်သည်',
            message: 'သတ်မှတ်ချက်ဖျက်ခြင်းအောင်မြင်ပါသည်'
          })
          this.fetchSpecsPaginated()
        }
      } catch (error) {
        console.error('ဖျက်ခြင်းမအောင်မြင်ပါ:', error)
      }
    },

    async upsertSpec() {
      try {
        const { data } = await goodsClient.mutate({
          mutation: gql`
            mutation MyMutation($specData: SpecInput!) {
              upsertSpec(specData: $specData) {
                spec {
                  specId
                  specName
                  specType
                  inputType
                  valueType
                  unit
                  isSku
                  createdAt
                }
                error {
                  message
                  conflictingField
                  existingValue
                  newValue
                }
                __typename
              }
            }
          `,
          variables: {
            specData: {
              specName: this.currentSpec.specName,
              specType: this.currentSpec.specType,
              inputType: this.currentSpec.inputType,
              isSku: false,
              unit: this.currentSpec.unit,
              valueType: this.currentSpec.valueType
            }
          }
        })

        console.log('Mutation result:', data)
        this.$notify.success({
          title: '成功',
          message: '规格保存成功'
        })
      } catch (error) {
        console.error('规格保存失败:', error)
        this.$notify.error({
          title: '错误',
          message: '规格保存失败'
        })
      }
    },

    saveSpec() {
      this.$refs.specForm.validate(valid => {
        if (valid) {
          // 如果是编辑模式，更新对应数据
          if (this.currentSpec.specName) {
            const index = this.specList.findIndex(
              item => item.specName === this.currentSpec.specName
            )
            if (index > -1) {
              this.specList.splice(index, 1, { ...this.currentSpec })
            }
          } else {
            this.specList.push({
              ...this.currentSpec,
              specName: `规格_${Date.now()}` // 生成唯一名称
            })
          }
          this.specDialogVisible = false

          // 调用upsertSpec方法
          this.upsertSpec()
        }
      })
    },

    async fetchSpecsPaginated() {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query GetSpecsPaginated {
              getSpecsPaginated(
                page: 1,
                pageSize: 100
              ) {
                items {
                  specId
                  specName
                  options {
                    isDefault
                    optionId
                    optionValue
                    sortOrder
                    specId
                  }
                  specType
                  inputType
                  valueType
                  unit
                  isSku
                  createdAt
                }
                pagination {
                  page
                  pageSize
                  total
                }
              }
            }
          `
        })

        if (data.getSpecsPaginated) {
          this.specList = data.getSpecsPaginated.items.map(item => ({
            ...item,
            specType: this.convertSpecType(item.specType),
            inputType: this.convertInputType(item.inputType),
            valueType: this.convertValueType(item.valueType),
            unit: Array.isArray(item.unit) ? item.unit : [],
            values: [],
            options: item.options.map(option => ({
              ...option,
              isDefault: option.isDefault === 1
            }))
          }))
          this.specPagination = {
            ...data.getSpecsPaginated.pagination
          }
        }
      } catch (error) {
        console.error('获取规格列表失败:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'သတ်မှတ်ချက်စာရင်းရယူခြင်းမအောင်မြင်ပါ'
        })
      }
    },

    handleSpecPageChange(newPage) {
      this.specPagination.page = newPage
      this.fetchSpecsPaginated()
    },

    handleSpecSizeChange(newSize) {
      this.specPagination.pageSize = newSize
      this.handleSpecPageChange(1)
    },

    convertSpecType(type) {
      const map = { 1: 'SALES_SPEC', 2: 'DISPLAY_PARAM' }
      return map[type] || 'SALES_SPEC'
    },
    convertInputType(type) {
      const map = {
        1: 'SINGLE_SELECT',
        2: 'MULTIPLE_SELECT',
        3: 'INPUT_BOX',
        4: 'COLOR_SELECT'
      }
      return map[type] || 'SINGLE_SELECT'
    },
    convertValueType(type) {
      const map = { 1: 'TEXT', 2: 'NUMBER', 3: 'DATE' }
      return map[type] || 'TEXT'
    },

    buildCategoryTree(items, parentId = 0) {
      return items
        .filter(item => item.parentId === parentId)
        .map(item => ({
          ...item,
          children: this.buildCategoryTree(items, item.categoryId)
        }))
    },

    showDetail(row) {
      this.currentDetail = {
        ...row,
        createdAt: this.formatDate(row.createdAt)
      }
      this.detailDialogVisible = true
    },

    formatDate(isoString) {
      return new Date(isoString).toLocaleString()
    },

    handleAddOption(spec) {
      this.currentOption = {
        specId: spec.specId,
        optionId: null,
        optionValue: '',
        sortOrder: (spec.values?.length || 0) + 1,
        isDefault: false
      }
      this.optionDialogVisible = true
    },

    async saveOption() {
      try {
        const { data } = await goodsClient.mutate({
          mutation: gql`
            mutation UpsertSpecOption($specOptionData: SpecOptionInput!) {
              upsertSpecOption(specOptionData: $specOptionData) {
                success
                specOption {
                  optionId
                  optionValue
                  isDefault
                }
              }
            }
          `,
          variables: {
            specOptionData: {
              specId: this.currentOption.specId,
              optionValue: this.currentOption.optionValue,
              sortOrder: this.currentOption.sortOrder,
              isDefault: this.currentOption.isDefault
            }
          }
        })

        if (data.upsertSpecOption.success) {
          // 如果当前有选中的分类，则进行关联
          this.$notify.success({
            title: 'အောင်မြင်သည်',
            message: 'ရွေးချယ်စရာသိမ်းဆည်းခြင်းအောင်မြင်ပါသည်'
          })
          this.optionDialogVisible = false
          this.fetchSpecsPaginated()
        } else if (data.upsertSpecOption.error) {
          this.$notify.error({
            title: 'အမှား',
            message: data.upsertSpecOption.error.message || 'ရွေးချယ်စရာသိမ်းဆည်းခြင်းမအောင်မြင်ပါ'
          })
        }
      } catch (error) {
        console.error('选项保存失败:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'ရွေးချယ်စရာသိမ်းဆည်းခြင်းမအောင်မြင်ပါ'
        })
      }
    },

    async upsertCategorySpecOption(categoryId, specId, optionId, isAdd = true) {
      try {
        const { data } = await goodsClient.mutate({
          mutation: gql`
            mutation UpsertCategorySpecOption($data: CategorySpecOptionInput!) {
              upsertCategorySpecOption(data: $data) {
                success
                error {
                  conflictingField
                  existingValue
                  message
                  newValue
                }
                categorySpecOption {
                  categoryId
                  optionId
                  specId
                }
              }
            }
          `,
          variables: {
            data: {
              categoryId,
              specId,
              optionId
            }
          }
        })

        if (data.upsertCategorySpecOption.success) {
          this.$notify.success({
            title: 'အောင်မြင်သည်',
            message: isAdd
              ? 'အမျိုးအစားသတ်မှတ်ချက်ရွေးချယ်စရာချိတ်ဆက်ခြင်းအောင်မြင်ပါသည်'
              : 'အမျိုးအစားသတ်မှတ်ချက်ရွေးချယ်စရာဖယ်ရှားခြင်းအောင်မြင်ပါသည်'
          })
          return true
        } else if (data.upsertCategorySpecOption.error) {
          this.$notify.error({
            title: 'အမှား',
            message: data.upsertCategorySpecOption.error.message || 'ချိတ်ဆက်ခြင်းမအောင်မြင်ပါ'
          })
          return false
        }
      } catch (error) {
        console.error('关联保存失败:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'ချိတ်ဆက်ခြင်းမအောင်မြင်ပါ'
        })
        return false
      }
    },

    async handleSaveSpecOptions() {
      try {
        this.savingOptions = true
        console.log('当前分类ID:', this.selectedLevel3)
        console.log('已选规格选项:', this.selectedOptions)

        // 获取当前关联数据时添加日志
        const currentOptions = await this.fetchCategorySpecOptions(this.selectedLevel3)
        console.log('当前关联数据:', currentOptions)

        // 在保存成功后刷新数据
        await this.fetchCategorySpecOptions(this.selectedLevel3)
      } catch (error) {
        console.error('保存规格选项失败:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'သတ်မှတ်ချက်များသိမ်းဆည်းခြင်းမအောင်မြင်ပါ'
        })
      } finally {
        this.savingOptions = false
      }
    },

    // 修改获取分类规格选项方法
    // async fetchCategorySpecOptions(categoryId) {
    //   try {
    //     const { data } = await goodsClient.query({
    //       query: gql`
    //         query GetCategorySpecOptions($categoryId: Int!) {
    //           getCategorySpecOptions(categoryId: $categoryId) {
    //             categoryId
    //             optionId
    //             specId
    //             categoryCnName
    //             categoryName
    //             optionValue
    //             specName
    //             createdAt  // 添加创建时间字段
    //           }
    //         }
    //       `,
    //       variables: { categoryId }
    //     })

    //     if (data.getCategorySpecOptions) {
    //       console.log('获取到的分类规格选项:', data.getCategorySpecOptions)
    //       this.categorySpecOptions = data.getCategorySpecOptions

    //       // 将已关联的选项设置到selectedOptions中
    //       const optionsMap = {}
    //       data.getCategorySpecOptions.forEach(item => {
    //         if (!optionsMap[item.specId]) {
    //           const spec = this.specList.find(s => s.specId === item.specId)
    //           optionsMap[item.specId] = spec?.inputType === 'MULTIPLE_SELECT' ? [] : null
    //         }
    //         if (Array.isArray(optionsMap[item.specId])) {
    //           optionsMap[item.specId].push(item.optionId)
    //         } else {
    //           optionsMap[item.specId] = item.optionId
    //         }
    //       })

    //       // 更新选中状态
    //       this.selectedOptions = optionsMap

    //       // 返回获取到的数据
    //       return data.getCategorySpecOptions
    //     }
    //     return []
    //   } catch (error) {
    //     console.error('获取分类规格选项失败:', error)
    //     this.$notify.error({
    //       title: 'အမှား',
    //       message: 'သတ်မှတ်ချက်များရယူခြင်းမအောင်မြင်ပါ'
    //     })
    //     return []
    //   }
    // },

    getOptionValue(options, optionId) {
      const option = options.find(opt => opt.optionId === optionId)
      return option ? option.optionValue : ''
    },

    async handleCategoryPageChange(newPage) {
      this.categoryPagination.page = newPage
      await this.fetchCategories()
    },

    async handleCategorySizeChange(newSize) {
      this.categoryPagination.pageSize = newSize
      this.categoryPagination.page = 1
      await this.fetchCategories()
    },

    // 检查选项是否已被选择
    isOptionSelected(specId, optionId) {
      return this.categorySpecOptions.some(item =>
        item.specId === specId &&
        item.optionId === optionId
      )
    },

    // 检查选项是否是新选择的
    isNewlySelected(specId, optionId) {
      const currentSelection = this.selectedOptions[specId]
      if (!currentSelection) return false

      const isSelected = Array.isArray(currentSelection)
        ? currentSelection.includes(optionId)
        : currentSelection === optionId

      return isSelected && !this.isOptionSelected(specId, optionId)
    }
  }
}
</script>

<style scoped>
.app-container {
  max-width: 1200px;
  margin: 20px auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-buttons {
  margin-left: auto;
}

.level-selectors {
  display: flex;
  gap: 8px;
  flex-grow: 1;
}

.level-selectors .el-select {
  flex: 1;
  max-width: 200px;
}

.custom-tree-node {
  width: 100%;
  transition: all 0.3s;
}

.custom-tree-node:hover {
  background-color: #f5f7fa;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-left: 3px solid transparent;
  transition: border-color 0.3s;
}

.node-content:hover {
  border-left-color: #409EFF;
}

.category-id {
  min-width: 60px;
  color: #909399;
  font-size: 12px;
  margin-right: 15px;
}

.name-wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.chinese-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.myanmar-name {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.parent-tag {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #67C23A;
  padding: 4px 8px;
  background: #f0f9eb;
  border-radius: 4px;
  margin-left: 15px;
}

.parent-tag i {
  margin-right: 5px;
}

/* 层级缩进样式 */
.el-tree >>> .el-tree-node__content {
  height: auto;
  padding: 5px 0;
}

.el-tree >>> .el-tree-node__content > .el-tree-node__expand-icon {
  padding: 6px;
}

.el-tree >>> .el-tree-node.is-current > .el-tree-node__content {
  background-color: #ecf5ff;
}

/* 新增表单样式 */
.form-card {
  margin-top: 20px;
}

.form-actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

.el-cascader {
  width: 100%;
}

/* 新增规格值输入样式 */
.value-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

/* 详情模态框样式 */
.detail-item {
  margin-bottom: 12px;
}
.detail-label {
  color: #909399;
  min-width: 80px;
  display: inline-block;
}
.detail-value {
  color: #303133;
}

/* 添加规格选项选择区域样式 */
.spec-select-card {
  margin-top: 20px;
}

.spec-select-content {
  padding: 10px;
}

.spec-options-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.spec-form {
  width: 100%;
}

.spec-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.no-category-selected {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.no-category-selected i {
  margin-right: 8px;
  font-size: 16px;
}

.spec-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.selected-values {
  margin-top: 8px;
}

.el-select {
  width: 100%;
}

.el-form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.el-form-item__content {
  width: 100%;
}

.el-select-dropdown__item {
  padding: 0 20px;
}

.option-selected {
  color: #67C23A;
}

.el-table-column--selection .cell {
  padding: 0 10px;
}

.el-button--text {
  margin-left: 8px;
  padding: 0;
}

.el-tag {
  margin-right: 5px;
}

.current-category-info {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.debug-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}
</style>
