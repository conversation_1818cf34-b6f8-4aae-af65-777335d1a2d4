<template>
  <div class="app-container">
    <!-- 头部搜索和操作区 -->
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-shopping-bag-1 header-icon"></i>
          <span class="header-title">အမှတ်တံဆိပ်စီမံခန့်ခွဲမှု</span>
        </div>
        <div class="header-controls">
          <el-button 
            type="primary" 
            size="small"
            class="control-button"
            @click="handleAdd"
          >
            <i class="el-icon-plus" /> အသစ်ထည့်မည်
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 品牌列表 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        :data="brandList"
        border
        fit
        highlight-current-row
        class="data-table"
      >
        <el-table-column 
          label="ID" 
          prop="brandId" 
          align="center" 
          width="80"
        >
          <template #default="{ row }">
            <span class="id-column">{{ row.brandId }}</span>
          </template>
        </el-table-column>

        <el-table-column 
          label="အမှတ်တံဆိပ်အမည်" 
          min-width="180" 
          align="center"
          prop="brandName"
        >
          <template #default="{ row }">
            <div class="brand-name">
              <span class="mm-name">{{ row.brandMmName }}</span>
              <span class="en-name">{{ row.brandEnName }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column 
          label="Logo" 
          width="120" 
          align="center"
        >
          <template #default="{ row }">
            <div class="image-preview">
              <img
                v-if="row.logoUrl"
                :src="row.logoUrl"
                class="brand-logo"
                alt="Logo"
              >
              <el-empty v-else description="Logo မရှိပါ" :image-size="32" />
            </div>
          </template>
        </el-table-column>

        <el-table-column 
          label="အခြေအနေ" 
          width="100" 
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'ACTIVE' ? 'success' : 'info'"
              size="small"
            >
              {{ row.status === 'ACTIVE' ? 'အသုံးပြုနေသည်' : 'ပိတ်ထားသည်' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column 
          label="နောက်ဆုံးပြင်ဆင်ချိန်" 
          width="160" 
          align="center"
          prop="updateTime"
        >
          <template #default="{ row }">
            <div class="update-time">
              <i class="el-icon-time" />
              <span>{{ row.updateTime ? new Date(row.updateTime).toLocaleString() : '-' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column 
          label="လုပ်ဆောင်ချက်" 
          width="160" 
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-edit"
                @click="handleEdit(row)"
              >
                ပြင်ဆင်မည်
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="handleDelete(row)"
              >
                ဖျက်မည်
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 品牌表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="brandForm"
        :model="currentBrand"
        :rules="rules"
        label-width="120px"
        class="brand-form"
      >
        <el-form-item 
          label="မြန်မာအမည်" 
          prop="brandMmName"
        >
          <el-input 
            v-model="currentBrand.brandMmName"
            placeholder="မြန်မာဘာသာဖြင့်အမှတ်တံဆိပ်အမည်ထည့်ပါ"
          />
        </el-form-item>

        <el-form-item 
          label="အင်္ဂလိပ်အမည်" 
          prop="brandEnName"
        >
          <el-input 
            v-model="currentBrand.brandEnName"
            placeholder="အင်္ဂလိပ်ဘာသာဖြင့်အမှတ်တံဆိပ်အမည်ထည့်ပါ"
          />
        </el-form-item>

        <el-form-item 
          label="Logo" 
          prop="logoUrl"
        >
          <el-upload
            class="logo-uploader"
            :show-file-list="false"
            :http-request="handleLogoUpload"
            :before-upload="beforeLogoUpload"
          >
            <img v-if="currentBrand.logoUrl" :src="currentBrand.logoUrl" class="logo">
            <i v-else class="el-icon-plus logo-uploader-icon"></i>
          </el-upload>
        </el-form-item>

        <el-form-item 
          label="အခြေအနေ" 
          prop="status"
        >
          <el-switch
            v-model="currentBrand.status"
            active-value="ACTIVE"
            inactive-value="INACTIVE"
            active-text="အသုံးပြုနေသည်"
            inactive-text="ပိတ်ထားသည်"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">ပယ်ဖျက်မည်</el-button>
        <el-button 
          type="primary" 
          :loading="saving"
          @click="handleSubmit"
        >
          သိမ်းဆည်းမည်
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { gql } from 'graphql-tag'
import { goodsClient } from '@/apolloClient'

export default {
  name: 'BrandManagement',
  data() {
    return {
      brandList: [],
      listLoading: false,
      dialogVisible: false,
      dialogTitle: '',
      currentBrand: {
        brandId: null,
        brandMmName: '',
        brandEnName: '',
        logoUrl: '',
        status: 'ACTIVE',
        updateTime: null
      },
      saving: false,
      rules: {
        brandMmName: [
          { required: true, message: 'မြန်မာအမည်ထည့်ရန်လိုအပ်ပါသည်', trigger: 'blur' }
        ],
        brandEnName: [
          { required: true, message: 'အင်္ဂလိပ်အမည်ထည့်ရန်လိုအပ်ပါသည်', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchBrands()
  },
  methods: {
    async fetchBrands() {
      const FETCH_BRANDS = gql`
        query AllBrands {
          allBrands {
            brandId
            brandMmName
            brandEnName
            logoUrl
            status
            createBy
            createTime
            isDeleted
            updateBy
            updateTime
            __typename
          }
        }
      `
      try {
        this.listLoading = true
        const { data } = await goodsClient.query({
          query: FETCH_BRANDS,
          fetchPolicy: 'no-cache'
        })
        this.brandList = data.allBrands
      } catch (error) {
        console.error('Failed to fetch brands:', error)
        this.$message.error('အမှတ်တံဆိပ်စာရင်းရယူရန်မအောင်မြင်ပါ')
      } finally {
        this.listLoading = false
      }
    },
    handleAdd() {
      this.dialogTitle = 'အမှတ်တံဆိပ်အသစ်ထည့်ရန်'
      this.currentBrand = {
        brandId: null,
        brandMmName: '',
        brandEnName: '',
        logoUrl: '',
        status: 'ACTIVE',
        updateTime: null
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = 'အမှတ်တံဆိပ်ပြင်ဆင်ရန်'
      this.currentBrand = { ...row }
      this.dialogVisible = true
    },
    async handleDelete(row) {
      try {
        await this.$confirm('ဤအမှတ်တံဆိပ်ကိုဖျက်မှာသေချာပါသလား?', 'သတိပေးချက်', {
          confirmButtonText: 'သေချာပါသည်',
          cancelButtonText: 'မလုပ်တော့ပါ',
          type: 'warning'
        })
        await this.deleteBrand(row.brandId)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to delete brand:', error)
          this.$message.error('ဖျက်ရန်မအောင်မြင်ပါ')
        }
      }
    },
    handleDialogClose(done) {
      if (this.saving) return
      done()
    },
    async handleSubmit() {
      try {
        await this.$refs.brandForm.validate()
        this.saving = true
        await this.saveBrand()
      } catch (error) {
        if (error !== false) {
          console.error('Failed to save brand:', error)
          this.$message.error('သိမ်းဆည်းရန်မအောင်မြင်ပါ')
        }
      } finally {
        this.saving = false
      }
    },
    async handleLogoUpload({ file }) {
      try {
        this.saving = true
        const formData = new FormData()
        formData.append('file', file)

        // 调用文件上传API
        const response = await fetch('http://192.168.1.11:8091/shwethe_file_path/api/v1/file_path/test', {
          method: 'POST',
          body: formData
        })

        if (!response.ok) {
          throw new Error('Logo ပုံတင်ရန်မအောင်မြင်ပါ')
        }

        const data = await response.json()
        const imageUrl = data.url

        // 更新 Logo URL
        this.currentBrand.logoUrl = imageUrl

        this.$notify({
          title: 'အောင်မြင်ပါသည်',
          message: 'Logo ပုံတင်ပြီးပါပြီ',
          type: 'success'
        })

        return {
          url: imageUrl,
          name: imageUrl.split('/').pop()
        }
      } catch (error) {
        console.error('Logo ပုံတင်ရန်မအောင်မြင်ပါ:', error)
        this.$notify.error({
          title: 'အမှား',
          message: 'Logo ပုံတင်ရန်မအောင်မြင်ပါ: ' + error.message
        })
        throw error
      } finally {
        this.saving = false
      }
    },
    beforeLogoUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('ပုံဖိုင်သာလက်ခံပါသည်!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('ပုံဖိုင်အရွယ်အစားသည် 2MB အောက်သာဖြစ်ရပါမည်!')
        return false
      }
      return true
    },
    async saveBrand() {
      const UPSERT_BRAND = gql`
        mutation UpsertBrand(
          $brandId: Int,
          $brandMmName: String!,
          $brandEnName: String!,
          $logoUrl: String,
          $status: String!
        ) {
          upsertBrand(
            brandData: {
              brandId: $brandId,
              brandMmName: $brandMmName,
              brandEnName: $brandEnName,
              logoUrl: $logoUrl,
              status: $status
            }
          ) {
            brand {
              brandId
              brandMmName
              brandEnName
              logoUrl
              status
              createBy
              updateBy
              createTime
              updateTime
              isDeleted
              __typename
            }
            success
            __typename
          }
        }
      `
      
      const variables = {
        brandId: this.currentBrand.brandId ? parseInt(this.currentBrand.brandId) : null,
        brandMmName: this.currentBrand.brandMmName,
        brandEnName: this.currentBrand.brandEnName,
        logoUrl: this.currentBrand.logoUrl || '',
        status: this.currentBrand.status
      }

      try {
        const { data } = await goodsClient.mutate({
          mutation: UPSERT_BRAND,
          variables
        })
        
        if (data.upsertBrand.success) {
          this.$message.success('အောင်မြင်စွာသိမ်းဆည်းပြီးပါပြီ')
          this.dialogVisible = false
          this.fetchBrands()
        } else {
          throw new Error('သိမ်းဆည်းရန်မအောင်မြင်ပါ')
        }
      } catch (error) {
        console.error('Failed to save brand:', error)
        this.$message.error(error.message || 'သိမ်းဆည်းရန်မအောင်မြင်ပါ')
      }
    },
    async deleteBrand(brandId) {
      const DELETE_BRAND = gql`
        mutation DeleteBrand($brandId: ID!) {
          deleteBrand(brandId: $brandId) {
            success
            error {
              message
            }
          }
        }
      `
      
      try {
        const { data } = await goodsClient.mutate({
          mutation: DELETE_BRAND,
          variables: {
            brandId
          }
        })
        
        if (data.deleteBrand.success) {
          this.$message.success('အောင်မြင်စွာဖျက်ပြီးပါပြီ')
          this.fetchBrands()
        } else {
          throw new Error(data.deleteBrand.error.message)
        }
      } catch (error) {
        console.error('Failed to delete brand:', error)
        this.$message.error(error.message || 'ဖျက်ရန်မအောင်မြင်ပါ')
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
}

.box-card :deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-icon {
  font-size: 24px;
  color: #409EFF;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.05);
}

.data-table {
  margin: 0;
}

.data-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.data-table :deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
  padding: 12px 0;
}

.brand-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mm-name {
  font-weight: 500;
  color: #303133;
}

.en-name {
  font-size: 12px;
  color: #909399;
}

.image-preview {
  width: 60px;
  height: 60px;
  margin: 0 auto;
}

.brand-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.logo-uploader {
  text-align: center;
}

.logo-uploader .logo {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
}

.logo-uploader-icon:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.update-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #909399;
}

.brand-form {
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}
</style>
