<template>
  <div class="modern-category">
    <!-- 第一个卡片：分类选择 -->
    <el-card class="category-card" shadow="hover">
      <div class="card-header">
        <div class="header-left">
          <i class="el-icon-menu"></i>
          <span>ကုန်ပစ္စည်းအမျိုးအစားစီမံခန့်ခွဲမှု</span>
        </div>
        <div class="header-right">
          <el-button-group>
            <el-tooltip content="အားလုံးချဲ့ရန်" placement="top">
              <el-button type="primary" plain size="small" @click="expandAll">
                <i class="el-icon-zoom-in"></i>
              </el-button>
            </el-tooltip>
            <el-tooltip content="အားလုံးကျုံ့ရန်" placement="top">
              <el-button type="primary" plain size="small" @click="collapseAll">
                <i class="el-icon-zoom-out"></i>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </div>
      </div>

      <div class="category-selectors">
        <div class="selector-group">
          <div class="selector-label">
            <span class="step-number">1</span>
            <span>ပထမအဆင့်</span>
          </div>
          <el-select
            v-model="selectedLevel1"
            placeholder="ပထမအဆင့်အမျိုးအစား"
            clearable
            @change="handleLevel1Change"
          >
            <el-option
              v-for="item in level1Options"
              :key="item.categoryId"
              :label="item.mmName"
              :value="item.categoryId"
            />
          </el-select>
        </div>

        <div class="selector-group">
          <div class="selector-label">
            <span class="step-number">2</span>
            <span>ဒုတိယအဆင့်</span>
          </div>
          <el-select
            v-model="selectedLevel2"
            placeholder="ဒုတိယအဆင့်အမျိုးအစား"
            clearable
            :disabled="!selectedLevel1"
            @change="handleLevel2Change"
          >
            <el-option
              v-for="item in level2Options"
              :key="item.categoryId"
              :label="item.mmName"
              :value="item.categoryId"
            />
          </el-select>
        </div>

        <div class="selector-group">
          <div class="selector-label">
            <span class="step-number">3</span>
            <span>တတိယအဆင့်</span>
          </div>
          <el-select
            v-model="selectedLevel3"
            placeholder="တတိယအဆင့်အမျိုးအစား"
            clearable
            :disabled="!selectedLevel2"
            @change="handleFilterChange"
          >
            <el-option
              v-for="item in level3Options"
              :key="item.categoryId"
              :label="item.mmName"
              :value="item.categoryId"
            />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 在第一个卡片和规格选择卡片之间添加 -->
    <el-card class="category-card" shadow="hover">
      <div class="card-header">
        <div class="header-left">
          <i class="el-icon-collection"></i>
          <span>ကုန်ပစ္စည်းအမျိုးအစားစီမံခန့်ခွဲမှု</span>
        </div>
        <el-tag v-if="selectedLevel3" type="info">
          Selected Category ID: {{ selectedLevel3 }}
        </el-tag>
      </div>
      <div class="card-content">
        <el-form label-width="120px">
          <el-select
            v-model="selectedCategories"
            label="အမျိုးအစားအမည်"
            placeholder="အမျိုးအစားအမည်"
            multiple
            collapse-tags
            clearable
            class="category-select"
          >
            <el-option
              v-for="specOption in categorySpecOptions"
              :key="specOption.specId"
              :label="specOption.specName"
              :value="specOption.specId"
            >
              <span>{{ specOption.specName }}</span>
              <span v-if="existingSpecIds.includes(specOption.specId)" 
                    style="color: #67C23A; float: right">
                <i class="el-icon-check"></i>
              </span>
            </el-option>
          </el-select>

          <el-form-item class="form-actions">
            <el-button
              type="primary"
              :loading="submitting"
              @click="handleSubmitCategories"
              :disabled="!selectedLevel3"
            >
              <i class="el-icon-check"></i>
              သိမ်းဆည်းမည်
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 规格选择卡片 -->
    <el-card class="spec-card" shadow="hover" v-if="selectedLevel3">
      <div class="card-header">
        <div class="header-info">
          <div class="header-title">
            <i class="el-icon-collection-tag"></i>
            <span>ကုန်ပစ္စည်းအမျိုးအစားသတ်မှတ်ချက်</span>
          </div>
          <el-tag type="warning">ID: {{ selectedLevel3 }}</el-tag>
        </div>
      </div>

      <div class="spec-content">
        <div class="spec-grid">
          <div 
            v-for="spec in categorySpecOptions" 
            :key="spec.specId"
            class="spec-item"
          >
            <div class="spec-item-header">
              <i class="el-icon-price-tag"></i>
              <span>{{ spec.specName }}</span>
              <el-tag 
                v-if="selectedOptions[spec.specId]"
                size="small" 
                type="info"
              >
                {{ selectedOptions[spec.specId].length }}
              </el-tag>
            </div>
            
            <el-select
              v-model="selectedOptions[spec.specId]"
              multiple
              collapse-tags
              :placeholder="spec.specName"
              class="spec-select"
            >
              <el-option
                v-for="option in spec.options"
                :key="option.optionId"
                :label="option.optionValue"
                :value="option.optionId"
              />
            </el-select>
          </div>
        </div>

        <div class="form-actions">
          <el-button
            type="primary"
            :loading="savingOptions"
            @click="handleSaveSpecOptions"
            class="save-button"
          >
            <i class="el-icon-check"></i>
            သိမ်းဆည်းမည်
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <i class="el-icon-info-circle"></i>
      <p>ကျေးဇူးပြု၍ အမျိုးအစားတစ်ခုရွေးချယ်ပါ</p>
    </div>
  </div>
</template>

<script>
import { goodsClient } from '@/apolloClient'
import gql from 'graphql-tag'

export default {
  data() {
    return {
      categoryTree: [],
      specList: [],
      categorySpecOptions: [],
      selectedLevel1: null,
      selectedLevel2: null,
      selectedLevel3: null,
      savingOptions: false,
      selectedOptions: {},
      sizeOptions: [],
      selectedSize: null,
      originalSelectedOptions: {},
      selectedCategories: [],
      submitting: false,
      existingSpecIds: [],
    }
  },

  computed: {
    level1Options() {
      return this.categoryTree
    },
    level2Options() {
      return this.selectedLevel1
        ? this.categoryTree.find(c => c.categoryId === this.selectedLevel1)?.children || []
        : []
    },
    level3Options() {
      return this.selectedLevel2
        ? this.level2Options.find(c => c.categoryId === this.selectedLevel2)?.children || []
        : []
    },
    currentSpecId() {
      return this.categorySpecOptions[0]?.specId || null
    },
    sizeOptionsList() {
      return this.categorySpecOptions[0]?.options || []
    },
    currentSelectedOptions() {
      return this.selectedOptions[this.currentSpecId] || []
    },
    hasSelectedOptions() {
      return this.currentSelectedOptions.length > 0
    }
  },

  watch: {
    selectedLevel3: {
      async handler(newVal) {
        if (newVal) {
          await this.fetchExistingSpecs(newVal)
        } else {
          this.existingSpecIds = []
        }
      }
    }
  },

  created() {
    this.fetchCategories()
    this.fetchSizeOptions()
  },

  methods: {
    async fetchCategories() {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query GetCategories($page: Int, $pageSize: Int) {
              goodsCategories(page: $page, pageSize: $pageSize) {
                items {
                  categoryId
                  mmName
                  parentId
                }
                page
                pageSize
                total
              }
            }
          `,
          variables: {
            page: 1,
            pageSize: 1000
          }
        })
        this.categoryTree = this.buildCategoryTree(data.goodsCategories.items)
      } catch (error) {
        console.error('获取分类失败:', error)
      }
    },

    async fetchSizeOptions() {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query GetSpecsPaginated($page: Int, $pageSize: Int) {
              getSpecsPaginated(page: $page, pageSize: $pageSize) {
                items {
                  createdAt
                  inputType
                  isSku
                  options {
                    isDefault
                    optionId
                    sortOrder
                    specId
                    optionValue
                  }
                  specId
                  specName
                  specType
                  unit
                  valueType
                }
                pagination {
                  page
                  pageSize
                  total
                  totalPages
                }
              }
            }
          `,
          variables: {
            page: 1,
            pageSize: 1000
          }
        })
        this.sizeOptions = data.getSpecsPaginated.items
        console.log('获取到的尺寸选项:', this.sizeOptions)
      } catch (error) {
        console.error('获取尺寸选项失败:', error)
      }
    },

    async fetchCategorySpecOptions(categoryId) {
      try {
        // 获取分类关联的规格选项
        const { data } = await goodsClient.query({
          query: gql`
            query GetCategorySpecOptions($categoryId: Int!) {
              getCategorySpecOptions(categoryId: $categoryId) {
                categoryId
                optionId
                specId
                optionValue
                specName
              }
            }
          `,
          variables: { categoryId }
        })

        // 合并所有规格选项
        const allSpecs = this.sizeOptions.reduce((acc, spec) => {
          acc[spec.specId] = {
            specId: spec.specId,
            specName: spec.specName,
            options: spec.options.map(opt => ({
              optionId: opt.optionId,
              optionValue: opt.optionValue
            }))
          }
          return acc
        }, {})

        // 标记已选中的选项
        const existingOptions = data.getCategorySpecOptions || []
        const initialSelected = {}

        existingOptions.forEach(item => {
          if (allSpecs[item.specId]) {
            allSpecs[item.specId].options = allSpecs[item.specId].options.map(opt => ({
              ...opt,
              selected: true
            }))

            // 初始化选中状态
            if (!initialSelected[item.specId]) {
              initialSelected[item.specId] = []
            }
            initialSelected[item.specId].push(item.optionId)
          }
        })

        // 设置初始选中值
        this.selectedOptions = { ...initialSelected }
        this.categorySpecOptions = Object.values(allSpecs)
      } catch (error) {
        console.error('获取分类规格选项失败:', error)
      }
    },

    async upsertCategorySpecOption(params) {
      try {
        await goodsClient.mutate({
          mutation: gql`
            mutation UpsertCategorySpecOption(
              $categoryId: Int!
              $specId: Int!
              $optionId: Int!
            ) {
              upsertCategorySpecOption(
                data: {
                  categoryId: $categoryId
                  specId: $specId
                  optionId: $optionId
                }
              ) {
                success
              }
            }
          `,
          variables: params
        })
      } catch (error) {
        console.error('保存关联失败:', error)
        throw error
      }
    },

    handleLevel1Change() {
      this.selectedLevel2 = null
      this.selectedLevel3 = null
    },

    handleLevel2Change() {
      this.selectedLevel3 = null
    },

    async handleFilterChange() {
      console.log('selectedLevel3', this.selectedLevel3)
      await this.fetchCategorySpecOptions(this.selectedLevel3)
    },

    expandAll() {
      this.$emit('expand-all')
    },

    collapseAll() {
      this.$emit('collapse-all')
    },

    getOptionLabel(options, optionId) {
      const option = options.find(opt => opt.optionId === optionId)
      return option ? option.optionValue : ''
    },

    isOptionSelected(specId, optionId) {
      if (!specId) return false
      return this.selectedOptions[specId] && this.selectedOptions[specId].includes(optionId)
    },

    isNewlySelected(specId, optionId) {
      const currentSelection = this.selectedOptions[specId]
      if (!currentSelection) return false

      const isSelected = Array.isArray(currentSelection)
        ? currentSelection.includes(optionId)
        : currentSelection === optionId

      return isSelected && !this.isOptionSelected(specId, optionId)
    },

    async handleSaveSpecOptions() {
      try {
        this.savingOptions = true
        const operations = []

        if (!this.selectedOptions) return

        // 计算新增和删除的选项
        const allSpecIds = new Set([
          ...Object.keys(this.selectedOptions),
          ...Object.keys(this.originalSelectedOptions)
        ])

        for (const specId of allSpecIds) {
          const currentIds = this.selectedOptions[specId] || []
          const originalIds = this.originalSelectedOptions[specId] || []

          // 处理新增
          const newOptions = currentIds.filter(id => !originalIds.includes(id))
          for (const optionId of newOptions) {
            operations.push(
              this.upsertCategorySpecOption({
                categoryId: this.selectedLevel3,
                specId: parseInt(specId),
                optionId: parseInt(optionId)
              })
            )
          }

          // 处理删除
          const removedIds = originalIds.filter(id => !currentIds.includes(id))
          for (const optionId of removedIds) {
            operations.push(
              this.deleteCategorySpecOption({
                categoryId: this.selectedLevel3,
                specId: parseInt(specId),
                optionId: parseInt(optionId)
              })
            )
          }
        }

        if (operations.length > 0) {
          await Promise.all(operations)

          // 重新获取数据
          await this.fetchCategorySpecOptions(this.selectedLevel3)
          await this.fetchSizeOptions()

          // 更新原始选中状态
          this.originalSelectedOptions = JSON.parse(JSON.stringify(this.selectedOptions))

          this.$notify.success({
            title: '成功',
            message: '规格选项保存成功'
          })
        } else {
          this.$notify.info({
            title: '提示',
            message: '没有新的选项需要保存'
          })
        }
      } catch (error) {
        console.error('保存规格选项失败:', error)
        this.$notify.error({
          title: '错误',
          message: '规格选项保存失败'
        })
      } finally {
        this.savingOptions = false
      }
    },

    async deleteCategorySpecOption(params) {
      try {
        await goodsClient.mutate({
          mutation: gql`
            mutation DeleteCategorySpecOption(
              $categoryId: Int!
              $specId: Int!
              $optionId: Int!
            ) {
              deleteCategorySpecOption(
                categoryId: $categoryId
                specId: $specId
                optionId: $optionId
              ) {
                success
              }
            }
          `,
          variables: params
        })
      } catch (error) {
        console.error('删除关联失败:', error)
        throw error
      }
    },

    buildCategoryTree(categories) {
      const tree = []
      const map = {}

      categories.forEach(category => {
        map[category.categoryId] = { ...category, children: [] }
      })

      categories.forEach(category => {
        if (category.parentId && map[category.parentId]) {
          map[category.parentId].children.push(map[category.categoryId])
        } else {
          tree.push(map[category.categoryId])
        }
      })

      return tree
    },

    removeOption(specId, optionId) {
      if (Array.isArray(this.selectedOptions[specId])) {
        this.selectedOptions[specId] = this.selectedOptions[specId].filter(id => id !== optionId)
      }
    },

    toggleOption(specId, optionId) {
      if (!specId) return
      if (!this.selectedOptions[specId]) {
        this.$set(this.selectedOptions, specId, [])
      }

      const index = this.selectedOptions[specId].indexOf(optionId)
      if (index > -1) {
        this.selectedOptions[specId].splice(index, 1)
      } else {
        if (!this.selectedOptions[specId].includes(optionId)) {
          this.selectedOptions[specId].push(optionId)
        }
      }
    },

    handleSpecChange(specId, newValue) {
      // 处理规格选项变化
      this.selectedOptions[specId] = newValue
    },

    async handleSubmitCategories() {
      if (!this.selectedLevel3) {
        this.$message.warning('请先选择三级分类')
        return
      }

      if (!this.selectedCategories.length) {
        this.$message.warning('ကျေးဇူးပြု၍ အမျိုးအစားရွေးချယ်ပါ')
        return
      }

      try {
        this.submitting = true

        // 获取需要添加和删除的规格
        const toAdd = this.selectedCategories.filter(id => !this.existingSpecIds.includes(id))
        const toRemove = this.existingSpecIds.filter(id => !this.selectedCategories.includes(id))

        // 创建添加操作
        const addOperations = toAdd.map(specId =>
          goodsClient.mutate({
            mutation: gql`
              mutation UpsertCategorySpec($specData: CategorySpecInput!) {
                upsertCategorySpec(specData: $specData) {
                  success
                }
              }
            `,
            variables: {
              specData: {
                categoryId: this.selectedLevel3,
                specId: specId,
                isRequired: false,
                sortOrder: 10
              }
            }
          })
        )

        // 创建删除操作
        const removeOperations = toRemove.map(specId =>
          goodsClient.mutate({
            mutation: gql`
              mutation DeleteCategorySpec($categoryId: Int!, $specId: Int!) {
                deleteCategorySpec(categoryId: $categoryId, specId: $specId) {
                  success
                }
              }
            `,
            variables: {
              categoryId: this.selectedLevel3,
              specId: specId
            }
          })
        )

        // 执行所有操作
        const results = await Promise.all([...addOperations, ...removeOperations])

        // 检查是否所有操作都成功
        const hasErrors = results.some(result => 
          !result.data.upsertCategorySpec?.success && 
          !result.data.deleteCategorySpec?.success
        )

        if (hasErrors) {
          this.$message.error('အချို့သောလုပ်ဆောင်ချက်များမအောင်မြင်ပါ')
        } else {
          this.$message.success('အောင်မြင်စွာသိမ်းဆည်းပြီးပါပြီ')
          // 重新获取最新数据
          await this.fetchExistingSpecs(this.selectedLevel3)
        }
      } catch (error) {
        console.error('သိမ်းဆည်းမှုမအောင်မြင်ပါ:', error)
        this.$message.error('သိမ်းဆည်းမှုမအောင်မြင်ပါ')
      } finally {
        this.submitting = false
      }
    },

    async fetchExistingSpecs(categoryId) {
      try {
        const { data } = await goodsClient.query({
          query: gql`
            query GetCategorySpecs($categoryId: Int!) {
              getCategorySpecs(categoryId: $categoryId) {
                specId
                categoryId
                isRequired
                sortOrder
                createdAt
              }
            }
          `,
          variables: {
            categoryId: parseInt(categoryId)
          }
        })

        // 更新已有规格ID列表
        this.existingSpecIds = data.getCategorySpecs.map(spec => spec.specId)
        
        // 预选已有的规格
        this.selectedCategories = [...this.existingSpecIds]
      } catch (error) {
        console.error('获取分类规格失败:', error)
        this.$message.error('获取分类规格失败')
      }
    }
  }
}
</script>

<style scoped>
.modern-category {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.category-card,
.spec-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.header-left i {
  font-size: 20px;
  color: #409EFF;
}

.category-selectors {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.spec-content {
  padding: 20px;
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.spec-item {
  padding: 16px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  background: #FAFAFA;
}

.spec-item-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #606266;
}

.spec-item-header i {
  color: #409EFF;
}

.spec-select {
  width: 100%;
}

.form-actions {
  margin-top: 24px;
  text-align: right;
  padding: 0 20px;
}

.save-button {
  min-width: 140px;
  height: 36px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  width: 100%;
}

:deep(.el-form-item__label) {
  color: #606266;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-button-group) {
  .el-button {
    padding: 8px 12px;
  }
}

.card-content {
  padding: 20px;
}

.category-select {
  width: 100%;
  margin-bottom: 20px;
}

.spec-card {
  margin-top: 20px;
  background: white;
  border-radius: 8px;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.header-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.header-title i {
  color: #409EFF;
  font-size: 20px;
}

.spec-content {
  padding: 20px;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
}

:deep(.el-tag) {
  margin-left: auto;
}
</style>

