<template>
  <div class="app-container">
    <select-category />
    <categories />
    <brand />
  </div>
</template>

<script>
import Categories from './components/categories.vue'
import SelectCategory from './components/selectCategory.vue'
import Brand from './components/brand.vue'
export default {
  name: 'EmployeeIndex',
  components: {
    Categories,
    SelectCategory,
    Brand
  },
  data() {
    return {
      activeTab: 'employee', // 当前激活的标签页
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
    }
  },
  methods: {
    handleFilter(query) {
      this.listQuery = { ...query }
      this.getList()
    },
    // 新增标签切换处理
    handleTabClick(tab) {
      // 切换标签时重置查询条件
      this.listQuery = {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
      this.getList()
    },
    async getList() {
      // 根据当前激活的标签调用不同的API
      if (this.activeTab === 'benefit') {
        // 调用福利管理接口
      } else {
        // 调用员工管理接口
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
