<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">
        ပေးပို့မှုမှတ်တမ်းအသစ်ထည့်ရန်
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ကုန်ပစ္စည်း ID" align="center">
        <template slot-scope="{row}">
          <span>{{ row.goods_id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ကုန်ပစ္စည်းအမည်" align="center">
        <template slot-scope="{row}">
          <span>{{ row.product_info ? row.product_info.product_id_name : '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ကုန်ပစ္စည်းဖော်ပြချက်" align="center">
        <template slot-scope="{row}">
          <span>{{ row.product_info ? row.product_info.product_d_name : '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="စတင်ရာဌာနခွဲ" align="center">
        <template slot-scope="{row}">
          <span>{{ row.branch_from_id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ရည်မှန်းဌာနခွဲ" align="center">
        <template slot-scope="{row}">
          <span>{{ row.branch_to_id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ကုန်ပစ္စည်းအရေအတွက်" align="center">
        <template slot-scope="{row}">
          <span>{{ row.product_qty }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ကုန်ပစ္စည်းအမျိုးအစား" align="center">
        <template slot-scope="{row}">
          <span>{{ getGoodsTypeLabel(row.goods_type_from) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ကားအကြိမ်" align="center">
        <template slot-scope="{row}">
          <span>{{ row.che_ci }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="ဖန်တီးသည့်အချိန်" align="center">
        <template slot-scope="{row}">
          <span>{{ formatDate(row.create_datetime) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="အခြေအနေ" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? 'တက်ကြွသော' : 'တက်ကြွမှုမရှိ' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="လုပ်ဆောင်ချက်" align="center" width="180">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleEdit(row)">
            တည်းဖြတ်ရန်
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(row)">
            ဖျက်ရန်
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { deliveryGoodsSystemClient } from '@/apolloClient'
import gql from 'graphql-tag'
import Pagination from '@/components/Pagination'

const GET_DELIVERY_GOODS = gql`
  query GetDeliveryGoods($page: Int!, $limit: Int!) {
    deliveryGoodsBranchesWithPagination(page: $page, limit: $limit) {
      items {
        goods_id
        branch_to_id
        branch_from_id
        product_qty
        create_datetime
        goods_type_from
        che_ci
        status
        product_info {
          product_id
          product_id_name
          product_d_name
        }
      }
      total
    }
  }
`

const DELETE_DELIVERY_GOOD = gql`
  mutation DeleteDeliveryGood($id: Int!) {
    deleteDeliveryGoodsBranch(id: $id)
  }
`

export default {
  name: 'DeliveryGoodsList',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getGoodsTypeLabel(type) {
      const types = {
        0: 'အမျိုးအစား 0',
        1: 'အမျိုးအစား 1',
        2: 'အမျိုးအစား 2'
      }
      return types[type] || `အမျိုးအစား ${type}`
    },
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    async getList() {
      this.listLoading = true
      try {
        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_DELIVERY_GOODS,
          variables: {
            page: this.listQuery.page,
            limit: this.listQuery.limit
          },
          fetchPolicy: 'network-only'
        })
        
        this.list = data.deliveryGoodsBranchesWithPagination.items
        this.total = data.deliveryGoodsBranchesWithPagination.total
      } catch (error) {
        console.error('ကုန်ပစ္စည်းပေးပို့မှုစာရင်းရယူရန် မအောင်မြင်ပါ:', error)
        this.$message.error('ကုန်ပစ္စည်းပေးပို့မှုစာရင်းရယူရန် မအောင်မြင်ပါ')
      } finally {
        this.listLoading = false
      }
    },
    handleEdit(row) {
      this.$router.push(`/deliveryGoodsSystem/form-input?id=${row.goods_id}`)
    },
    handleDelete(row) {
      this.$confirm('ဤပေးပို့မှုမှတ်တမ်းကို ဖျက်ရန် သေချာပါသလား?', 'အသိပေးချက်', {
        confirmButtonText: 'အတည်ပြုပါ',
        cancelButtonText: 'ပယ်ဖျက်ပါ',
        type: 'warning'
      }).then(async () => {
        try {
          await deliveryGoodsSystemClient.mutate({
            mutation: DELETE_DELIVERY_GOOD,
            variables: { id: row.goods_id }
          })
          this.$message.success('အောင်မြင်စွာ ဖျက်လိုက်ပါပြီ')
          this.getList()
        } catch (error) {
          this.$message.error('ဖျက်ရန် မအောင်မြင်ပါ: ' + (error.message || 'အမှားအယွင်းမသိရှိပါ'))
          console.error('ဖျက်ရာတွင် အမှားအယွင်းဖြစ်ပွားခဲ့သည်:', error)
        }
      }).catch(() => {
        this.$message.info('ဖျက်ခြင်းကို ပယ်ဖျက်လိုက်ပါပြီ')
      })
    },
    handleCreate() {
      this.$router.push('/deliveryGoodsSystem/form-input')
    }
  }
}
</script> 