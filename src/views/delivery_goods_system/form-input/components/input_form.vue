<template>
  <div class="form-container">
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="140px"
      class="delivery-form"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="ကုန်ပစ္စည်း ID" prop="goods_id">
            <el-select
              v-model="formData.goods_id"
              filterable
              remote
              clearable
              placeholder="ကုန်ပစ္စည်းရှာရန် စာသားရိုက်ထည့်ပါ"
              :remote-method="searchProductNames"
              :loading="productSearchLoading"
              class="full-width"
            >
              <el-option
                v-for="item in productOptions"
                :key="item.productId"
                :label="`${item.productIdName} - ${item.productMmName } - ${item.productDName}`"
                :value="item.productId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="ကုန်ပစ္စည်းအရေအတွက်" prop="product_qty">
            <el-input-number
              v-model="formData.product_qty"
              :min="0"
              class="full-width"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="စတင်ရာဌာနခွဲ" prop="branch_from_id">
            <el-select
              v-model="formData.branch_from_id"
              class="full-width"
              placeholder="ဌာနခွဲရွေးချယ်ပါ"
              @change="validateBranchSelection"
            >
              <el-option
                v-for="item in branchOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="formData.branch_to_id === item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="ရည်မှန်းဌာနခွဲ" prop="branch_to_id">
            <el-select
              v-model="formData.branch_to_id"
              class="full-width"
              placeholder="ဌာနခွဲရွေးချယ်ပါ"
              @change="validateBranchSelection"
            >
              <el-option
                v-for="item in branchOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="formData.branch_from_id === item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item class="form-buttons">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')">
          <i class="el-icon-check" /> တင်သွင်းရန်
        </el-button>
        <el-button size="medium" @click="resetForm('ruleForm')">
          <i class="el-icon-refresh" /> ပြန်လည်သတ်မှတ်ရန်
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { shwethe_data_client, deliveryGoodsSystemClient } from '@/apolloClient'
import gql from 'graphql-tag'

const SEARCH_PRODUCT_NAMES = gql`
  query SearchProductNames($keyword: String, $page: Int, $pageSize: Int, $sortBy: String, $sortOrder: String) {
    searchProductNames(input: {
      keyword: $keyword
      page: $page
      pageSize: $pageSize
      sortBy: $sortBy
      sortOrder: $sortOrder
    }) {
      total
      page
      pageSize
      items {
        autoId
        productId
        productMmName
        productDName
        productIdName
      }
    }
  }
`

const CREATE_DELIVERY_GOODS = gql`
  mutation CreateDeliveryGoods($input: DeliveryGoodsBranchInput!) {
    createDeliveryGoodsBranch(input: $input) {
      goods_id
    }
  }
`

const UPDATE_DELIVERY_GOODS = gql`
  mutation UpdateDeliveryGoods($id: Int!, $input: DeliveryGoodsBranchInput!) {
    updateDeliveryGoodsBranch(id: $id, input: $input) {
      goods_id
    }
  }
`

const GET_DELIVERY_GOOD = gql`
  query GetDeliveryGood($id: Int!) {
    deliveryGoodsBranch(id: $id) {
      goods_id
      branch_to_id
      branch_from_id
      product_qty
      goods_type_from
      che_ci
      status
      create_datetime
      product_info {
        product_id
        product_id_name
        product_d_name
      }
    }
  }
`

export default {
  name: 'InputForm',
  data() {
    return {
      formData: {
        goods_id: '',
        branch_from_id: '',
        branch_to_id: '',
        product_qty: 0,
        goods_type_from: 0,
        che_ci: 0,
        status: 0,
        createDate: ''
      },
      branchOptions: [
        { value: 1, label: 'ဌာနခွဲ 1' },
        { value: 2, label: 'ဌာနခွဲ 2' },
        { value: 3, label: 'ဌာနခွဲ 3' }
      ],
      rules: {
        goods_id: [
          { required: true, message: 'ကုန်ပစ္စည်း ID ဖြည့်သွင်းပါ', trigger: 'blur' }
        ],
        branch_from_id: [
          { required: true, message: 'စတင်ရာဌာနခွဲ ဖြည့်သွင်းပါ', trigger: 'change' }
        ],
        branch_to_id: [
          { required: true, message: 'ရည်မှန်းဌာနခွဲ ဖြည့်သွင်းပါ', trigger: 'change' }
        ],
        product_qty: [
          { required: true, message: 'ကုန်ပစ္စည်းအရေအတွက် ဖြည့်သွင်းပါ', trigger: 'blur' }
        ],
        goods_type_from: [
          { required: true, message: 'ကုန်ပစ္စည်းအမျိုးအစား ရွေးချယ်ပါ', trigger: 'change' }
        ]
      },
      productOptions: [],
      productSearchLoading: false,
      isEdit: false,
      editId: null
    }
  },
  created() {
    // 检查URL参数，如果有ID则为编辑模式
    const id = this.$route.query.id
    if (id) {
      this.isEdit = true
      this.editId = parseInt(id)
      this.fetchDeliveryGood(this.editId)
    }
  },
  methods: {
    async searchProductNames(keyword) {
      if (!keyword) {
        this.productOptions = []
        return
      }
      this.productSearchLoading = true
      try {
        const { data } = await shwethe_data_client.query({
          query: SEARCH_PRODUCT_NAMES,
          variables: {
            keyword: keyword.toLowerCase(),
            page: 1,
            pageSize: 10,
            sortBy: 'product_id',
            sortOrder: 'DESC'
          }
        })

        // 对搜索结果进行排序，使匹配度高的结果优先显示
        this.productOptions = data.searchProductNames.items.map(item => ({
          ...item,
          productIdName: this.formatDisplayName(item.productIdName),
          productDName: item.productDName ? this.formatDisplayName(item.productDName) : null,
          productMmName: item.productMmName ? this.formatDisplayName(item.productMmName) : null
        })).sort((a, b) => {
          const searchTerm = keyword.toLowerCase()

          // 检查是否完全匹配关键词
          const aExactMatch = a.productIdName.toLowerCase() === searchTerm ||
                                      a.productDName?.toLowerCase() === searchTerm ||
                                      a.productMmName?.toLowerCase() === searchTerm
          const bExactMatch = b.productIdName.toLowerCase() === searchTerm ||
                                      b.productDName?.toLowerCase() === searchTerm ||
                                      b.productMmName?.toLowerCase() === searchTerm

          // 检查是否以关键词开头
          const aStartsWith = a.productIdName.toLowerCase().startsWith(searchTerm) ||
                                      a.productDName?.toLowerCase().startsWith(searchTerm) ||
                                      a.productMmName?.toLowerCase().startsWith(searchTerm)
          const bStartsWith = b.productIdName.toLowerCase().startsWith(searchTerm) ||
                                      b.productDName?.toLowerCase().startsWith(searchTerm) ||
                                      b.productMmName?.toLowerCase().startsWith(searchTerm)

          // 检查是否包含关键词
          const aContains = a.productIdName.toLowerCase().includes(searchTerm) ||
                                    a.productDName?.toLowerCase().includes(searchTerm) ||
                                    a.productMmName?.toLowerCase().includes(searchTerm)
          const bContains = b.productIdName.toLowerCase().includes(searchTerm) ||
                                    b.productDName?.toLowerCase().includes(searchTerm) ||
                                    b.productMmName?.toLowerCase().includes(searchTerm)

          // 排序优先级：完全匹配 > 开头匹配 > 包含匹配 > 按ID排序
          if (aExactMatch && !bExactMatch) return -1
          if (!aExactMatch && bExactMatch) return 1
          if (aStartsWith && !bStartsWith) return -1
          if (!aStartsWith && bStartsWith) return 1
          if (aContains && !bContains) return -1
          if (!aContains && bContains) return 1

          // 如果匹配度相同，则按ID降序排序
          return b.productId - a.productId
        })
      } catch (e) {
        this.$message.error('ကုန်ပစ္စည်းရှာဖွေမှု မအောင်မြင်ပါ')
        console.error(e)
      }
      this.productSearchLoading = false
    },
    formatDisplayName(name) {
      if (!name) return ''
      return name.toLowerCase().split(' ').map(word => {
        if (word === word.toUpperCase()) {
          return word
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      }).join(' ')
    },
    async fetchDeliveryGood(id) {
      try {
        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_DELIVERY_GOOD,
          variables: { id }
        })

        if (data.deliveryGoodsBranch) {
          const item = data.deliveryGoodsBranch
          this.formData = {
            goods_id: item.goods_id,
            branch_from_id: item.branch_from_id,
            branch_to_id: item.branch_to_id,
            product_qty: item.product_qty,
            goods_type_from: item.goods_type_from,
            che_ci: item.che_ci,
            status: item.status,
            createDate: item.create_datetime ? item.create_datetime.split('T')[0] : ''
          }

          // 如果有商品信息，添加到选项中
          if (item.product_info) {
            this.productOptions = [{
              productId: item.product_info.product_id,
              productIdName: item.product_info.product_id_name,
              productDName: item.product_info.product_d_name
            }]
          }
        }
      } catch (error) {
        this.$message.error('ပေးပို့မှုမှတ်တမ်းရယူရန် မအောင်မြင်ပါ: ' + (error.message || 'အမှားအယွင်းမသိရှိပါ'))
        console.error(error)
      }
    },
    async submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          try {
            const input = {
              goods_id: Number(this.formData.goods_id),
              branch_from_id: this.formData.branch_from_id,
              branch_to_id: this.formData.branch_to_id,
              product_qty: this.formData.product_qty,
              goods_type_from: this.formData.goods_type_from,
              che_ci: this.formData.che_ci,
              status: this.formData.status
            }

            let result
            if (this.isEdit) {
              // 更新现有记录
              result = await deliveryGoodsSystemClient.mutate({
                mutation: UPDATE_DELIVERY_GOODS,
                variables: {
                  id: this.editId,
                  input
                }
              })
              if (result.data.updateDeliveryGoodsBranch) {
                this.$message.success('အောင်မြင်စွာ မွမ်းမံပြီးပါပြီ')
                this.$router.push('/deliveryGoodsSystem/goods-list')
              }
            } else {
              // 创建新记录
              result = await deliveryGoodsSystemClient.mutate({
                mutation: CREATE_DELIVERY_GOODS,
                variables: { input }
              })
              if (result.data.createDeliveryGoodsBranch) {
                this.$message.success('အောင်မြင်စွာ တင်သွင်းပြီးပါပြီ')
                this.resetForm(formName)
              }
            }
          } catch (error) {
            this.$message.error((this.isEdit ? 'မွမ်းမံရန်' : 'တင်သွင်းရန်') + ' မအောင်မြင်ပါ: ' + (error.message || 'အမှားအယွင်းမသိရှိပါ'))
            console.error('လုပ်ဆောင်မှုအမှား:', error)
          }
        } else {
          this.$message.error('ဖောင်ကို မှန်ကန်စွာ ဖြည့်စွက်ပါ')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    validateBranchSelection() {
      if (this.formData.branch_from_id === this.formData.branch_to_id) {
        this.$message.warning('စတင်ရာဌာနခွဲနှင့် ရည်မှန်းဌာနခွဲ မတူညီရပါ')
        if (this.formData.branch_from_id === this.formData.branch_to_id) {
          this.formData.branch_to_id = ''
        }
      }
    }
  }
}
</script>

<style scoped>
.form-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.delivery-form {
    max-width: 1200px;
    margin: 0 auto;
}

.delivery-form .el-form-item {
    margin-bottom: 22px;
}

.delivery-form .el-input-number.full-width {
    width: 100%;
}

.delivery-form .el-select.full-width {
    width: 100%;
}

.form-buttons {
    margin-top: 32px;
    text-align: center;
}

.form-buttons .el-button {
    padding: 12px 24px;
    margin: 0 8px;
}

.el-form-item__label {
    font-weight: 500;
}

.el-input-number .el-input__inner {
    text-align: left;
}
</style>
