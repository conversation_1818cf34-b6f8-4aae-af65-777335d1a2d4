<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="form-inline">
        <el-form-item label="ကုန်ပစ္စည်း ID">
          <el-select
            v-model="listQuery.goods_id"
            filterable
            remote
            clearable
            placeholder="ကုန်ပစ္စည်းရှာရန် စာသားရိုက်ထည့်ပါ"
            :remote-method="searchProductNames"
            :loading="productSearchLoading"
            style="width: 300px"
          >
            <el-option
              v-for="item in productOptions"
              :key="item.jiaYiId"
              :label="`${item.jiaYiIdName} - ${item.jiaYiMmName || item.jiaYiIdName}`"
              :value="item.jiaYiId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">ရှာဖွေရန်</el-button>
          <el-button @click="resetFilter">ပြန်လည်သတ်မှတ်ရန်</el-button>
          <el-button type="success" @click="submitCheciData">ကားအကြိမ်အချက်အလက်များကိုပိတ်ရန်</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="transfer-container">
      <div class="left-table">
        <h3>左侧表格</h3>
        <el-table
          v-loading="leftTableLoading"
          :data="leftTableData"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="ID" prop="goods_id" width="80" align="center" />
          <el-table-column label="名称" prop="product_info.product_id_name" min-width="120" />
          <el-table-column label="名称" prop="product_info.product_mm_name" min-width="120" />
          <el-table-column label="名称" prop="product_info.product_d_name" min-width="120" />
          <el-table-column label="日期" prop="create_datetime" width="120" align="center">
            <template slot-scope="{row}">
              <span>{{ formatDate(row.create_datetime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="100" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status | statusFilter">{{ row.status === 1 ? '激活' : '未激活' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="产品数量" prop="product_qty" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.product_qty }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分配数量" prop="delivery_goods_branch_from_tb.qty" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.delivery_goods_branch_from_tb ? row.delivery_goods_branch_from_tb.qty : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车次" prop="che_ci" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.che_ci }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" icon="el-icon-right" @click="transferItem(row)" />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="right-table">
        <div class="right-header">
          <h3>右侧表格</h3>
          <div class="select-container">
            <el-button
              type="primary"
              icon="el-icon-refresh"
              :loading="refreshLoading"
              style="margin-right: 10px"
              @click="handleSummit"
            >Summit</el-button>
            <el-button
              type="primary"
              icon="el-icon-refresh"
              :loading="refreshLoading"
              style="margin-right: 10px"
              @click="handleRefresh"
            >刷新</el-button>
            <span class="select-label">车次选择：</span>
            <el-select
              v-model="selectedTrain"
              placeholder="请选择车次"
              filterable
              clearable
              @change="handleTrainChange"
            >
              <el-option
                v-for="item in trainOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>

        <el-table
          v-loading="rightTableLoading"
          :data="rightTableData"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="ID" prop="goods_id" width="80" align="center" />
          <el-table-column label="名称" prop="product_info.product_id_name" min-width="120" />
          <el-table-column label="日期" prop="create_datetime" width="120" align="center">
            <template slot-scope="{row}">
              <span>{{ formatDate(row.create_datetime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="100" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status | statusFilter">{{ row.status === 1 ? '激活' : '未激活' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="产品数量" prop="product_qty" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.product_qty }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分配数量" prop="delivery_goods_branch_from_tb.qty" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.delivery_goods_branch_from_tb ? row.delivery_goods_branch_from_tb.qty : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车次" prop="che_ci" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.che_ci }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="{row}">
              <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeItem(row)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { deliveryGoodsSystemClient, shwethe_data_client } from '@/apolloClient'
import gql from 'graphql-tag'
import { postCheci } from '@/api/rest/shwethe_data_n'
import { updateCheciStatus } from '@/api/rest/shwethe_data'

const GET_DELIVERY_GOODS_BY_CHECI_2 = gql`
  query GetDeliveryGoodsByCheCI($page: Int!, $limit: Int!, $branch_to_id: Int!, $che_ci: Int!) {
    deliveryGoodsBranchesFromWithPagination(
      page: $page,
      limit: $limit,
      filter: {
        branch_to_id: $branch_to_id,
        che_ci: $che_ci
      }
    ) {
      items {
        auto_id
        goods_id
        branch_to_id
        branch_from_id
        product_qty
        create_datetime
        goods_type_from
        che_ci
        status
        product_info {
          product_id
          product_id_name
          product_d_name
          product_mm_name
        }
        delivery_goods_branch_from_tb {
          jia_yi_fang
          qty
        }
        delivery_goods_branch_to_tb {
          jia_yi_fang
          qty
        }
      }
      total
    }
  }
`

const SEARCH_PRODUCT_NAMES = gql`
  query SearchJiaYiNames($keyword: String, $page: Int, $pageSize: Int, $sortBy: String, $sortOrder: String) {
    searchJiaYiNames(input: {
      keyword: $keyword
      page: $page
      pageSize: $pageSize
      sortBy: $sortBy
      sortOrder: $sortOrder
    }) {
      total
      page
      pageSize
      items {
        autoId
        jiaYiId
        jiaYiIdName
        jiaYiMmName
      }
    }
  }
`

const GET_DELIVERY_GOODS_FROM = gql`
  query GetDeliveryGoodsFrom($page: Int!, $limit: Int!, $branch_from_id: Int!, $create_datetime: DateTimeFilter!) {
    deliveryGoodsBranchesFromWithPagination(
      page: $page,
      limit: $limit,
      filter: {
        branch_from_id: $branch_from_id,
        che_ci: 0,
        create_datetime: $create_datetime
      }
    ) {
      items {
        auto_id
        goods_id
        branch_to_id
        branch_from_id
        product_qty
        create_datetime
        goods_type_from
        che_ci
        status
        product_info {
          product_id
          product_id_name
          product_d_name
          product_mm_name
        }
        delivery_goods_branch_from_tb {
          jia_yi_fang
          qty
        }
      }
    }
  }
`

const GET_DELIVERY_GOODS_BY_CHECI = gql`
  query GetDeliveryGoodsByCheCI($page: Int!, $limit: Int!, $branch_from_id: Int!, $che_ci: Int!, $create_datetime: DateTimeFilter!) {
    deliveryGoodsBranchesFromWithPagination(
      page: $page,
      limit: $limit,
      filter: {
        branch_from_id: $branch_from_id,
        che_ci: $che_ci,
        create_datetime: $create_datetime
      }
    ) {
      items {
        auto_id
        goods_id
        branch_to_id
        branch_from_id
        product_qty
        create_datetime
        goods_type_from
        che_ci
        status
        product_info {
          product_id
          product_id_name
          product_d_name
          product_mm_name
        }
        delivery_goods_branch_from_tb {
          jia_yi_fang
          qty
        }
      }
    }
  }
`

const GET_DELIVERY_GOODS_BY_CHECI_All = gql`
  query GetDeliveryGoodsByCheCI($page: Int!, $limit: Int!, $che_ci: Int!, $create_datetime: DateTimeFilter!) {
    deliveryGoodsBranchesFromWithPagination(
      page: $page,
      limit: $limit,
      filter: {
        che_ci: $che_ci,
        create_datetime: $create_datetime
      }
    ) {
      items {
        auto_id
        goods_id
        branch_to_id
        branch_from_id
        product_qty
        create_datetime
        goods_type_from
        che_ci
        status
        product_info {
          product_id
          product_id_name
          product_d_name
          product_mm_name
        }
        delivery_goods_branch_from_tb {
          jia_yi_fang
          qty
        }
      }
    }
  }
`

const GET_CHECI_DATA = gql`
  query GetCheciData {
    checis(
      filter: {
        status_code_id: 300
      }
    ) {
      items {
        che_ci
        che_liang
        type_varchar
        che_liang_info {
          id
          id_name
          mm_name
        }
      }
      page
      page_size
    }
  }
`

const UPDATE_DELIVERY_GOODS_BRANCH = gql`
  mutation UpdateDeliveryGoodsBranch($auto_id: Int!, $input: DeliveryGoodsBranchInput!) {
    updateDeliveryGoodsBranch(auto_id: $auto_id, input: $input) {
      auto_id
      goods_id
      che_ci
    }
  }
`

export default {
  name: 'TransferList',
  filters: {
    statusFilter(status) {
      const statusMap = {
        1: 'success',
        0: 'info'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      leftTableLoading: false,
      rightTableLoading: false,
      refreshLoading: false,
      selectedTrain: '',
      productSearchLoading: false,
      productOptions: [],
      trainOptions: [],
      checiOptions: [],
      leftTableData: [],
      rightTableData: [],
      listQuery: {
        page: 1,
        limit: 10,
        branch_from_id: 1,
        checiFilter: '',
        goods_id: ''
      },
      total: 0
    }
  },
  created() {
    this.fetchData()
    this.fetchCheciData()
  },
  methods: {
    async searchProductNames(keyword) {
      if (!keyword) {
        this.productOptions = []
        return
      }
      this.productSearchLoading = true
      try {
        const { data } = await shwethe_data_client.query({
          query: SEARCH_PRODUCT_NAMES,
          variables: {
            keyword,
            page: 1,
            pageSize: 10,
            sortBy: 'jia_yi_id',
            sortOrder: 'DESC'
          }
        })
        this.productOptions = data.searchJiaYiNames.items
      } catch (e) {
        this.$message.error('ကုန်ပစ္စည်းရှာဖွေမှု မအောင်မြင်ပါ')
        console.error(e)
      }
      this.productSearchLoading = false
    },
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    async fetchData() {
      console.log(this.$store.state.user.fendianId)
      this.leftTableLoading = true
      try {
        const today = new Date()
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999)

        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_DELIVERY_GOODS_FROM,
          variables: {
            page: this.listQuery.page,
            limit: this.listQuery.limit,
            branch_from_id: this.$store.state.user.fendianId,
            create_datetime: {
              gte: startOfDay.toISOString(),
              lte: endOfDay.toISOString()
            }
          },
          fetchPolicy: 'network-only'
        })

        this.leftTableData = data.deliveryGoodsBranchesFromWithPagination.items
        this.total = data.deliveryGoodsBranchesFromWithPagination.total

        this.extractCheciOptions()
      } catch (error) {
        console.error('获取发货列表失败:', error)
        this.$message.error('获取发货列表失败')
      } finally {
        this.leftTableLoading = false
      }
    },
    extractCheciOptions() {
      const checiSet = new Set()
      this.leftTableData.forEach(item => {
        if (item.che_ci !== undefined && item.che_ci !== null) {
          checiSet.add(item.che_ci)
        }
      })

      this.checiOptions = Array.from(checiSet).map(checiValue => ({
        value: checiValue,
        label: `ကားအကြိမ် ${checiValue}`
      }))
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchFilteredData()
    },
    resetFilter() {
      this.listQuery.checiFilter = ''
      this.listQuery.goods_id = ''
      this.fetchData()
    },
    async fetchFilteredData() {
      this.leftTableLoading = true
      try {
        const today = new Date()
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999)

        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_DELIVERY_GOODS_FROM,
          variables: {
            page: this.listQuery.page,
            limit: this.listQuery.limit,
            branch_from_id: this.listQuery.branch_from_id,
            create_datetime: {
              gte: startOfDay.toISOString(),
              lte: endOfDay.toISOString()
            }
          },
          fetchPolicy: 'network-only'
        })

        let filteredData = data.deliveryGoodsBranchesFromWithPagination.items

        // 应用筛选条件
        if (this.listQuery.checiFilter !== '') {
          filteredData = filteredData.filter(item =>
            item.che_ci === this.listQuery.checiFilter
          )
        }

        if (this.listQuery.goods_id !== '') {
          filteredData = filteredData.filter(item =>
            item.goods_id === this.listQuery.goods_id
          )
        }

        this.leftTableData = filteredData
        this.total = filteredData.length
      } catch (error) {
        console.error('获取筛选数据失败:', error)
        this.$message.error('获取筛选数据失败')
      } finally {
        this.leftTableLoading = false
      }
    },
    handleTrainChange(val) {
      if (!val) {
        this.rightTableData = []
        return
      }

      this.rightTableLoading = true
      // 根据选择的车次加载对应的数据
      this.fetchTrainData(val)
    },
    async fetchTrainData(trainId) {
      try {
        this.rightTableLoading = true
        const today = new Date()
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999)

        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_DELIVERY_GOODS_BY_CHECI_All,
          variables: {
            page: 1,
            limit: 100,
            che_ci: parseInt(trainId),
            create_datetime: {
              gte: startOfDay.toISOString(),
              lte: endOfDay.toISOString()
            }
          },
          fetchPolicy: 'network-only'
        })

        this.rightTableData = data.deliveryGoodsBranchesFromWithPagination.items
      } catch (error) {
        console.error('获取车次数据失败:', error)
        this.$message.error('获取车次数据失败')
      } finally {
        this.rightTableLoading = false
      }
    },
    transferItem(row) {
      // 检查是否已选择车次
      if (!this.selectedTrain) {
        this.$message({
          message: '请先选择车次',
          type: 'warning'
        })
        return
      }

      // 检查是否已存在
      const isExist = this.rightTableData.some(item => item.auto_id === row.auto_id)
      if (isExist) {
        this.$message({
          message: '该商品已经添加到右侧表格',
          type: 'warning'
        })
        return
      }

      // 更新商品的车次信息
      this.updateDeliveryGoodsCheCI(row)
    },
    async updateDeliveryGoodsCheCI(row) {
      try {
        this.rightTableLoading = true
        const { data } = await deliveryGoodsSystemClient.mutate({
          mutation: UPDATE_DELIVERY_GOODS_BRANCH,
          variables: {
            auto_id: row.auto_id,
            input: {
              che_ci: parseInt(this.selectedTrain)
            }
          }
        })

        if (data && data.updateDeliveryGoodsBranch) {
          // 更新成功，添加到右侧表格
          const updatedRow = { ...row, che_ci: parseInt(this.selectedTrain) }

          // 创建一个新数组而不是直接修改原数组
          this.rightTableData = [...this.rightTableData, updatedRow]

          this.$message({
            message: '商品车次更新成功',
            type: 'success'
          })

          // 刷新左侧表格数据
          this.fetchData()

          // 也刷新右侧表格数据
          this.fetchTrainData(this.selectedTrain)
        }
      } catch (error) {
        console.error('更新商品车次失败:', error)
        this.$message.error('更新商品车次失败')
      } finally {
        this.rightTableLoading = false
      }
    },
    // 提交选择框中选择的数据
    async submitCheciData() {
      try {
        // 获取选择的商品ID
        const goodsId = this.listQuery.goods_id
        if (!goodsId) {
          this.$message({
            message: 'ကျေးဇူးပြု၍ ကုန်ပစ္စည်းရွေးချယ်ပါ',
            type: 'warning'
          })
          return
        }

        const now = new Date().toISOString()
        const data = {
          che_ci: 0,
          che_liang: this.listQuery.goods_id,
          datetime: now,
          id: goodsId,
          status_code_id: 300,
          type_varchar: 'tranfer_goods'
        }

        const result = await postCheci(data)

        // 检查是否成功
        if (result.success) {
          this.$message({
            message: 'ကားအကြိမ်အချက်အလက်များကို အောင်မြင်စွာ တင်သွင်းပြီးပါပြီ',
            type: 'success'
          })
          console.log('车次数据提交成功:', result.data)
        } else {
          throw new Error(result.error || '未知错误')
        }
      } catch (error) {
        console.error('车次数据提交失败:', error)
        this.$message.error('ကားအကြိမ်အချက်အလက်များကို တင်သွင်းရန် မအောင်မြင်ပါ')
      }
    },
    async removeItem(row) {
      try {
        this.rightTableLoading = true
        // 调用API将che_ci更新为0
        const { data } = await deliveryGoodsSystemClient.mutate({
          mutation: UPDATE_DELIVERY_GOODS_BRANCH,
          variables: {
            auto_id: row.auto_id,
            input: {
              che_ci: 0 // 将车次设置为0
            }
          }
        })

        if (data && data.updateDeliveryGoodsBranch) {
          // 从右侧表格中移除该行
          this.rightTableData = this.rightTableData.filter(item => item.auto_id !== row.auto_id)

          this.$message({
            message: '商品已移回车次0',
            type: 'success'
          })

          // 刷新左侧表格数据
          this.fetchData()

          // 如果右侧表格仍有选择的车次，刷新右侧表格数据
          if (this.selectedTrain) {
            this.fetchTrainData(this.selectedTrain)
          }
        }
      } catch (error) {
        console.error('移除商品失败:', error)
        this.$message.error('移除商品失败')
      } finally {
        this.rightTableLoading = false
      }
    },
    async fetchCheciData() {
      try {
        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_CHECI_DATA,
          fetchPolicy: 'network-only'
        })

        if (data && data.checis && data.checis.items) {
          this.trainOptions = data.checis.items.map(item => ({
            value: item.che_ci.toString(),
            label: `${item.che_ci} - ${item.che_liang_info.id_name} (${item.che_liang_info.mm_name})`
          }))
        }
      } catch (error) {
        console.error('获取车次数据失败:', error)
        this.$message.error('获取车次数据失败')
      }
    },
    async handleRefresh() {
      try {
        this.refreshLoading = true
        await this.fetchCheciData()
        this.$message({
          message: '刷新成功',
          type: 'success'
        })
      } catch (error) {
        console.error('刷新失败:', error)
        this.$message.error('刷新失败')
      } finally {
        this.refreshLoading = false
      }
    },
    async handleSummit() {
      if (!this.selectedTrain) {
        this.$message.warning('请选择车次号码')
        return
      }
      try {
        this.refreshLoading = true
        await updateCheciStatus(this.selectedTrain, { status_code_id: 200 })
        this.$message.success('ကားအကြိမ်အခြေအနေ အောင်မြင်စွာ အပ်ဒိတ်လုပ်ပြီးပါပြီ')

        // 刷新数据
        await this.fetchCheciData()
        if (this.selectedTrain) {
          await this.fetchTrainData(this.selectedTrain)
        }
      } catch (error) {
        console.error('车次状态更新失败:', error)
        if (error.response) {
          this.$message.error(`ကားအကြိမ်အခြေအနေ အပ်ဒိတ်လုပ်ခြင်း မအောင်မြင်ပါ: ${error.response.data?.message || '未知错误'}`)
        } else if (error.request) {
          this.$message.error('ဆာဗာနှင့် ဆက်သွယ်၍မရပါ။ ကျေးဇူးပြု၍ သင့်အင်တာနက်ကို စစ်ဆေးပါ')
        } else {
          this.$message.error('ကားအကြိမ်အခြေအနေ အပ်ဒိတ်လုပ်ခြင်း မအောင်မြင်ပါ')
        }
      } finally {
        this.refreshLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-inline {
  display: flex;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;
    margin-right: 20px;
  }
}

.transfer-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;

  .left-table, .right-table {
    width: 48%;
  }

  .right-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .select-container {
      display: flex;
      align-items: center;

      .select-label {
        margin-right: 10px;
      }
    }
  }
}
</style>

