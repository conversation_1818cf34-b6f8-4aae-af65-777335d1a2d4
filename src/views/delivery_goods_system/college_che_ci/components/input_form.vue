<template>
  <div class="input-form">
    <h1>ကုန်ပစ္စည်းပေးပို့မှုစနစ်</h1>
    <div class="right-table">
      <div class="right-header">
        <div class="select-container">
          <el-button
            type="primary"
            icon="el-icon-refresh"
            :loading="refreshLoading"
            style="margin-right: 10px"
            @click="handleRefresh"
          >刷新</el-button>
          <span class="select-label">车次选择：</span>
          <el-select
            v-model="selectedTrain"
            placeholder="请选择车次"
            filterable
            clearable
            @change="handleTrainChange"
          >
            <el-option
              v-for="item in trainOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            :loading="refreshLoading"
            style="margin-right: 10px"
            @click="handleSummit"
          >Summit</el-button>
        </div>
      </div>

      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="ID" prop="goods_id" width="80" align="center" />
        <el-table-column label="产品ID名称" prop="product_info.product_id_name" min-width="120" />
        <el-table-column label="产品缅文名称" prop="product_info.product_mm_name" min-width="120" />
        <el-table-column label="产品英文名称" prop="product_info.product_d_name" min-width="120" />
        <el-table-column label="日期" prop="create_datetime" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ formatDate(row.create_datetime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template slot-scope="{row}">
            <el-tag :type="row.status === 1 ? 'success' : 'info'">
              {{ row.status === 1 ? '激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="产品数量" prop="product_qty" width="100" align="center">
          <template slot-scope="{row}">
            <span>{{ row.product_qty }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分配数量(从)" prop="delivery_goods_branch_from_tb.qty" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.delivery_goods_branch_from_tb ? row.delivery_goods_branch_from_tb.qty : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分配数量(到)" prop="delivery_goods_branch_to_tb.qty" width="120" align="center">
          <template slot-scope="{row}">
            <span>{{ row.delivery_goods_branch_to_tb ? row.delivery_goods_branch_to_tb.qty : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车次" prop="che_ci" width="100" align="center">
          <template slot-scope="{row}">
            <span>{{ row.che_ci }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" icon="el-icon-check" :disabled="row.status === 200" @click="submitItem(row)" />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { deliveryGoodsSystemClient } from '@/apolloClient'
import { updateCheciStatus } from '@/api/rest/shwethe_data'
import gql from 'graphql-tag'

const GET_DELIVERY_GOODS_BY_CHECI = gql`
  query GetDeliveryGoodsByCheCI($page: Int!, $limit: Int!, $branch_to_id: Int!, $che_ci: Int!) {
    deliveryGoodsBranchesFromWithPagination(
      page: $page,
      limit: $limit,
      filter: {
        branch_to_id: $branch_to_id,
        che_ci: $che_ci
      }
    ) {
      items {
        auto_id
        goods_id
        branch_to_id
        branch_from_id
        product_qty
        create_datetime
        goods_type_from
        che_ci
        status
        product_info {
          product_id
          product_id_name
          product_d_name
          product_mm_name
        }
        delivery_goods_branch_from_tb {
          jia_yi_fang
          qty
        }
        delivery_goods_branch_to_tb {
          jia_yi_fang
          qty
        }
      }
      total
    }
  }
`

const GET_CHECI_DATA = gql`
  query GetCheciData {
    checis(
      filter: {
        type_varchar: "tranfer_goods"
        status_code_id: 200
      }
    ) {
      items {
        che_ci
        che_liang
        type_varchar
        status_code_id
        che_liang_info {
          id
          id_name
          mm_name
        }
      }
      page
      page_size
    }
  }
`

const UPDATE_DELIVERY_GOODS_BRANCH = gql`
  mutation UpdateDeliveryGoodsBranch($auto_id: Int!, $input: DeliveryGoodsBranchInput!) {
    updateDeliveryGoodsBranch(auto_id: $auto_id, input: $input) {
      auto_id
      goods_id
      branch_to_id
      branch_from_id
      status
    }
  }
`

export default {
  name: 'InputForm',
  data() {
    return {
      refreshLoading: false,
      tableLoading: false,
      selectedTrain: '',
      trainOptions: [],
      tableData: [],
      listQuery: {
        page: 1,
        limit: 100,
        branch_from_id: 1
      }
    }
  },
  // computed: {
  //   isTrainStatus200() {
  //     if (!this.selectedTrain) return false
  //     const selectedTrainData = this.trainOptions.find(option => option.value === this.selectedTrain)
  //     return selectedTrainData && selectedTrainData.status_code_id === 200
  //   }
  // },
  created() {
    this.fetchCheciData()
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    handleTrainChange(val) {
      if (!val) {
        this.tableData = []
        return
      }

      this.tableLoading = true
      this.fetchTrainData(val)
    },
    async fetchTrainData(trainId) {
      try {
        this.tableLoading = true
        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_DELIVERY_GOODS_BY_CHECI,
          variables: {
            page: this.listQuery.page,
            limit: this.listQuery.limit,
            branch_to_id: this.$store.state.user.fendianId,
            che_ci: parseInt(trainId)
          },
          fetchPolicy: 'network-only'
        })

        this.tableData = data.deliveryGoodsBranchesFromWithPagination.items
      } catch (error) {
        console.error('获取车次数据失败:', error)
        this.$message.error('获取车次数据失败')
      } finally {
        this.tableLoading = false
      }
    },
    async submitItem(row) {
      try {
        this.tableLoading = true
        const { data } = await deliveryGoodsSystemClient.mutate({
          mutation: UPDATE_DELIVERY_GOODS_BRANCH,
          variables: {
            auto_id: row.auto_id,
            input: {
              status: 200
            }
          }
        })

        if (data && data.updateDeliveryGoodsBranch) {
          this.tableData = this.tableData.filter(item => item.auto_id !== row.auto_id)

          this.$message({
            message: '提交成功',
            type: 'success'
          })

          if (this.selectedTrain) {
            this.fetchTrainData(this.selectedTrain)
          }
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.tableLoading = false
      }
    },
    async fetchCheciData() {
      try {
        const { data } = await deliveryGoodsSystemClient.query({
          query: GET_CHECI_DATA,
          fetchPolicy: 'network-only'
        })

        if (data && data.checis && data.checis.items) {
          this.trainOptions = data.checis.items.map(item => ({
            value: item.che_ci.toString(),
            label: `${item.che_ci} - ${item.che_liang_info.id_name} (${item.che_liang_info.mm_name})`,
            status_code_id: item.status_code_id
          }))
        }
      } catch (error) {
        console.error('获取车次数据失败:', error)
        this.$message.error('获取车次数据失败')
      }
    },
    async handleRefresh() {
      try {
        this.refreshLoading = true
        await this.fetchCheciData()
        this.$message({
          message: '刷新成功',
          type: 'success'
        })
      } catch (error) {
        console.error('刷新失败:', error)
        this.$message.error('刷新失败')
      } finally {
        this.refreshLoading = false
      }
    },

    async handleSummit() {
      if (!this.selectedTrain) {
        this.$message.warning('请选择车次')
        return
      }
      try {
        this.refreshLoading = true
        await updateCheciStatus(this.selectedTrain, { status_code_id: 200 })
        this.$message.success('车次状态更新成功')
        await this.fetchCheciData()
      } catch (error) {
        this.$message.error('车次状态更新失败')
        console.error(error)
      } finally {
        this.refreshLoading = false
      }
    }
  }
}
</script>

<style scoped>
.input-form {
  padding: 20px;
}
.right-table {
  margin-top: 20px;
}
.right-header {
  margin-bottom: 20px;
}
.select-container {
  display: flex;
  align-items: center;
}
.select-label {
  margin-right: 10px;
}
</style>
