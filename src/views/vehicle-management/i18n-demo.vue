<template>
  <div class="i18n-demo">
    <div class="app-container">
      <div class="demo-header">
        <h1>{{ $t('vehicle.status.title') }}</h1>
        <div class="lang-selector">
          <el-radio-group v-model="selectedLang" @change="changeLang">
            <el-radio-button label="my">မြန်မာ</el-radio-button>
            <el-radio-button label="zh">中文</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <el-divider />

      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <div slot="header">
                <span>{{ $t('vehicle.list.title') }}</span>
              </div>
              <el-table :data="demoData" style="width: 100%">
                <el-table-column :label="$t('vehicle.list.licensePlate')" prop="licensePlate" />
                <el-table-column :label="$t('vehicle.list.brand')" prop="brand" />
                <el-table-column :label="$t('vehicle.status.current')" prop="status">
                  <template slot-scope="scope">
                    <el-tag 
                      :type="getStatusTagType(scope.row.status)"
                      :color="getStatusColor(scope.row.status)"
                      :style="{ color: 'white' }"
                    >
                      {{ getStatusName(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card>
              <div slot="header">
                <span>{{ $t('vehicle.statistics.statusStats') }}</span>
              </div>
              <div class="status-stats">
                <div 
                  v-for="stat in statusStats" 
                  :key="stat.status"
                  class="stat-item"
                >
                  <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                    <i :class="`el-icon-${stat.icon}`"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-name">{{ getStatusName(stat.status) }}</div>
                    <div class="stat-count">{{ stat.count }} {{ $t('vehicle.statistics.vehicles') }}</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-divider />

        <div class="demo-buttons">
          <el-button type="primary" @click="showDialog = true">
            {{ $t('vehicle.dialog.updateStatus') }}
          </el-button>
          <el-button type="success">
            {{ $t('vehicle.common.search') }}
          </el-button>
          <el-button type="info">
            {{ $t('vehicle.common.refresh') }}
          </el-button>
          <el-button type="warning">
            {{ $t('vehicle.common.export') }}
          </el-button>
        </div>
      </div>

      <!-- 状态更新演示对话框 -->
      <el-dialog
        :title="$t('vehicle.dialog.updateStatus')"
        :visible.sync="showDialog"
        width="400px"
      >
        <el-form label-width="100px">
          <el-form-item :label="$t('vehicle.status.current')">
            <el-tag color="#52c41a" style="color: white">
              {{ $t('vehicle.status.active') }}
            </el-tag>
          </el-form-item>
          <el-form-item :label="$t('vehicle.status.new')">
            <el-select v-model="selectedStatus" :placeholder="$t('vehicle.status.selectNewStatus')">
              <el-option
                v-for="status in statusOptions"
                :key="status"
                :label="getStatusName(status)"
                :value="status"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('vehicle.status.remark')">
            <el-input 
              v-model="statusRemark"
              type="textarea"
              :placeholder="$t('vehicle.status.enterRemark')"
            />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="showDialog = false">{{ $t('vehicle.common.cancel') }}</el-button>
          <el-button type="primary" @click="updateStatus">{{ $t('vehicle.common.confirm') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  name: 'I18nDemo',
  data() {
    return {
      selectedLang: this.$i18n.locale,
      showDialog: false,
      selectedStatus: '',
      statusRemark: '',
      demoData: [
        {
          licensePlate: '1A-2345',
          brand: 'Toyota',
          status: 'active'
        },
        {
          licensePlate: '2B-6789',
          brand: 'Honda',
          status: 'maintenance'
        },
        {
          licensePlate: '3C-1234',
          brand: 'Nissan',
          status: 'parked'
        }
      ],
      statusOptions: [
        'active', 'inactive', 'maintenance', 'parked', 
        'in_transit', 'reserved', 'scrapped', 'sold', 'lost', 'damaged'
      ],
      statusStats: [
        { status: 'active', count: 15, color: '#52c41a', icon: 'check' },
        { status: 'maintenance', count: 3, color: '#1890ff', icon: 'setting' },
        { status: 'parked', count: 8, color: '#722ed1', icon: 'location' },
        { status: 'inactive', count: 2, color: '#faad14', icon: 'warning' }
      ]
    }
  },
  methods: {
    changeLang(lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('app/setLanguage', lang)
      this.$message.success(this.$t('navbar.langSelect.success'))
    },
    getStatusName(status) {
      const i18nKey = `vehicle.status.${status}`
      if (this.$te(i18nKey)) {
        return this.$t(i18nKey)
      }
      return status
    },
    getStatusColor(status) {
      const colorMap = {
        'active': '#52c41a',
        'inactive': '#faad14',
        'maintenance': '#1890ff',
        'parked': '#722ed1',
        'in_transit': '#13c2c2',
        'reserved': '#eb2f96',
        'scrapped': '#8c8c8c',
        'sold': '#52c41a',
        'lost': '#f5222d',
        'damaged': '#ff4d4f'
      }
      return colorMap[status] || '#8c8c8c'
    },
    getStatusTagType(status) {
      const typeMap = {
        'active': 'success',
        'inactive': 'warning',
        'maintenance': 'primary',
        'parked': '',
        'in_transit': 'info',
        'reserved': 'danger',
        'scrapped': 'info',
        'sold': 'success',
        'lost': 'danger',
        'damaged': 'danger'
      }
      return typeMap[status] || 'info'
    },
    updateStatus() {
      this.$message.success(this.$t('vehicle.status.updateSuccess'))
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.i18n-demo {
  .demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h1 {
      margin: 0;
      color: #333;
    }
  }
  
  .demo-content {
    .status-stats {
      .stat-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          
          i {
            color: white;
            font-size: 16px;
          }
        }
        
        .stat-info {
          .stat-name {
            font-weight: 500;
            color: #333;
          }
          
          .stat-count {
            color: #666;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .demo-buttons {
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style> 