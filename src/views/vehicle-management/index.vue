<template>
  <div class="vehicle-management">
    <div class="app-container">
      <div class="filter-container">
        <!-- 自动更新进度提示 -->
        <div v-if="autoUpdating" class="auto-update-progress">
          <i class="el-icon-loading"></i>
          <span>正在自动更新车辆状态（检查所有记录）... ({{ autoUpdateProgress }}/{{ autoUpdateTotal }})</span>
          <el-progress 
            :percentage="Math.round((autoUpdateProgress / autoUpdateTotal) * 100)" 
            :show-text="false"
            style="width: 200px; margin-left: 10px;"
          />
        </div>
        
        <el-input
          v-model="listQuery.q"
          :placeholder="$t('vehicle.list.searchPlaceholder')"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <!-- 状态按钮组 -->
        <div class="filter-item status-button-group">
          <el-button
            :type="listQuery.status === '' ? 'primary' : 'default'"
            :plain="listQuery.status !== ''"
            size="small"
            @click="resetAllFilters"
          >
            全部{{ totalCount > 0 ? ` (${totalCount})` : '' }}
          </el-button>
          <el-button
            v-for="option in statusOptions"
            :key="option.status"
            :type="listQuery.status === option.status ? 'primary' : 'default'"
            :plain="listQuery.status !== option.status"
            size="small"
            @click="handleStatusChange(option.status)"
            :disabled="statusCounts[option.status] === 0"
            :class="{ 'filter-selected': currentFilters.selectedStatus === option.status }"
          >
            {{ getStatusName(option.status) }}{{ statusCounts[option.status] !== undefined ? ` (${statusCounts[option.status]})` : '' }}
          </el-button>
        </div>
        
        <!-- 车辆类型按钮组 -->
        <div class="filter-item type-button-group">
          <el-button
            :type="listQuery.type_id === '' || listQuery.type_id === null ? 'primary' : 'default'"
            :plain="listQuery.type_id !== '' && listQuery.type_id !== null"
            size="small"
            @click="resetAllFilters"
          >
            全部类型{{ totalCount > 0 ? ` (${totalCount})` : '' }}
          </el-button>
          <el-button
            v-for="type in vehicleTypes"
            :key="type.type_id"
            :type="listQuery.type_id === type.type_id ? 'primary' : 'default'"
            :plain="listQuery.type_id !== type.type_id"
            size="small"
            @click="handleTypeChange(type.type_id)"
            :disabled="typeCounts[type.type_id] === 0"
            :class="{ 'filter-selected': currentFilters.selectedType === type.type_id }"
          >
            {{ type.type_name }}{{ typeCounts[type.type_id] !== undefined ? ` (${typeCounts[type.type_id]})` : '' }}
          </el-button>
        </div>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('vehicle.common.search') }}
        </el-button>
        <el-button
          class="filter-item"
          type="info"
          icon="el-icon-info"
          @click="debugQuery"
          style="margin-left: 5px;"
        >
          调试查询
        </el-button>
        <el-button
          class="filter-item"
          type="danger"
          icon="el-icon-warning"
          @click="showUpdateGuide"
          style="margin-left: 5px;"
        >
          更新指南
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          {{ $t('vehicle.list.addVehicle') }}
        </el-button>
        <el-button
          class="filter-item"
          type="success"
          icon="el-icon-pie-chart"
          @click="goToStatusPage"
        >
          {{ $t('vehicle.status.management') }}
        </el-button>
        <el-button
          class="filter-item"
          type="warning"
          icon="el-icon-date"
          @click="showTodayInserts"
        >
          今日记录
        </el-button>
        <el-button
          class="filter-item"
          type="success"
          icon="el-icon-refresh"
          @click="autoUpdateAllVehicleStatus"
          :loading="autoUpdating"
          :disabled="autoUpdating"
        >
          自动更新状态（全部记录）
        </el-button>
        <el-button
          class="filter-item"
          type="info"
          icon="el-icon-pie-chart"
          @click="showSummary"
        >
          统计汇总
        </el-button>
        
        <!-- 排序指示器 -->
        <div class="filter-item sort-indicator">
          <el-tag type="success" size="small">
            排序: {{ getSortDisplayName(listQuery.order_by) }}
            <i 
              :class="listQuery.order_direction === 'asc' ? 'el-icon-sort-up' : 'el-icon-sort-down'"
              style="margin-left: 4px;"
            />
          </el-tag>
          <el-button
            type="text"
            size="mini"
            @click="toggleSortDirection"
            style="margin-left: 5px;"
          >
            切换排序
          </el-button>
        </div>
        
        <!-- 筛选状态指示器 -->
        <div v-if="currentFilters.selectedType || currentFilters.selectedStatus" class="filter-status-indicator">
          <el-tag type="info" size="small" closable @close="resetAllFilters">
            当前筛选: 
            <span v-if="currentFilters.selectedType">
              {{ getTypeName(currentFilters.selectedType) }}
            </span>
            <span v-if="currentFilters.selectedType && currentFilters.selectedStatus"> + </span>
            <span v-if="currentFilters.selectedStatus">
              {{ getStatusName(currentFilters.selectedStatus) }}
            </span>
          </el-tag>
        </div>
      </div>

      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="handleSortChange"
      >
        <el-table-column label="ID" prop="auto_id" sortable="custom" align="center" width="80">
          <template slot-scope="{row}">
            <span>{{ row.auto_id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车牌号" prop="license_plate" sortable="custom" width="150px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.license_plate }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.model')" prop="model" sortable="custom" width="150px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.model }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.brand')" prop="brand" sortable="custom" width="120px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.brand }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.color')" prop="color" sortable="custom" width="100px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.color }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.year')" prop="year" sortable="custom" width="100px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.year }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.type')" width="120px" align="center">
          <template slot-scope="{row}">
            <el-tag type="info">
              {{ row.vehicle_type ? row.vehicle_type.type_name : '-' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.status.current')" width="120px" align="center">
          <template slot-scope="{row}">
            <vehicle-status-manager
              :vehicle-id="row.che_liang_id"
              :current-status="row.status || 'active'"
              :remark="row.status_remark"
              :quick-edit="true"
              @status-updated="handleStatusUpdated"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态更新时间" prop="status_updated_at" sortable="custom" width="160px" align="center">
          <template slot-scope="{row}">
            <span v-if="row.status_updated_at">
              {{ formatDateTime(row.status_updated_at) }}
            </span>
            <span v-else style="color: #909399;">未更新</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.image')" width="100px" align="center">
          <template slot-scope="{row}">
            <el-image
              v-if="row.images && row.images.car_images && row.images.car_images.length > 0"
              :src="row.images.car_images[0]"
              :preview-src-list="row.images.car_images"
              style="width: 50px; height: 40px; border-radius: 4px;"
              fit="cover"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.actions')" align="center" width="380" class-name="small-padding fixed-width">
          <template slot-scope="{row,$index}">
            <el-button type="info" size="mini" @click="handleViewDetail(row)">
              {{ $t('vehicle.common.detail') }}
            </el-button>
            <el-button type="primary" size="mini" @click="handleUpdate(row)">
              {{ $t('vehicle.common.edit') }}
            </el-button>
            <el-button type="warning" size="mini" @click="handleViewInserts(row)">
              每日输入记录
            </el-button>
            <el-button 
              type="success" 
              size="mini" 
              @click="handleUpdateVehicleStatus(row)"
              :loading="row.statusUpdating"
            >
              更新状态（最新记录）
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />

      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="temp"
          label-position="left"
          label-width="90px"
          style="width: 400px; margin-left:50px;"
        >
          <el-form-item :label="$t('vehicle.list.licensePlate')" prop="license_plate">
            <el-input v-model="temp.license_plate" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.list.model')" prop="model">
            <el-input v-model="temp.model" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.list.brand')" prop="brand">
            <el-input v-model="temp.brand" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.list.color')" prop="color">
            <el-input v-model="temp.color" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.list.year')" prop="year">
            <el-input v-model="temp.year" :placeholder="$t('vehicle.form.yearPlaceholder')" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            {{ $t('vehicle.common.cancel') }}
          </el-button>
          <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
            {{ $t('vehicle.common.confirm') }}
          </el-button>
        </div>
      </el-dialog>

      <!-- 每日输入记录对话框 -->
      <el-dialog 
        title="车辆每日输入记录" 
        :visible.sync="insertsDialogVisible"
        width="90%"
        top="5vh"
      >
        <div class="inserts-dialog-content">
          <div class="inserts-header">
            <div class="vehicle-info">
              <h3>车辆: {{ currentVehicle.license_plate }} ({{ currentVehicle.model }})</h3>
              <div class="status-info">
                <span>当前状态: </span>
                <el-tag :type="currentVehicle.status === 'active' ? 'success' : 'info'">
                  {{ currentVehicle.status }}
                </el-tag>
                <span v-if="currentVehicle.status_updated_at" class="status-time">
                  (最后更新: {{ formatDateTime(currentVehicle.status_updated_at) }})
                </span>
                <span v-else class="status-time">
                  (未更新)
                </span>
              </div>
            </div>
            <div class="inserts-actions">
              <el-button 
                type="primary" 
                size="small" 
                icon="el-icon-refresh" 
                @click="refreshInserts"
              >
                刷新
              </el-button>
            </div>
          </div>
          
          <el-table
            v-loading="insertsLoading"
            :data="insertsList"
            border
            fit
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column label="ID" prop="auto_id" width="80" align="center" sortable />
            <el-table-column label="日期时间" width="160" align="center" sortable>
              <template slot-scope="{row}">
                <span>{{ formatDateTime(row.datetime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="司机ID" prop="jia_yi_id_driver" width="100" align="center" />
            <el-table-column label="分点ID" prop="fen_dian_id" width="80" align="center" />
            <el-table-column label="状态" prop="status" width="100" align="center">
              <template slot-scope="{row}">
                <div>
                  <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                    {{ row.status }}
                  </el-tag>
                  <div v-if="isStatusAlreadyUpdated(row)" class="status-updated-hint">
                    <i class="el-icon-check" style="color: #67C23A; font-size: 12px;"></i>
                    <span style="color: #67C23A; font-size: 12px;">已同步</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="位置" width="160" align="center">
              <template slot-scope="{row}">
                <div v-if="row.latitude && row.longitude" class="location-info">
                  <div class="location-text">
                    {{ row.latitude.toFixed(6) }}
                  </div>
                  <div class="location-text">
                    {{ row.longitude.toFixed(6) }}
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="图片" width="100" align="center">
              <template slot-scope="{row}">
                <el-image
                  v-if="row.image"
                  :src="'http://' + row.image"
                  :preview-src-list="['http://' + row.image]"
                  style="width: 50px; height: 40px; border-radius: 4px;"
                  fit="cover"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" width="100" align="center">
              <template slot-scope="{row}">
                <span>{{ row.where || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="{row}">
                <el-button
                  type="primary"
                  size="mini"
                  @click="updateStatusFromInsert(row)"
                  :disabled="isStatusAlreadyUpdated(row)"
                  :loading="row.updating"
                >
                  {{ isStatusAlreadyUpdated(row) ? '已更新' : '更新状态' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="inserts-pagination">
            <el-pagination
              v-show="insertsTotal > 0"
              :current-page="insertsQuery.page"
              :page-size="insertsQuery.size"
              :total="insertsTotal"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleInsertsPageSizeChange"
              @current-change="handleInsertsPageChange"
            />
          </div>
        </div>
        
        <div slot="footer" class="dialog-footer">
          <el-button @click="insertsDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>

      <!-- 今日记录对话框 -->
      <el-dialog 
        title="今日所有车辆进入记录" 
        :visible.sync="todayInsertsDialogVisible"
        width="95%"
        top="3vh"
      >
        <div class="today-inserts-content">
          <div class="today-inserts-header">
            <div class="today-info">
              <h3>今日记录总览</h3>
              <div class="today-stats">
                <span>总记录数: {{ todayInsertsTotal }}</span>
                <span style="margin-left: 20px;">当前页: {{ todayInsertsQuery.page }}</span>
                <span v-if="todaySelectedInserts.length > 0" style="margin-left: 20px; color: #409EFF;">
                  已选择: {{ todaySelectedInserts.length }} 条
                </span>
              </div>
            </div>
            <div class="today-actions">
              <el-button 
                type="success" 
                size="small" 
                icon="el-icon-check" 
                @click="batchUpdateTodayStatus"
                :disabled="todayInsertsList.length === 0"
              >
                批量更新状态
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                icon="el-icon-refresh" 
                @click="refreshTodayInserts"
              >
                刷新
              </el-button>
            </div>
          </div>
          
          <el-table
            v-loading="todayInsertsLoading"
            :data="todayInsertsList"
            border
            fit
            highlight-current-row
            style="width: 100%;"
            @selection-change="handleTodaySelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
              :selectable="(row) => !isTodayStatusUpdated(row)"
            />
            <el-table-column label="记录ID" prop="auto_id" width="80" align="center" sortable />
            <el-table-column label="车辆ID" prop="jia_yi_id_vehicle" width="100" align="center" />
            <el-table-column label="司机ID" prop="jia_yi_id_driver" width="100" align="center" />
            <el-table-column label="日期时间" width="160" align="center" sortable>
              <template slot-scope="{row}">
                <span>{{ formatDateTime(row.datetime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="分点ID" prop="fen_dian_id" width="80" align="center" />
            <el-table-column label="状态" prop="status" width="120" align="center">
              <template slot-scope="{row}">
                <div>
                  <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                    {{ row.status }}
                  </el-tag>
                  <div v-if="isTodayStatusUpdated(row)" class="status-updated-hint">
                    <i class="el-icon-check" style="color: #67C23A; font-size: 12px;"></i>
                    <span style="color: #67C23A; font-size: 12px;">已同步</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="位置" width="160" align="center">
              <template slot-scope="{row}">
                <div v-if="row.latitude && row.longitude" class="location-info">
                  <div class="location-text">
                    {{ row.latitude.toFixed(6) }}
                  </div>
                  <div class="location-text">
                    {{ row.longitude.toFixed(6) }}
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="图片" width="100" align="center">
              <template slot-scope="{row}">
                <el-image
                  v-if="row.image"
                  :src="'http://' + row.image"
                  :preview-src-list="['http://' + row.image]"
                  style="width: 50px; height: 40px; border-radius: 4px;"
                  fit="cover"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" width="100" align="center">
              <template slot-scope="{row}">
                <span>{{ row.where || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="{row}">
                <el-button
                  type="primary"
                  size="mini"
                  @click="updateTodayStatusFromInsert(row)"
                  :disabled="isTodayStatusUpdated(row)"
                  :loading="row.updating"
                >
                  {{ isTodayStatusUpdated(row) ? '已更新' : '更新状态' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="today-pagination">
            <el-pagination
              v-show="todayInsertsTotal > 0"
              :current-page="todayInsertsQuery.page"
              :page-size="todayInsertsQuery.size"
              :total="todayInsertsTotal"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleTodayPageSizeChange"
              @current-change="handleTodayPageChange"
            />
          </div>
        </div>
        
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeTodayInsertsDialog">关闭</el-button>
        </div>
      </el-dialog>
      
      <!-- 更新指南对话框 -->
      <el-dialog 
        title="状态更新指南" 
        :visible.sync="updateGuideVisible"
        width="80%"
        top="5vh"
      >
        <div class="update-guide-content">
          <h3>🔄 当前系统中有以下更新方式：</h3>
          
          <div class="guide-section">
            <h4>1. 🚀 自动状态更新（推荐）</h4>
            <p>功能：页面加载时自动更新所有车辆状态</p>
            <ul>
              <li>✅ 页面打开时自动执行</li>
              <li>✅ 基于所有insert记录更新（不限于今天）</li>
              <li>✅ 显示更新进度和结果统计</li>
              <li>✅ 检查每个车辆的最新记录</li>
              <li>✅ 智能跳过无需更新的车辆</li>
              <li>✅ 手动触发：点击 <el-button type="success" size="mini" disabled>自动更新状态</el-button> 按钮</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>2. 📅 今日记录更新</h4>
            <p>位置：筛选区域的 <el-button type="warning" size="mini" disabled>今日记录</el-button> 按钮</p>
            <p>功能：查看今天所有车辆的进入记录，可以批量更新状态</p>
            <ul>
              <li>✅ 显示今天所有车辆的insert记录</li>
              <li>✅ 支持多选批量更新状态</li>
              <li>✅ 自动检测已更新的记录</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>3. ✅ 直接更新车辆状态</h4>
            <p>位置：每个车辆行的 <el-button type="success" size="mini" disabled>更新状态</el-button> 按钮</p>
            <p>功能：根据车辆所有历史记录中的最新记录更新状态</p>
            <ul>
              <li>✅ 自动获取该车辆的所有insert记录</li>
              <li>✅ 查找该车辆历史上最新的记录</li>
              <li>✅ 一键更新车辆状态</li>
              <li>✅ 智能判断是否需要更新</li>
              <li>✅ 实时反馈更新结果和记录时间</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>4. 🚗 单个车辆记录更新</h4>
            <p>位置：每个车辆行的 <el-button type="warning" size="mini" disabled>每日输入记录</el-button> 按钮</p>
            <p>功能：查看特定车辆的历史记录，可以单独更新状态</p>
            <ul>
              <li>✅ 显示特定车辆的所有insert记录</li>
              <li>✅ 支持单条记录更新状态</li>
              <li>✅ 显示状态同步情况</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>5. 🔍 如何查看更新状态</h4>
            <p>在主车辆列表中新增了 <strong>"状态更新时间"</strong> 列</p>
            <ul>
              <li>✅ 显示最后一次状态更新的时间</li>
              <li>✅ 如果从未更新显示"未更新"</li>
              <li>✅ 时间格式：YYYY-MM-DD HH:mm:ss</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>6. 📊 更新流程</h4>
            <div class="process-steps">
              <div class="step">
                <span class="step-number">1</span>
                <span>🚀 最推荐：页面自动更新或点击"自动更新状态"</span>
              </div>
              <div class="step">
                <span class="step-number">2</span>
                <span>或点击每行的绿色"更新状态"按钮</span>
              </div>
              <div class="step">
                <span class="step-number">3</span>
                <span>系统获取每个车辆的所有insert记录</span>
              </div>
              <div class="step">
                <span class="step-number">4</span>
                <span>查找车辆历史上最新的记录并更新状态</span>
              </div>
              <div class="step">
                <span class="step-number">5</span>
                <span>显示更新结果、最新状态时间和记录时间</span>
              </div>
            </div>
          </div>
          
          <div class="guide-section">
            <h4>7. 💡 当前API配置</h4>
            <div class="api-info">
              <p><strong>获取车辆最新记录:</strong> GET /api/v1/inserts/vehicle/{vehicleId}?page=1&size=1</p>
              <p><strong>获取今日记录:</strong> GET /api/v1/inserts/today?page=1&size=1000</p>
              <p><strong>获取车辆历史记录:</strong> GET /api/v1/inserts/vehicle/{vehicleId}</p>
              <p><strong>更新状态:</strong> PUT /api/v1/vehicles/{vehicleId}/status</p>
              <p><strong>更新逻辑:</strong> 从车辆所有历史记录中获取最新记录</p>
              <p><strong>状态映射:</strong> pause → parked, repair → maintenance (其他状态保持不变)</p>
            </div>
          </div>
        </div>
        
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="updateGuideVisible = false">我知道了</el-button>
        </div>
      </el-dialog>
      
      <!-- 统计汇总对话框 -->
      <el-dialog 
        title="车辆统计汇总" 
        :visible.sync="summaryDialogVisible"
        width="90%"
        top="3vh"
      >
        <div class="summary-content" v-loading="summaryLoading">
          <div class="summary-header">
            <div class="summary-total">
              <h3>📊 总体统计</h3>
              <div class="total-info">
                <span class="total-number">{{ summaryData.total_vehicles }}</span>
                <span class="total-label">总车辆数</span>
              </div>
            </div>
            <div class="summary-actions">
              <el-button 
                type="primary" 
                size="small" 
                icon="el-icon-refresh" 
                @click="refreshSummary"
                :loading="summaryLoading"
              >
                刷新数据
              </el-button>
            </div>
          </div>
          
          <el-row :gutter="20">
            <!-- 状态统计 -->
            <el-col :span="12">
              <div class="stat-section">
                <h4>🚗 状态统计</h4>
                <el-table :data="summaryData.status_stats" size="small" border>
                  <el-table-column prop="status" label="状态" width="120">
                    <template slot-scope="{row}">
                      <el-tag :type="getStatusTagType(row.status)">{{ getStatusDisplayName(row.status) }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="count" label="数量" align="center" />
                  <el-table-column label="比例" align="center">
                    <template slot-scope="{row}">
                      {{ ((row.count / summaryData.total_vehicles) * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
            
            <!-- 类型统计 -->
            <el-col :span="12">
              <div class="stat-section">
                <h4>🔧 类型统计</h4>
                <el-table :data="summaryData.type_stats" size="small" border>
                  <el-table-column prop="type_name" label="类型" />
                  <el-table-column prop="vehicle_count" label="数量" align="center" />
                  <el-table-column label="比例" align="center">
                    <template slot-scope="{row}">
                      {{ ((row.vehicle_count / summaryData.total_vehicles) * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" style="margin-top: 20px;">
            <!-- 品牌统计 (前10名) -->
            <el-col :span="12">
              <div class="stat-section">
                <h4>🏢 品牌统计 (前10名)</h4>
                <el-table :data="summaryData.brand_stats.slice(0, 10)" size="small" border>
                  <el-table-column prop="brand" label="品牌" />
                  <el-table-column prop="count" label="数量" align="center" />
                  <el-table-column label="比例" align="center">
                    <template slot-scope="{row}">
                      {{ ((row.count / summaryData.total_vehicles) * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
            
            <!-- 颜色统计 (前10名) -->
            <el-col :span="12">
              <div class="stat-section">
                <h4>🎨 颜色统计 (前10名)</h4>
                <el-table :data="summaryData.color_stats.slice(0, 10)" size="small" border>
                  <el-table-column prop="color" label="颜色" />
                  <el-table-column prop="count" label="数量" align="center" />
                  <el-table-column label="比例" align="center">
                    <template slot-scope="{row}">
                      {{ ((row.count / summaryData.total_vehicles) * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" style="margin-top: 20px;">
            <!-- 年份统计 (前10名) -->
            <el-col :span="12">
              <div class="stat-section">
                <h4>📅 年份统计 (前10名)</h4>
                <el-table :data="summaryData.year_stats.slice(0, 10)" size="small" border>
                  <el-table-column prop="year" label="年份" />
                  <el-table-column prop="count" label="数量" align="center" />
                  <el-table-column label="比例" align="center">
                    <template slot-scope="{row}">
                      {{ ((row.count / summaryData.total_vehicles) * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
            
            <!-- 型号统计 (前10名) -->
            <el-col :span="12">
              <div class="stat-section">
                <h4>🚙 型号统计 (前10名)</h4>
                <el-table :data="summaryData.model_stats.slice(0, 10)" size="small" border>
                  <el-table-column prop="model" label="型号" />
                  <el-table-column prop="count" label="数量" align="center" />
                  <el-table-column label="比例" align="center">
                    <template slot-scope="{row}">
                      {{ ((row.count / summaryData.total_vehicles) * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <div slot="footer" class="dialog-footer">
          <el-button @click="summaryDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { fetchVehicleList, createVehicle, updateVehicle, searchVehicles, getVehicleStatusOptions, getVehiclesByStatus, getVehicleTypes, getVehicleInserts, updateVehicleStatusFromInsert, getTodayInserts, getVehicleLatestInsert, getVehicleCountSummary, getCrossStats, getDoubleFilterStats, getInserts } from '@/api/vehicle'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import VehicleStatusManager from '@/components/VehicleStatusManager'

export default {
  name: 'VehicleManagement',
  components: { Pagination, VehicleStatusManager },
  directives: { waves },

  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 20,
        q: undefined,
        status: undefined,
        type_id: undefined,
        // 添加排序参数
        order_by: 'auto_id',
        order_direction: 'desc'
      },
      statusOptions: [],
      vehicleTypes: [],
      // 按钮统计数据
      statusCounts: {}, // 存储各状态的车辆数量
      typeCounts: {}, // 存储各类型的车辆数量
      totalCount: 0, // 总车辆数量
      // 当前筛选状态
      currentFilters: {
        selectedType: null,
        selectedStatus: null
      },
      // 交叉筛选数据
      crossStatsData: {},

      temp: {
        auto_id: undefined,
        license_plate: '',
        model: '',
        brand: '',
        color: '',
        year: new Date().getFullYear().toString(),
        type_id: undefined,
        owner_id: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('vehicle.dialog.editVehicle'),
        create: this.$t('vehicle.dialog.addVehicle')
      },
      rules: {
        license_plate: [{ required: true, message: this.$t('vehicle.form.licensePlateRequired'), trigger: 'blur' }],
        model: [{ required: true, message: this.$t('vehicle.form.modelRequired'), trigger: 'blur' }],
        brand: [{ required: true, message: this.$t('vehicle.form.brandRequired'), trigger: 'blur' }]
      },
      insertsDialogVisible: false,
      insertsLoading: false,
      insertsList: [],
      insertsTotal: 0,
      insertsQuery: {
        page: 1,
        size: 20
      },
      currentVehicle: {},
      // 今日记录相关数据
      todayInsertsDialogVisible: false,
      todayInsertsLoading: false,
      todayInsertsList: [],
      todayInsertsTotal: 0,
      todayInsertsQuery: {
        page: 1,
        size: 20
      },
      todaySelectedInserts: [],
      updateGuideVisible: false, // 新增的更新指南对话框可见性
      autoUpdating: false, // 自动更新状态标记
      autoUpdateProgress: 0, // 自动更新进度
      autoUpdateTotal: 0, // 自动更新总数
      // 统计汇总相关数据
      summaryDialogVisible: false,
      summaryLoading: false,
      summaryData: {
        brand_stats: [],
        color_stats: [],
        model_stats: [],
        status_stats: [],
        year_stats: [],
        type_stats: [],
        summary: [],
        total_vehicles: 0
      }
    }
  },
  created() {
    this.getStatusOptions()
    this.getVehicleTypes()
    this.getSummaryData()
    this.updateButtonCounts() // 获取按钮统计数据
    this.getList().then(() => {
      // 页面加载完成后自动更新所有车辆状态
      this.autoUpdateAllVehicleStatus()
    })
  },
  methods: {
    async getStatusOptions() {
      try {
        const response = await getVehicleStatusOptions()
        this.statusOptions = response.data
      } catch (error) {
        console.error(this.$t('vehicle.message.getStatusOptionsError'), error)
      }
    },
    async getVehicleTypes() {
      try {
        const response = await getVehicleTypes()
        this.vehicleTypes = response.data
        console.log('✅ 获取车辆类型成功:', this.vehicleTypes)
      } catch (error) {
        console.error('❌ 获取车辆类型失败', error)
        this.$message.error('获取车辆类型失败: ' + error.message)
      }
    },
    async getList() {
      this.listLoading = true
      console.log('getList - 当前查询参数:', this.listQuery)
      
      // 清理空的参数
      const cleanQuery = this.cleanQueryParams(this.listQuery)
      console.log('getList - 清理后的参数:', cleanQuery)
      
      // 统一使用 fetchVehicleList 处理所有筛选条件
      // 因为它支持所有参数：page, size, q, status, type_id 等
      let api = fetchVehicleList
      
      console.log('getList - 使用的API:', api.name)
      console.log('getList - 传递的参数:', cleanQuery)
      
      try {
        const response = await api(cleanQuery)
        console.log(response.data)
        if (response.data.items) {
          this.list = response.data.items
          this.total = response.data.total
        } else {
          this.list = response.data
          this.total = response.total
        }
        this.listLoading = false
        
        // 获取列表后刷新按钮统计数据
        this.updateButtonCounts()
      } catch (error) {
        console.error(this.$t('vehicle.message.getListError'), error)
        this.$message.error(this.$t('vehicle.message.getListError'))
        this.list = []
        this.total = 0
        this.listLoading = false
      }
    },
    debugQuery() {
      console.log('=== 调试查询参数 ===')
      console.log('原始 listQuery:', this.listQuery)
      console.log('vehicleTypes:', this.vehicleTypes)
      console.log('statusOptions:', this.statusOptions)
      
      const cleanQuery = this.cleanQueryParams(this.listQuery)
      console.log('清理后的查询参数:', cleanQuery)
      
      // 显示车辆类型详情
      const typeDetails = this.vehicleTypes.map(t => `${t.type_id}: ${t.type_name}`).join('\n')
      
      // 显示在页面上
      this.$alert(`
        原始参数: ${JSON.stringify(this.listQuery, null, 2)}
        
        清理后参数: ${JSON.stringify(cleanQuery, null, 2)}
        
        排序参数: 
        - 字段: ${this.listQuery.order_by} (${this.getSortDisplayName(this.listQuery.order_by)})
        - 方向: ${this.listQuery.order_direction} (${this.listQuery.order_direction === 'asc' ? '升序' : '降序'})
        
        车辆类型数量: ${this.vehicleTypes.length}
        
        车辆类型详情:
        ${typeDetails}
      `, '调试信息', {
        confirmButtonText: '确定'
      })
    },
    handleTypeChange(typeId) {
      this.listQuery.type_id = typeId === '' ? null : typeId
      
      // 更新当前筛选状态
      this.currentFilters.selectedType = typeId === '' ? null : typeId
      
      // 更新按钮数量
      this.updateButtonCounts()
      
      console.log('车辆类型变更:', typeId)
      console.log('更新后的listQuery:', this.listQuery)
      // 自动触发搜索
      this.handleFilter()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleViewDetail(row) {
      this.$router.push(`/vehicle-management/detail/${row.che_liang_id}`)
    },

    resetTemp() {
      this.temp = {
        auto_id: undefined,
        license_plate: '',
        model: '',
        brand: '',
        color: '',
        year: new Date().getFullYear().toString(),
        type_id: undefined,
        owner_id: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createVehicle(this.temp).then(() => {
            this.list.unshift(this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('vehicle.message.success'),
              message: this.$t('vehicle.message.createSuccess'),
              type: 'success',
              duration: 2000
            })
            // 创建车辆后刷新按钮统计数据
            this.updateButtonCounts()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateVehicle(tempData).then(() => {
            const index = this.list.findIndex(v => v.che_liang_id === this.temp.che_liang_id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('vehicle.message.success'),
              message: this.$t('vehicle.message.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            // 更新车辆后刷新按钮统计数据
            this.updateButtonCounts()
          })
        }
      })
    },
    handleStatusChange(status) {
      this.listQuery.status = status
      
      // 更新当前筛选状态
      this.currentFilters.selectedStatus = status === '' ? null : status
      
      // 更新按钮数量
      this.updateButtonCounts()
      
      this.handleFilter()
    },
    handleStatusUpdated(data) {
      // 更新本地列表中的状态
      const vehicleIndex = this.list.findIndex(v => v.che_liang_id === data.vehicleId)
      if (vehicleIndex !== -1) {
        this.list[vehicleIndex].status = data.newStatus
        this.list[vehicleIndex].status_remark = data.remark
      }
    },
    cleanQueryParams(query) {
      // 清理空的参数，只保留有效值
      const cleanQuery = {}
      console.log('cleanQueryParams - 原始参数:', query)
      
      Object.keys(query).forEach(key => {
        const value = query[key]
        console.log(`  参数 ${key}:`, value, typeof value)
        if (value !== undefined && value !== null && value !== '') {
          cleanQuery[key] = value
          console.log(`    ✓ 保留参数 ${key}:`, value)
        } else {
          console.log(`    ✗ 过滤参数 ${key}:`, value)
        }
      })
      
      console.log('cleanQueryParams - 清理后参数:', cleanQuery)
      return cleanQuery
    },
    // 处理表格排序变化
    handleSortChange(sortInfo) {
      console.log('排序变化:', sortInfo)
      
      if (sortInfo.prop) {
        // 根据字段名映射到API对应的字段
        const fieldMapping = {
          'auto_id': 'auto_id',
          'license_plate': 'license_plate',
          'model': 'model',
          'brand': 'brand',
          'color': 'color',
          'year': 'year',
          'status_updated_at': 'status_updated_at'
        }
        
        this.listQuery.order_by = fieldMapping[sortInfo.prop] || sortInfo.prop
        this.listQuery.order_direction = sortInfo.order === 'ascending' ? 'asc' : 'desc'
      } else {
        // 清除排序
        this.listQuery.order_by = 'auto_id'
        this.listQuery.order_direction = 'desc'
      }
      
      console.log('更新后的排序参数:', {
        order_by: this.listQuery.order_by,
        order_direction: this.listQuery.order_direction
      })
      
      // 重新获取数据
      this.handleFilter()
    },
    getStatusName(status) {
      // 优先使用国际化翻译
      const i18nKey = `vehicle.status.${status}`
      if (this.$te(i18nKey)) {
        return this.$t(i18nKey)
      }
      // 如果没有国际化翻译，使用API返回的状态名
      const option = this.statusOptions.find(o => o.status === status)
      return option ? option.status_name : status
    },
    goToStatusPage() {
      this.$router.push('/vehicle-management/status')
    },
    handleViewInserts(row) {
      this.currentVehicle = row
      this.insertsDialogVisible = true
      this.refreshInserts()
    },
         async refreshInserts() {
       this.insertsLoading = true
       try {
         const response = await getVehicleInserts(this.currentVehicle.che_liang_id, {
           page: this.insertsQuery.page,
           size: this.insertsQuery.size
         })

                   // 处理API响应格式
          if (response.success && response.data) {
            this.insertsList = response.data
            this.insertsTotal = response.total || 0
          } else {
            this.insertsList = []
            this.insertsTotal = 0
          }
       } catch (error) {
         console.error('❌ 获取每日输入记录失败', error)
         this.$message.error('获取每日输入记录失败: ' + error.message)
         this.insertsList = []
         this.insertsTotal = 0
       } finally {
         this.insertsLoading = false
       }
     },
    handleInsertsPageChange(val) {
      this.insertsQuery.page = val
      this.refreshInserts()
    },
         handleInsertsPageSizeChange(val) {
       this.insertsQuery.size = val
       this.refreshInserts()
     },
     formatDateTime(dateTimeString) {
       if (!dateTimeString) return '-'
       try {
         const date = new Date(dateTimeString)
         const year = date.getFullYear()
         const month = String(date.getMonth() + 1).padStart(2, '0')
         const day = String(date.getDate()).padStart(2, '0')
         const hours = String(date.getHours()).padStart(2, '0')
         const minutes = String(date.getMinutes()).padStart(2, '0')
         const seconds = String(date.getSeconds()).padStart(2, '0')
         return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
       } catch (error) {
         return dateTimeString
       }
     },
     // 状态映射工具函数
     mapInsertStatusToApiStatus(insertStatus) {
       const statusMapping = {
         'pause': 'parked',  // pause 状态转换为 parked
         'repair': 'maintenance',  // repair 状态转换为 maintenance
         'active': 'active',
         'parked': 'parked',
         'maintenance': 'maintenance'
       }
       return statusMapping[insertStatus] || insertStatus
     },
     // 获取状态显示名称
     getStatusDisplayName(status) {
       const statusDisplayMap = {
         'parked': '停车',
         'active': '活跃',
         'maintenance': '维护',
         'damaged': '损坏'
       }
       return statusDisplayMap[status] || status
     },
     // 获取状态标签类型
     getStatusTagType(status) {
       const statusTagMap = {
         'active': 'success',
         'parked': 'info',
         'maintenance': 'warning',
         'damaged': 'danger'
       }
       return statusTagMap[status] || 'info'
     },
     // 检查状态是否已经更新过
     isStatusAlreadyUpdated(insertRow) {
       if (!this.currentVehicle.status_updated_at) return false
       
       // 将时间字符串转换为Date对象进行比较
       const insertTime = new Date(insertRow.datetime)
       const statusUpdatedTime = new Date(this.currentVehicle.status_updated_at)
       
       // 如果状态更新时间在insert时间之后，说明已经更新过了
       return statusUpdatedTime >= insertTime
     },
     // 从insert记录更新车辆状态
     async updateStatusFromInsert(insertRow) {
       // 设置该行的loading状态
       this.$set(insertRow, 'updating', true)
       
       try {
         const response = await updateVehicleStatusFromInsert(this.currentVehicle.che_liang_id, insertRow)
         
                    if (response.success) {
             // 状态映射：与API保持一致
             const finalStatus = this.mapInsertStatusToApiStatus(insertRow.status)
             
             // 更新当前车辆信息
             if (response.data && response.data.status_updated_at) {
               this.currentVehicle.status = response.data.status
               this.currentVehicle.status_updated_at = response.data.status_updated_at
             } else {
               this.currentVehicle.status = finalStatus
               this.currentVehicle.status_updated_at = new Date().toISOString()
             }
             
             // 更新主列表中的车辆状态
             const vehicleIndex = this.list.findIndex(v => v.che_liang_id === this.currentVehicle.che_liang_id)
             if (vehicleIndex !== -1) {
               this.list[vehicleIndex].status = this.currentVehicle.status
               this.list[vehicleIndex].status_updated_at = this.currentVehicle.status_updated_at
             }
             
             // 显示友好的状态信息
             if (insertRow.status === 'pause') {
               this.$message.success(`状态已更新：${insertRow.status} → parked`)
             } else if (insertRow.status === 'repair') {
               this.$message.success(`状态已更新：${insertRow.status} → maintenance`)
             } else {
               this.$message.success('状态更新成功')
             }
           } else {
             this.$message.error('状态更新失败: ' + response.message)
           }
       } catch (error) {
         console.error('❌ 状态更新失败', error)
         this.$message.error('状态更新失败: ' + error.message)
                } finally {
           this.$set(insertRow, 'updating', false)
         }
       },
               // 今日记录相关方法
        async showTodayInserts() {
          // 确保车辆列表已加载，用于状态比较
          if (this.list.length === 0) {
            await this.getList()
          }
          
          this.todayInsertsDialogVisible = true
          this.refreshTodayInserts()
        },
       async refreshTodayInserts() {
         this.todayInsertsLoading = true
         try {
           const response = await getTodayInserts({
             page: this.todayInsertsQuery.page,
             size: this.todayInsertsQuery.size
           })
           
           if (response.success && response.data) {
             this.todayInsertsList = response.data
             this.todayInsertsTotal = response.total || 0
           } else {
             this.todayInsertsList = []
             this.todayInsertsTotal = 0
           }
         } catch (error) {
           console.error('❌ 获取今日记录失败', error)
           this.$message.error('获取今日记录失败: ' + error.message)
           this.todayInsertsList = []
           this.todayInsertsTotal = 0
         } finally {
           this.todayInsertsLoading = false
         }
       },
               // 检查今日记录的状态是否已更新
        isTodayStatusUpdated(insertRow) {
          // 如果有本地标识，直接返回
          if (insertRow.status_synced) {
            return true
          }
          
          // 查找对应的车辆
          const vehicle = this.list.find(v => v.che_liang_id === insertRow.jia_yi_id_vehicle)
          if (!vehicle || !vehicle.status_updated_at) {
            return false
          }
          
          // 比较insert时间和车辆状态更新时间
          try {
            const insertTime = new Date(insertRow.datetime)
            const statusUpdatedTime = new Date(vehicle.status_updated_at)
            
            // 如果状态更新时间在insert时间之后，且状态相同，说明可能已经更新过了
            return statusUpdatedTime >= insertTime && vehicle.status === insertRow.status
          } catch (error) {
            console.error('时间比较失败:', error)
            return false
          }
        },
       // 从今日记录更新车辆状态
       async updateTodayStatusFromInsert(insertRow) {
         this.$set(insertRow, 'updating', true)
         
         try {
           const response = await updateVehicleStatusFromInsert(insertRow.jia_yi_id_vehicle, insertRow)
           
           if (response.success) {
             // 状态映射：与API保持一致
             const finalStatus = this.mapInsertStatusToApiStatus(insertRow.status)
             
             // 标记为已更新
             this.$set(insertRow, 'status_synced', true)
             
             // 更新主列表中的车辆状态
             const vehicleIndex = this.list.findIndex(v => v.che_liang_id === insertRow.jia_yi_id_vehicle)
             if (vehicleIndex !== -1) {
               this.list[vehicleIndex].status = finalStatus
               this.list[vehicleIndex].status_updated_at = new Date().toISOString()
             }
             
             // 显示友好的状态信息
             if (insertRow.status === 'pause') {
               this.$message.success(`状态已更新：${insertRow.status} → parked`)
             } else if (insertRow.status === 'repair') {
               this.$message.success(`状态已更新：${insertRow.status} → maintenance`)
             } else {
               this.$message.success('状态更新成功')
             }
           } else {
             this.$message.error('状态更新失败: ' + response.message)
           }
         } catch (error) {
           console.error('❌ 状态更新失败', error)
           this.$message.error('状态更新失败: ' + error.message)
         } finally {
           this.$set(insertRow, 'updating', false)
         }
       },
       // 处理今日记录选择变化
       handleTodaySelectionChange(selection) {
         this.todaySelectedInserts = selection
       },
       // 批量更新今日记录状态
       async batchUpdateTodayStatus() {
         if (this.todaySelectedInserts.length === 0) {
           this.$message.warning('请选择要更新的记录')
           return
         }
         
         this.$confirm(`确定要批量更新 ${this.todaySelectedInserts.length} 条记录的状态吗？`, '确认操作', {
           confirmButtonText: '确定',
           cancelButtonText: '取消',
           type: 'warning'
         }).then(async () => {
           const updatePromises = this.todaySelectedInserts.map(insertRow => {
             return this.updateTodayStatusFromInsert(insertRow)
           })
           
                       try {
              await Promise.all(updatePromises)
              this.$message.success('批量更新完成')
              // 清空选择
              this.todaySelectedInserts = []
              // 刷新主列表以显示更新后的状态
              this.getList()
            } catch (error) {
              console.error('❌ 批量更新失败', error)
              this.$message.error('批量更新失败')
            }
         }).catch(() => {
           this.$message.info('已取消批量更新')
         })
       },
       // 处理今日记录分页变化
       handleTodayPageChange(val) {
         this.todayInsertsQuery.page = val
         this.refreshTodayInserts()
       },
               // 处理今日记录分页大小变化
        handleTodayPageSizeChange(val) {
          this.todayInsertsQuery.size = val
          this.refreshTodayInserts()
        },
        // 关闭今日记录对话框
        closeTodayInsertsDialog() {
          this.todayInsertsDialogVisible = false
          // 清空选择
          this.todaySelectedInserts = []
          // 重置分页
          this.todayInsertsQuery.page = 1
            },
    // 显示更新指南
    showUpdateGuide() {
      this.updateGuideVisible = true
    },
    // 获取统计汇总数据
    async getSummaryData() {
      try {
        const response = await getVehicleCountSummary()
        if (response.success) {
          this.summaryData = response.data
          console.log('✅ 获取车辆统计汇总成功:', this.summaryData)
        } else {
          console.error('❌ 获取车辆统计汇总失败:', response.message)
        }
      } catch (error) {
        console.error('❌ 获取车辆统计汇总失败:', error)
      }
    },
    // 显示统计汇总对话框
    showSummary() {
      this.summaryDialogVisible = true
      this.refreshSummary()
    },
    // 刷新统计数据
    async refreshSummary() {
      this.summaryLoading = true
      try {
        const response = await getVehicleCountSummary()
        if (response.success) {
          this.summaryData = response.data
        } else {
          this.$message.error('获取统计数据失败: ' + response.message)
        }
      } catch (error) {
        console.error('❌ 刷新统计数据失败:', error)
        this.$message.error('获取统计数据失败: ' + error.message)
      } finally {
        this.summaryLoading = false
      }
    },
         // 自动更新所有车辆状态（检查所有记录）
         async autoUpdateAllVehicleStatus() {
           if (this.list.length === 0) {
             console.log('没有车辆需要更新状态')
             return
           }
           
           this.autoUpdating = true
           this.autoUpdateProgress = 0
           this.autoUpdateTotal = this.list.length
           
           console.log(`🚀 开始自动更新 ${this.autoUpdateTotal} 辆车的状态...（检查所有记录）`)
           
           try {
             let successCount = 0
             let skipCount = 0
             let errorCount = 0
             let noRecordCount = 0
             
             // 为每个车辆更新状态
             for (let i = 0; i < this.list.length; i++) {
               const vehicle = this.list[i]
               this.autoUpdateProgress = i + 1
               
               try {
                 // 获取该车辆的最新记录（所有时间范围）
                 const vehicleResponse = await getVehicleLatestInsert(vehicle.che_liang_id)
                 
                 if (!vehicleResponse.success || !vehicleResponse.data || vehicleResponse.data.length === 0) {
                   console.log(`车辆 ${vehicle.license_plate} 暂无进入记录`)
                   noRecordCount++
                   continue
                 }
                 
                 // 获取最新的记录
                 const latestInsert = vehicleResponse.data[0]
                 
                 // 状态映射
                 const expectedStatus = this.mapInsertStatusToApiStatus(latestInsert.status)
                 
                 // 检查是否需要更新（强制更新，不跳过）
                 const needsUpdate = vehicle.status !== expectedStatus || !vehicle.status_updated_at
                 
                 if (!needsUpdate) {
                   // 即使状态相同也检查时间，确保状态更新时间在insert时间之后
                   if (vehicle.status_updated_at) {
                     const insertTime = new Date(latestInsert.datetime)
                     const statusUpdatedTime = new Date(vehicle.status_updated_at)
                     
                     if (statusUpdatedTime >= insertTime) {
                       console.log(`车辆 ${vehicle.license_plate} 状态已是最新`)
                       skipCount++
                       continue
                     }
                   }
                 }
                 
                 // 更新车辆状态
                 const updateResponse = await updateVehicleStatusFromInsert(vehicle.che_liang_id, latestInsert)
                 
                 if (updateResponse.success) {
                   const finalStatus = this.mapInsertStatusToApiStatus(latestInsert.status)
                   vehicle.status = finalStatus
                   vehicle.status_updated_at = new Date().toISOString()
                   successCount++
                   console.log(`✅ 车辆 ${vehicle.license_plate} 状态已更新为: ${finalStatus} (基于记录: ${latestInsert.datetime})`)
                 } else {
                   errorCount++
                   console.error(`❌ 车辆 ${vehicle.license_plate} 状态更新失败:`, updateResponse.message)
                 }
                 
               } catch (error) {
                 errorCount++
                 console.error(`❌ 车辆 ${vehicle.license_plate || vehicle.che_liang_id} 更新失败:`, error)
               }
               
               // 添加小延迟避免API请求过快
               await new Promise(resolve => setTimeout(resolve, 100))
             }
             
             // 显示结果
             const message = `自动更新完成: 成功 ${successCount} 辆，跳过 ${skipCount} 辆，无记录 ${noRecordCount} 辆，失败 ${errorCount} 辆`
             console.log(`🎉 ${message}`)
             
             if (successCount > 0) {
               this.$message.success(message)
             } else if (errorCount === 0 && noRecordCount === 0) {
               this.$message.info(message)
             } else {
               this.$message.warning(message)
             }
             
           } catch (error) {
             console.error('❌ 自动更新失败:', error)
             this.$message.error('自动更新失败: ' + error.message)
           } finally {
             this.autoUpdating = false
             this.autoUpdateProgress = 0
             this.autoUpdateTotal = 0
             // 自动更新完成后刷新按钮统计数据
             this.updateButtonCounts()
           }
         },
         // 处理车辆状态更新（检查所有记录）
         async handleUpdateVehicleStatus(vehicle) {
           // 设置loading状态
           this.$set(vehicle, 'statusUpdating', true)
           
           try {
             // 获取该车辆的最新记录（所有时间范围）
             const vehicleResponse = await getVehicleLatestInsert(vehicle.che_liang_id)
             
             if (!vehicleResponse.success || !vehicleResponse.data || vehicleResponse.data.length === 0) {
               this.$message.warning('该车辆暂无进入记录')
               return
             }
             
             // 获取最新的记录
             const latestInsert = vehicleResponse.data[0]
             
             // 状态映射：将insert状态转换为API状态进行比较
             const expectedStatus = this.mapInsertStatusToApiStatus(latestInsert.status)
             
             // 检查是否已经是最新状态
             if (vehicle.status === expectedStatus && vehicle.status_updated_at) {
               const insertTime = new Date(latestInsert.datetime)
               const statusUpdatedTime = new Date(vehicle.status_updated_at)
               
               if (statusUpdatedTime >= insertTime) {
                 this.$message.info(`车辆状态已根据最新记录更新，无需重复更新 (最新记录: ${latestInsert.datetime})`)
                 return
               }
             }
             
             // 更新车辆状态
             const updateResponse = await updateVehicleStatusFromInsert(vehicle.che_liang_id, latestInsert)
             
             if (updateResponse.success) {
               // 状态映射：与API保持一致
               const finalStatus = this.mapInsertStatusToApiStatus(latestInsert.status)
               
               // 更新本地数据
               vehicle.status = finalStatus
               vehicle.status_updated_at = new Date().toISOString()
               
               // 显示友好的状态信息
               const displayStatus = this.getStatusDisplayName(finalStatus)
               const originalStatus = latestInsert.status
               
               if (originalStatus === 'pause') {
                 this.$message.success(`状态已更新：${originalStatus} → ${displayStatus} (${finalStatus})，基于记录: ${latestInsert.datetime}`)
               } else if (originalStatus === 'repair') {
                 this.$message.success(`状态已更新：${originalStatus} → ${displayStatus} (${finalStatus})，基于记录: ${latestInsert.datetime}`)
               } else {
                 this.$message.success(`状态已根据最新记录更新为: ${displayStatus} (${finalStatus})，基于记录: ${latestInsert.datetime}`)
               }
               
               // 可选：刷新整个列表以确保数据一致性
               // this.getList()
             } else {
               this.$message.error('状态更新失败: ' + updateResponse.message)
             }
             
           } catch (error) {
             console.error('❌ 更新车辆状态失败', error)
             this.$message.error('更新失败: ' + error.message)
           } finally {
             this.$set(vehicle, 'statusUpdating', false)
           }
         },
         // 获取按钮统计数据 - 支持动态筛选
         async updateButtonCounts() {
           try {
             let data;
             
             // 根据当前筛选条件获取不同的数据
             if (this.currentFilters.selectedType && this.currentFilters.selectedStatus) {
               // 双重筛选
               data = await this.getDoubleFilterStatsData(this.currentFilters.selectedType, this.currentFilters.selectedStatus);
               if (data) {
                 this.updateDoubleFilterDisplay(data);
               } else {
                 // Fallback: 使用全部统计数据
                 data = await this.getFullSummaryData();
                 this.updateAllButtons(data);
               }
             } else if (this.currentFilters.selectedType) {
               // 按类型筛选 - 更新状态按钮
               data = await this.getCrossStatsData('type', this.currentFilters.selectedType);
               if (data) {
                 this.updateStatusButtonsFromTypeFilter(data);
               } else {
                 // Fallback: 使用全部统计数据
                 data = await this.getFullSummaryData();
                 this.updateAllButtons(data);
               }
             } else if (this.currentFilters.selectedStatus) {
               // 按状态筛选 - 更新类型按钮
               data = await this.getCrossStatsData('status', this.currentFilters.selectedStatus);
               if (data) {
                 this.updateTypeButtonsFromStatusFilter(data);
               } else {
                 // Fallback: 使用全部统计数据
                 data = await this.getFullSummaryData();
                 this.updateAllButtons(data);
               }
             } else {
               // 无筛选 - 显示全部统计
               data = await this.getFullSummaryData();
               this.updateAllButtons(data);
             }
           } catch (error) {
             console.error('❌ 更新按钮数量失败:', error);
             // Fallback: 尝试获取基础统计数据
             try {
               const data = await this.getFullSummaryData();
               this.updateAllButtons(data);
             } catch (fallbackError) {
               console.error('❌ Fallback获取统计数据也失败:', fallbackError);
             }
           }
         },
         
         // 获取交叉统计数据
         async getCrossStatsData(filterType, filterValue) {
           try {
             const response = await getCrossStats(filterType, filterValue);
             return response.success ? response.data : null;
           } catch (error) {
             // 如果是404错误，说明API端点不存在，使用基础统计
             if (error.response && error.response.status === 404) {
               console.warn('⚠️ 交叉统计API端点不存在，使用基础统计数据');
               return null; // 触发fallback
             }
             console.error('❌ 获取交叉统计数据失败:', error);
             return null;
           }
         },
         
         // 获取双重筛选统计
         async getDoubleFilterStatsData(typeId, status) {
           try {
             const response = await getDoubleFilterStats(typeId, status);
             return response.success ? response.data : null;
           } catch (error) {
             // 如果是404错误，说明API端点不存在，使用基础统计
             if (error.response && error.response.status === 404) {
               console.warn('⚠️ 双重筛选API端点不存在，使用基础统计数据');
               return null; // 触发fallback
             }
             console.error('❌ 获取双重筛选统计失败:', error);
             return null;
           }
         },
         
         // 获取完整统计
         async getFullSummaryData() {
           try {
             const response = await getVehicleCountSummary();
             return response.success ? response.data : null;
           } catch (error) {
             console.error('❌ 获取完整统计失败:', error);
             return null;
           }
         },
         
         // 当选择类型时更新状态按钮
         updateStatusButtonsFromTypeFilter(data) {
           if (!data || !data.stats) return;
           
           // 重置所有状态计数
           this.resetStatusCounts();
           
           // 更新有数据的状态计数
           data.stats.forEach(stat => {
             this.statusCounts[stat.status] = stat.count;
           });
           
           console.log('✅ 按类型筛选更新状态按钮:', this.statusCounts);
         },
         
         // 当选择状态时更新类型按钮
         updateTypeButtonsFromStatusFilter(data) {
           if (!data || !data.stats) return;
           
           // 重置所有类型计数
           this.resetTypeCounts();
           
           // 更新有数据的类型计数
           data.stats.forEach(stat => {
             this.typeCounts[stat.type_id] = stat.count;
           });
           
           console.log('✅ 按状态筛选更新类型按钮:', this.typeCounts);
         },
         
         // 双重筛选显示更新
         updateDoubleFilterDisplay(data) {
           if (!data) return;
           
           // 重置所有计数
           this.resetAllCounts();
           
           // 只显示符合双重筛选条件的数据
           if (data.count) {
             this.statusCounts[this.currentFilters.selectedStatus] = data.count;
             this.typeCounts[this.currentFilters.selectedType] = data.count;
             this.totalCount = data.count;
           }
           
           console.log('✅ 双重筛选更新显示:', { status: this.statusCounts, type: this.typeCounts, total: this.totalCount });
         },
         
         // 更新所有按钮（无筛选状态）
         updateAllButtons(data) {
           if (!data) return;
           
           // 更新总数
           this.totalCount = data.total_vehicles || 0;
           
           // 更新状态统计
           this.statusCounts = {};
           if (data.status_stats) {
             data.status_stats.forEach(status => {
               this.statusCounts[status.status] = status.count;
             });
           }
           
           // 更新类型统计
           this.typeCounts = {};
           if (data.type_stats) {
             data.type_stats.forEach(type => {
               this.typeCounts[type.type_id] = type.vehicle_count;
             });
           }
           
           console.log('✅ 更新所有按钮统计:', {
             total: this.totalCount,
             statusCounts: this.statusCounts,
             typeCounts: this.typeCounts
           });
         },
         
         // 重置状态计数
         resetStatusCounts() {
           this.statusCounts = {};
           this.statusOptions.forEach(option => {
             this.statusCounts[option.status] = 0;
           });
         },
         
         // 重置类型计数
         resetTypeCounts() {
           this.typeCounts = {};
           this.vehicleTypes.forEach(type => {
             this.typeCounts[type.type_id] = 0;
           });
         },
         
         // 重置所有计数
         resetAllCounts() {
           this.resetStatusCounts();
           this.resetTypeCounts();
           this.totalCount = 0;
         },
         
         // 重置所有筛选
         resetAllFilters() {
           this.currentFilters.selectedType = null;
           this.currentFilters.selectedStatus = null;
           this.listQuery.status = undefined;
           this.listQuery.type_id = undefined;
           
           // 重置排序
           this.listQuery.order_by = 'auto_id';
           this.listQuery.order_direction = 'desc';
           
           // 更新按钮数量
           this.updateButtonCounts();
           
           // 更新车辆列表
           this.handleFilter();
           
           console.log('✅ 重置所有筛选和排序');
         },
         
         // 获取状态按钮文本的映射
         getStatusDisplayText(status) {
           const statusMap = {
             'active': '活跃',
             'parked': '停车',
             'maintenance': '维护',
             'damaged': '损坏',
             'inactive': '不活跃'
           };
           return statusMap[status] || this.getStatusName(status);
         },
         
         // 获取类型名称
         getTypeName(typeId) {
           const type = this.vehicleTypes.find(t => t.type_id === typeId);
           return type ? type.type_name : `类型${typeId}`;
         },
         
         // 获取排序字段的显示名称
         getSortDisplayName(fieldName) {
           const displayMapping = {
             'auto_id': 'ID',
             'license_plate': '车牌号',
             'model': '型号',
             'brand': '品牌',
             'color': '颜色',
             'year': '年份',
             'status_updated_at': '状态更新时间'
           };
           return displayMapping[fieldName] || fieldName;
         },
         
         // 切换排序方向
         toggleSortDirection() {
           this.listQuery.order_direction = this.listQuery.order_direction === 'asc' ? 'desc' : 'asc';
           console.log('切换排序方向:', this.listQuery.order_direction);
           this.handleFilter();
         }
     }
}
</script>

<style lang="scss" scoped>
.vehicle-management {
  .filter-container {
    padding: 20px 0;
  }
  
  .filter-status-indicator {
    margin-left: 10px;
    display: inline-block;
    
    .el-tag {
      background: #f0f9ff;
      border-color: #bfdbfe;
      color: #1e40af;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background: #dbeafe;
        border-color: #93c5fd;
      }
    }
  }
  
  .sort-indicator {
    display: flex;
    align-items: center;
    
    .el-tag {
      background: #f0f9ff;
      border-color: #10b981;
      color: #047857;
      
      i {
        color: #047857;
        font-weight: bold;
      }
    }
    
    .el-button--text {
      color: #047857;
      padding: 0 8px;
      font-size: 12px;
      
      &:hover {
        color: #065f46;
        background: #d1fae5;
      }
    }
  }
}

.inserts-dialog-content {
  .inserts-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
    
    .vehicle-info {
      h3 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .status-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #606266;
        
        .status-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .inserts-pagination {
    margin-top: 20px;
    text-align: center;
  }
  
  .location-info {
    .location-text {
      font-size: 12px;
      color: #606266;
      line-height: 1.2;
    }
  }
  
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
  }
  
  .status-updated-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
    gap: 2px;
  }
}

.auto-update-progress {
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 10px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  padding: 8px 16px;
  
  i {
    color: #3b82f6;
    margin-right: 8px;
    font-size: 16px;
  }
  
  span {
    color: #1e40af;
    font-size: 14px;
    font-weight: 500;
    margin-right: 10px;
  }
}

.status-button-group {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
  
  .el-button {
    margin-left: 0;
    margin-right: 0;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &.el-button--primary {
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    
    &.filter-selected {
      background: linear-gradient(45deg, #409EFF, #66b1ff);
      color: white;
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
    }
  }
}

.type-button-group {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
  
  .el-button {
    margin-left: 0;
    margin-right: 0;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &.el-button--primary {
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    
    &.filter-selected {
      background: linear-gradient(45deg, #409EFF, #66b1ff);
      color: white;
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
    }
  }
}

.today-inserts-content {
  .today-inserts-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
    
    .today-info {
      h3 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .today-stats {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #606266;
      }
    }
    
    .today-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .today-pagination {
    margin-top: 20px;
    text-align: center;
  }
  
  .location-info {
    .location-text {
      font-size: 12px;
      color: #606266;
      line-height: 1.2;
    }
  }
  
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
  }
  
  .status-updated-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
    gap: 2px;
  }
}

.update-guide-content {
  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #303133;
    font-size: 18px;
  }

  .guide-section {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ebeef5;

    h4 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #606266;
      font-size: 15px;
    }

    p {
      margin-bottom: 10px;
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
    }

    ul {
      margin-top: 0;
      margin-bottom: 15px;
      padding-left: 20px;
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
    }

    li {
      margin-bottom: 5px;
    }

    .process-steps {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-top: 10px;
      padding-left: 20px;
      color: #606266;
      font-size: 14px;
      line-height: 1.6;

      .step {
        display: flex;
        align-items: center;
        gap: 10px;

        .step-number {
          display: inline-block;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border-radius: 50%;
          background-color: #409EFF;
          color: #fff;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }

    .api-info {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed #ebeef5;
      color: #909399;
      font-size: 13px;
      line-height: 1.6;

      p {
        margin-bottom: 5px;
      }

      strong {
        color: #303133;
      }
    }
  }
}

.summary-content {
  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e4e7ed;
    
    .summary-total {
      h3 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }
      
      .total-info {
        display: flex;
        align-items: baseline;
        gap: 8px;
        
        .total-number {
          font-size: 32px;
          font-weight: bold;
          color: #409EFF;
        }
        
        .total-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
    
    .summary-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .stat-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 8px;
    }
    
    .el-table {
      border-radius: 6px;
      overflow: hidden;
      
      .el-table__header {
        background: #fafafa;
      }
      
      .el-table__body {
        background: #fff;
      }
    }
  }
}
</style> 