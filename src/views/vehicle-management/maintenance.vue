<template>
  <div class="vehicle-maintenance">
    <div class="app-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.q"
          placeholder="搜索车牌号或型号"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.type"
          placeholder="维护类型"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="保养" value="maintenance" />
          <el-option label="维修" value="repair" />
          <el-option label="检查" value="inspection" />
        </el-select>
        <el-select
          v-model="listQuery.type_id"
          :placeholder="$t('vehicle.list.searchByType')"
          clearable
          style="width: 150px"
          class="filter-item"
        >
          <el-option
            v-for="type in vehicleTypes"
            :key="type.type_id"
            :label="type.type_name"
            :value="type.type_id"
          />
        </el-select>
        <el-date-picker
          v-model="listQuery.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="filter-item"
          style="width: 240px"
        />
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加维护记录
        </el-button>
        <el-button
          class="filter-item"
          type="success"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </div>

      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车牌号" width="120px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.licensePlate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车辆型号" width="150px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.vehicleModel }}</span>
          </template>
        </el-table-column>
        <el-table-column label="维护类型" width="100px" align="center">
          <template slot-scope="{row}">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="维护内容" min-width="200px">
          <template slot-scope="{row}">
            <span>{{ row.content }}</span>
          </template>
        </el-table-column>
        <el-table-column label="维护日期" width="120px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.maintenanceDate | parseTime('{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="费用" width="100px" align="center">
          <template slot-scope="{row}">
            <span>￥{{ row.cost }}</span>
          </template>
        </el-table-column>
        <el-table-column label="维护人员" width="120px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.technician }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" class-name="status-col" width="100">
          <template slot-scope="{row}">
            <el-tag :type="row.status | statusFilter">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template slot-scope="{row,$index}">
            <el-button type="primary" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
            <el-button size="mini" type="info" @click="handleView(row)">
              查看
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(row, $index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />

      <!-- 添加/编辑维护记录对话框 -->
      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="temp"
          label-position="left"
          label-width="100px"
          style="width: 500px; margin-left:50px;"
        >
          <el-form-item label="选择车辆" prop="vehicleId">
            <el-select v-model="temp.vehicleId" placeholder="请选择车辆" style="width: 100%">
              <el-option
                v-for="vehicle in vehicleOptions"
                :key="vehicle.id"
                :label="`${vehicle.licensePlate} - ${vehicle.model}`"
                :value="vehicle.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="维护类型" prop="type">
            <el-select v-model="temp.type" placeholder="请选择维护类型" style="width: 100%">
              <el-option label="保养" value="maintenance" />
              <el-option label="维修" value="repair" />
              <el-option label="检查" value="inspection" />
            </el-select>
          </el-form-item>
          <el-form-item label="维护内容" prop="content">
            <el-input
              v-model="temp.content"
              :autosize="{ minRows: 3, maxRows: 6}"
              type="textarea"
              placeholder="请输入维护内容"
            />
          </el-form-item>
          <el-form-item label="维护日期" prop="maintenanceDate">
            <el-date-picker
              v-model="temp.maintenanceDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="费用" prop="cost">
            <el-input-number v-model="temp.cost" :min="0" :precision="2" style="width: 100%" />
          </el-form-item>
          <el-form-item label="维护人员" prop="technician">
            <el-input v-model="temp.technician" placeholder="请输入维护人员姓名" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="temp.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="已完成" value="completed" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="待处理" value="pending" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="temp.remarks"
              :autosize="{ minRows: 2, maxRows: 4}"
              type="textarea"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
            确认
          </el-button>
        </div>
      </el-dialog>

      <!-- 查看维护记录详情对话框 -->
      <el-dialog title="维护记录详情" :visible.sync="viewDialogVisible" width="600px">
        <div class="maintenance-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="车牌号">{{ viewData.licensePlate }}</el-descriptions-item>
            <el-descriptions-item label="车辆型号">{{ viewData.vehicleModel }}</el-descriptions-item>
            <el-descriptions-item label="维护类型">
              <el-tag :type="getTypeTagType(viewData.type)">
                {{ getTypeLabel(viewData.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="维护日期">
              {{ viewData.maintenanceDate | parseTime('{y}-{m}-{d}') }}
            </el-descriptions-item>
            <el-descriptions-item label="费用">￥{{ viewData.cost }}</el-descriptions-item>
            <el-descriptions-item label="维护人员">{{ viewData.technician }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="viewData.status | statusFilter">
                {{ getStatusLabel(viewData.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ viewData.createdAt | parseTime('{y}-{m}-{d} {h}:{i}') }}
            </el-descriptions-item>
            <el-descriptions-item label="维护内容" :span="2">
              <p>{{ viewData.content }}</p>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              <p>{{ viewData.remarks || '无' }}</p>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import { getVehicleTypes } from '@/api/vehicle'

export default {
  name: 'VehicleMaintenance',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        completed: 'success',
        in_progress: 'warning',
        pending: 'info'
      }
      return statusMap[status]
    },
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 20,
        q: undefined,
        type: undefined,
        type_id: undefined,
        dateRange: null
      },
      vehicleTypes: [],
      vehicleOptions: [
        { id: 1, licensePlate: '京A12345', model: '奔驰E300' },
        { id: 2, licensePlate: '京B67890', model: '宝马X5' },
        { id: 3, licensePlate: '京C11111', model: '奥迪A6' }
      ],
      temp: {
        id: undefined,
        vehicleId: undefined,
        type: '',
        content: '',
        maintenanceDate: new Date(),
        cost: 0,
        technician: '',
        status: 'pending',
        remarks: ''
      },
      dialogFormVisible: false,
      viewDialogVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑维护记录',
        create: '添加维护记录'
      },
      viewData: {},
      rules: {
        vehicleId: [{ required: true, message: '请选择车辆', trigger: 'change' }],
        type: [{ required: true, message: '请选择维护类型', trigger: 'change' }],
        content: [{ required: true, message: '请输入维护内容', trigger: 'blur' }],
        maintenanceDate: [{ required: true, message: '请选择维护日期', trigger: 'change' }],
        cost: [{ required: true, message: '请输入费用', trigger: 'blur' }],
        technician: [{ required: true, message: '请输入维护人员', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getVehicleTypes()
    this.getList()
  },
  methods: {
    async getVehicleTypes() {
      try {
        const response = await getVehicleTypes()
        this.vehicleTypes = response.data
      } catch (error) {
        console.error('获取车辆类型失败', error)
      }
    },
    getList() {
      this.listLoading = true
      console.log('maintenance.vue - 当前查询参数:', this.listQuery)
      
      // 清理空的参数（为将来真实API调用做准备）
      const cleanQuery = this.cleanQueryParams(this.listQuery)
      console.log('maintenance.vue - 清理后的参数:', cleanQuery)
      
      // 模拟数据
      setTimeout(() => {
        this.list = [
          {
            id: 1,
            licensePlate: '京A12345',
            vehicleModel: '奔驰E300',
            type: 'maintenance',
            content: '更换机油、机滤，检查轮胎气压',
            maintenanceDate: new Date('2023-12-01'),
            cost: 500,
            technician: '张师傅',
            status: 'completed',
            createdAt: new Date('2023-12-01'),
            remarks: '常规保养'
          },
          {
            id: 2,
            licensePlate: '京B67890',
            vehicleModel: '宝马X5',
            type: 'repair',
            content: '更换刹车片，检查刹车系统',
            maintenanceDate: new Date('2023-11-28'),
            cost: 800,
            technician: '李师傅',
            status: 'completed',
            createdAt: new Date('2023-11-28'),
            remarks: '刹车系统维修'
          },
          {
            id: 3,
            licensePlate: '京C11111',
            vehicleModel: '奥迪A6',
            type: 'inspection',
            content: '年检，全车检查',
            maintenanceDate: new Date('2023-11-25'),
            cost: 200,
            technician: '王师傅',
            status: 'in_progress',
            createdAt: new Date('2023-11-25'),
            remarks: '年度检查'
          }
        ]
        this.total = this.list.length
        this.listLoading = false
      }, 1000)
    },
    cleanQueryParams(query) {
      // 清理空的参数，只保留有效值
      const cleanQuery = {}
      Object.keys(query).forEach(key => {
        const value = query[key]
        if (value !== undefined && value !== null && value !== '') {
          cleanQuery[key] = value
        }
      })
      return cleanQuery
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        vehicleId: undefined,
        type: '',
        content: '',
        maintenanceDate: new Date(),
        cost: 0,
        technician: '',
        status: 'pending',
        remarks: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const vehicle = this.vehicleOptions.find(v => v.id === this.temp.vehicleId)
          const newRecord = {
            ...this.temp,
            id: Date.now(),
            licensePlate: vehicle.licensePlate,
            vehicleModel: vehicle.model,
            createdAt: new Date()
          }
          this.list.unshift(newRecord)
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '创建成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const index = this.list.findIndex(v => v.id === this.temp.id)
          this.list.splice(index, 1, this.temp)
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    handleView(row) {
      this.viewData = Object.assign({}, row)
      this.viewDialogVisible = true
    },
    handleDelete(row, index) {
      this.$confirm('确认删除这条维护记录吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.list.splice(index, 1)
        this.$notify({
          title: '成功',
          message: '删除成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleExport() {
      this.$message.success('导出功能开发中...')
    },
    getTypeLabel(type) {
      const typeMap = {
        maintenance: '保养',
        repair: '维修',
        inspection: '检查'
      }
      return typeMap[type] || type
    },
    getTypeTagType(type) {
      const typeMap = {
        maintenance: 'success',
        repair: 'warning',
        inspection: 'info'
      }
      return typeMap[type] || ''
    },
    getStatusLabel(status) {
      const statusMap = {
        completed: '已完成',
        in_progress: '进行中',
        pending: '待处理'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-maintenance {
  .filter-container {
    padding: 20px 0;
    
    .filter-item {
      margin-right: 10px;
    }
  }
  
  .maintenance-detail {
    p {
      margin: 0;
      line-height: 1.5;
    }
  }
}
</style> 