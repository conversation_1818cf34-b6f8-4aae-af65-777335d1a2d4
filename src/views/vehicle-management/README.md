# 车辆管理系统

基于 Vue.js + Element UI 的车辆管理系统，使用 `shwethe_drive_ops` API 服务。

## 功能特性

### 1. 车辆管理 (`/vehicle-management`)
- **车辆列表展示**：支持分页、搜索、筛选
- **车辆信息管理**：添加、编辑、删除车辆
- **车辆状态管理**：可用/不可用状态切换
- **车辆字段**：
  - 车牌号
  - 车辆型号
  - 品牌
  - 颜色
  - 年份
  - 状态
  - 描述
  - 创建时间

### 2. 车辆详情 (`/vehicle-management/detail/:id`)
- **详细信息展示**：完整的车辆信息展示
- **维护记录管理**：查看和管理车辆维护记录
- **在线编辑**：直接在详情页编辑车辆信息
- **操作历史**：记录车辆的各种操作

### 3. 车辆统计 (`/vehicle-management/statistics`)
- **统计卡片**：总车辆数、可用车辆、不可用车辆、维护中车辆
- **图表展示**：
  - 车辆品牌分布（饼图）
  - 车辆状态分布（饼图）
  - 车辆年份分布（柱状图）
- **最近维护记录**：显示最近的维护活动

### 4. 维护管理 (`/vehicle-management/maintenance`)
- **维护记录管理**：添加、编辑、删除维护记录
- **高级筛选**：按维护类型、日期范围筛选
- **维护类型**：保养、维修、检查
- **详细信息**：维护内容、费用、维护人员、状态
- **导出功能**：支持维护记录导出

## API 接口

所有 API 调用都使用 `shwethe_drive_ops` 服务（`http://************:15679/drive_ops`）

基础路径：`/drive_ops/api/v1`

### 车辆相关接口
```javascript
// 获取车辆列表（支持分页）
GET /vehicles?page=1&size=20

// 获取单个车辆
GET /vehicles/:id

// 创建车辆
POST /vehicles

// 更新车辆
PUT /vehicles/:id

// 删除车辆
DELETE /vehicles/:id

// 搜索车辆
GET /vehicles/search?q=关键词&page=1&size=20

// 根据车主获取车辆
GET /vehicles/owner/:owner_id

// 获取车辆统计
GET /vehicles/stats

// 获取车辆类型列表
GET /vehicle-types

// 创建车辆类型
POST /vehicle-types

// 获取单个车辆类型
GET /vehicle-types/:id

// 更新车辆类型
PUT /vehicle-types/:id

// 删除车辆类型
DELETE /vehicle-types/:id

// 获取车主列表
GET /owners?page=1&size=20

// 获取进入记录
GET /inserts?page=1&size=20

// 获取文档列表
GET /documents?page=1&size=20

// 健康检查
GET /health

// 获取系统信息
GET /info
```

## 文件结构

```
src/views/vehicle-management/
├── index.vue          # 车辆列表页面
├── detail.vue         # 车辆详情页面
├── statistics.vue     # 车辆统计页面
├── maintenance.vue    # 维护管理页面
└── README.md         # 说明文档

src/api/
├── vehicle.js         # 车辆相关API
└── vehicle/
    └── index.js       # 车辆API模块

```

## 使用说明

### 1. 安装依赖
确保项目已安装以下依赖：
- Vue.js 2.x
- Element UI
- ECharts (用于统计图表)
- Axios (HTTP 请求)

### 2. 配置 API 地址
在 `src/utils/request.js` 中已配置：
```javascript
shwethe_drive_ops: 'http://************:15679/drive_ops'
```

### 3. 路由配置

#### 3.1 创建路由模块文件
在 `src/router/modules/vehicleManagement.js` 中创建：

```javascript
import Layout from '@/layout'

const vehicleManagementRouter = {
  path: '/vehicle-management',
  component: Layout,
  redirect: '/vehicle-management/index',
  name: 'VehicleManagement',
  meta: {
    title: '车辆管理',
    icon: 'el-icon-truck'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/vehicle-management/index'),
      name: 'VehicleList',
      meta: { title: '车辆列表', icon: 'el-icon-s-grid' }
    },
    {
      path: 'detail/:id',
      component: () => import('@/views/vehicle-management/detail'),
      name: 'VehicleDetail',
      meta: { title: '车辆详情', noCache: true },
      hidden: true
    },
    {
      path: 'statistics',
      component: () => import('@/views/vehicle-management/statistics'),
      name: 'VehicleStatistics',
      meta: { title: '车辆统计', icon: 'el-icon-data-analysis' }
    },
    {
      path: 'maintenance',
      component: () => import('@/views/vehicle-management/maintenance'),
      name: 'VehicleMaintenance',
      meta: { title: '维护管理', icon: 'el-icon-setting' }
    }
  ]
}

export default vehicleManagementRouter
```

#### 3.2 在主路由文件中引入
在 `src/router/index.js` 中添加：

```javascript
// 导入车辆管理路由模块
import vehicleManagementRouter from './modules/vehicleManagement'

// 在 constantRoutes 数组中添加
const constantRoutes = [
  // ... 其他路由
  vehicleManagementRouter,
  // ... 其他路由
]
```

## 数据格式

### API 响应格式

#### 标准响应
```javascript
{
  success: Boolean,        // 操作是否成功
  message: String,         // 响应消息
  data: Object|Array       // 响应数据
}
```

#### 分页响应
```javascript
{
  success: Boolean,        // 操作是否成功
  message: String,         // 响应消息
  data: Array,            // 数据列表
  page: Number,           // 当前页码
  size: Number,           // 每页大小
  total: Number           // 总记录数
}
```

### 车辆数据结构
```javascript
{
  id: Number,              // 车辆ID
  licensePlate: String,    // 车牌号
  model: String,           // 车辆型号
  brand: String,           // 品牌
  color: String,           // 颜色
  year: Number,            // 年份
  status: String,          // 状态 (active/inactive)
  description: String,     // 描述
  createdAt: Date,         // 创建时间
  updatedAt: Date          // 更新时间
}
```

### 维护记录数据结构
```javascript
{
  id: Number,              // 记录ID
  vehicleId: Number,       // 车辆ID
  licensePlate: String,    // 车牌号
  vehicleModel: String,    // 车辆型号
  type: String,            // 维护类型 (maintenance/repair/inspection)
  content: String,         // 维护内容
  maintenanceDate: Date,   // 维护日期
  cost: Number,            // 费用
  technician: String,      // 维护人员
  status: String,          // 状态 (completed/in_progress/pending)
  remarks: String,         // 备注
  createdAt: Date          // 创建时间
}
```

## 特色功能

1. **响应式设计**：适配不同屏幕尺寸
2. **实时搜索**：支持车牌号、型号等字段搜索
3. **状态管理**：直观的状态标签和颜色区分
4. **数据可视化**：使用 ECharts 展示统计图表
5. **操作权限**：支持编辑、删除等权限控制
6. **数据导出**：支持维护记录等数据导出
7. **表单验证**：完整的表单验证机制

## 扩展说明

系统采用模块化设计，可以根据需要扩展以下功能：
- 车辆分类管理
- 司机管理
- 保险管理
- 油耗统计
- GPS 追踪
- 报表生成

## 技术栈

- **前端框架**：Vue.js 2.x
- **UI 组件库**：Element UI
- **图表库**：ECharts
- **HTTP 客户端**：Axios
- **样式预处理器**：SCSS
- **API 服务**：shwethe_drive_ops

## 注意事项

1. 确保 API 服务 `http://************:15679/drive_ops` 正常运行
2. 所有日期时间格式统一使用 ISO 8601 标准
3. 图片上传功能需要后端支持
4. 导出功能需要后端返回正确的文件格式
5. 权限控制需要结合后端 JWT 认证 