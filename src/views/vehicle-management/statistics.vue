<template>
  <div class="vehicle-statistics">
    <div class="app-container">
      <h2>车辆统计</h2>
      
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <i class="el-icon-s-goods" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">总车辆数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <i class="el-icon-check" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.active }}</div>
                <div class="stat-label">可用车辆</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon inactive">
                <i class="el-icon-close" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.inactive }}</div>
                <div class="stat-label">不可用车辆</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon maintenance">
                <i class="el-icon-setting" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.maintenance }}</div>
                <div class="stat-label">维护中</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-section">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header">
              <span>车辆品牌分布</span>
            </div>
            <div id="brandChart" style="height: 300px;" />
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header">
              <span>车辆状态分布</span>
            </div>
            <div id="statusChart" style="height: 300px;" />
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="charts-section">
        <el-col :span="24">
          <el-card class="chart-card">
            <div slot="header">
              <span>车辆年份分布</span>
            </div>
            <div id="yearChart" style="height: 400px;" />
          </el-card>
        </el-col>
      </el-row>

      <!-- 最近维护记录 -->
      <el-card class="recent-maintenance">
        <div slot="header">
          <span>最近维护记录</span>
          <el-button type="text" @click="viewAllMaintenance">查看全部</el-button>
        </div>
        
        <el-table :data="recentMaintenanceRecords" border>
          <el-table-column label="车牌号" prop="licensePlate" width="120" />
          <el-table-column label="车辆型号" prop="model" width="150" />
          <el-table-column label="维护类型" prop="maintenanceType" width="100" />
          <el-table-column label="维护内容" prop="maintenanceContent" />
          <el-table-column label="维护日期" prop="maintenanceDate" width="120">
            <template slot-scope="{row}">
              <span>{{ row.maintenanceDate | parseTime('{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="费用" prop="cost" width="100">
            <template slot-scope="{row}">
              <span>￥{{ row.cost }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getVehicleStats } from '@/api/vehicle'
import { parseTime } from '@/utils'
import * as echarts from 'echarts'

export default {
  name: 'VehicleStatistics',
  filters: {
    parseTime
  },
  data() {
    return {
      statistics: {
        total: 0,
        active: 0,
        inactive: 0,
        maintenance: 0
      },
      recentMaintenanceRecords: [
        {
          licensePlate: '京A12345',
          model: '奔驰E300',
          maintenanceType: '保养',
          maintenanceContent: '更换机油、机滤',
          maintenanceDate: new Date('2023-12-01'),
          cost: 500
        },
        {
          licensePlate: '京B67890',
          model: '宝马X5',
          maintenanceType: '维修',
          maintenanceContent: '更换刹车片',
          maintenanceDate: new Date('2023-11-28'),
          cost: 800
        },
        {
          licensePlate: '京C11111',
          model: '奥迪A6',
          maintenanceType: '检查',
          maintenanceContent: '年检',
          maintenanceDate: new Date('2023-11-25'),
          cost: 200
        }
      ]
    }
  },
  mounted() {
    this.getStatistics()
    this.initCharts()
  },
  methods: {
    getStatistics() {
      getVehicleStats().then(response => {
        this.statistics = response.data.data || response.data || {
          total: 150,
          active: 120,
          inactive: 25,
          maintenance: 5
        }
      }).catch(() => {
        // 如果API调用失败，使用模拟数据
        this.statistics = {
          total: 150,
          active: 120,
          inactive: 25,
          maintenance: 5
        }
      })
    },
    
    initCharts() {
      this.initBrandChart()
      this.initStatusChart()
      this.initYearChart()
    },
    
    initBrandChart() {
      const chart = echarts.init(document.getElementById('brandChart'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: '品牌分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '40',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 35, name: '奔驰' },
              { value: 30, name: '宝马' },
              { value: 25, name: '奥迪' },
              { value: 20, name: '大众' },
              { value: 15, name: '丰田' },
              { value: 25, name: '其他' }
            ]
          }
        ]
      }
      chart.setOption(option)
    },
    
    initStatusChart() {
      const chart = echarts.init(document.getElementById('statusChart'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: '状态分布',
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
              { value: this.statistics.active, name: '可用', itemStyle: { color: '#67C23A' } },
              { value: this.statistics.inactive, name: '不可用', itemStyle: { color: '#F56C6C' } },
              { value: this.statistics.maintenance, name: '维护中', itemStyle: { color: '#E6A23C' } }
            ]
          }
        ]
      }
      chart.setOption(option)
    },
    
    initYearChart() {
      const chart = echarts.init(document.getElementById('yearChart'))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['车辆数量']
        },
        xAxis: {
          type: 'category',
          data: ['2018', '2019', '2020', '2021', '2022', '2023']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '车辆数量',
            type: 'bar',
            data: [12, 18, 25, 30, 35, 30],
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      chart.setOption(option)
    },
    
    viewAllMaintenance() {
      this.$router.push('/vehicle-management/maintenance')
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-statistics {
  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          
          i {
            font-size: 24px;
            color: white;
          }
          
          &.total {
            background: linear-gradient(45deg, #409EFF, #66b1ff);
          }
          
          &.active {
            background: linear-gradient(45deg, #67C23A, #85ce61);
          }
          
          &.inactive {
            background: linear-gradient(45deg, #F56C6C, #f78989);
          }
          
          &.maintenance {
            background: linear-gradient(45deg, #E6A23C, #ebb563);
          }
        }
        
        .stat-info {
          .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 5px;
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-card {
      ::v-deep .el-card__header {
        padding: 18px 20px;
        border-bottom: 1px solid #EBEEF5;
        
        span {
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }
  
  .recent-maintenance {
    ::v-deep .el-card__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      span {
        font-weight: bold;
        color: #303133;
      }
    }
  }
}
</style> 