<template>
  <div class="vehicle-detail">
    <div class="app-container">
      <div class="detail-header">
        <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        <h2>车辆详情</h2>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
          <el-button type="danger" icon="el-icon-delete" @click="handleDelete">删除</el-button>
        </div>
      </div>

      <el-card v-loading="loading" class="vehicle-info-card">
        <div slot="header" class="card-header">
          <span>基本信息</span>
          <el-tag type="success">
            可用
          </el-tag>
        </div>
        
        <div class="vehicle-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>车牌号：</label>
                <span>{{ vehicle.license_plate || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>车辆型号：</label>
                <span>{{ vehicle.model || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>品牌：</label>
                <span>{{ vehicle.brand || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>颜色：</label>
                <span>{{ vehicle.color || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>年份：</label>
                <span>{{ vehicle.year || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>车辆ID：</label>
                <span>{{ vehicle.che_liang_id || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>车主ID：</label>
                <span>{{ vehicle.owner_id || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>类型ID：</label>
                <span>{{ vehicle.type_id || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <label>车辆图片：</label>
                <div class="car-images">
                  <el-image
                    v-if="vehicle.images && vehicle.images.car_images && vehicle.images.car_images.length > 0"
                    v-for="(image, index) in vehicle.images.car_images"
                    :key="index"
                    :src="image"
                    :preview-src-list="vehicle.images.car_images"
                    style="width: 100px; height: 80px; margin-right: 10px; border-radius: 4px;"
                    fit="cover"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <span v-else class="no-image">暂无图片</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 维护记录 -->
      <el-card class="maintenance-card">
        <div slot="header" class="card-header">
          <span>维护记录</span>
          <el-button type="primary" size="small" @click="handleAddMaintenance">添加记录</el-button>
        </div>
        
        <el-table :data="maintenanceRecords" border>
          <el-table-column label="维护日期" prop="date" width="120">
            <template slot-scope="{row}">
              <span>{{ row.date | parseTime('{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="维护类型" prop="type" width="120" />
          <el-table-column label="维护内容" prop="content" />
          <el-table-column label="费用" prop="cost" width="100">
            <template slot-scope="{row}">
              <span>￥{{ row.cost }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="{row, $index}">
              <el-button size="mini" type="danger" @click="handleDeleteMaintenance($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 编辑对话框 -->
      <el-dialog title="编辑车辆" :visible.sync="editDialogVisible">
        <el-form ref="editForm" :model="editForm" :rules="rules" label-width="80px">
          <el-form-item label="车牌号" prop="license_plate">
            <el-input v-model="editForm.license_plate" />
          </el-form-item>
          <el-form-item label="车辆型号" prop="model">
            <el-input v-model="editForm.model" />
          </el-form-item>
          <el-form-item label="品牌" prop="brand">
            <el-input v-model="editForm.brand" />
          </el-form-item>
          <el-form-item label="颜色" prop="color">
            <el-input v-model="editForm.color" />
          </el-form-item>
          <el-form-item label="年份" prop="year">
            <el-input v-model="editForm.year" />
          </el-form-item>
          <el-form-item label="车主ID" prop="owner_id">
            <el-input-number v-model="editForm.owner_id" :min="1" />
          </el-form-item>
          <el-form-item label="类型ID" prop="type_id">
            <el-input-number v-model="editForm.type_id" :min="1" />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </div>
      </el-dialog>

      <!-- 添加维护记录对话框 -->
      <el-dialog title="添加维护记录" :visible.sync="maintenanceDialogVisible">
        <el-form ref="maintenanceForm" :model="maintenanceForm" label-width="80px">
          <el-form-item label="维护日期" prop="date">
            <el-date-picker v-model="maintenanceForm.date" type="date" />
          </el-form-item>
          <el-form-item label="维护类型" prop="type">
            <el-select v-model="maintenanceForm.type">
              <el-option label="保养" value="maintenance" />
              <el-option label="维修" value="repair" />
              <el-option label="检查" value="inspection" />
            </el-select>
          </el-form-item>
          <el-form-item label="维护内容" prop="content">
            <el-input v-model="maintenanceForm.content" type="textarea" :rows="3" />
          </el-form-item>
          <el-form-item label="费用" prop="cost">
            <el-input-number v-model="maintenanceForm.cost" :min="0" />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="maintenanceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveMaintenance">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { fetchVehicle, updateVehicle, deleteVehicle } from '@/api/vehicle'
import { parseTime } from '@/utils'

export default {
  name: 'VehicleDetail',
  filters: {
    parseTime
  },
  data() {
    return {
      loading: false,
      vehicle: {},
      editDialogVisible: false,
      maintenanceDialogVisible: false,
      editForm: {},
      maintenanceForm: {
        date: new Date(),
        type: '',
        content: '',
        cost: 0
      },
      maintenanceRecords: [
        {
          date: new Date('2023-01-15'),
          type: '保养',
          content: '更换机油、机滤',
          cost: 300
        },
        {
          date: new Date('2023-03-20'),
          type: '维修',
          content: '更换刹车片',
          cost: 800
        }
      ],
      rules: {
        license_plate: [{ required: true, message: '车牌号是必填项', trigger: 'blur' }],
        model: [{ required: true, message: '车辆型号是必填项', trigger: 'blur' }],
        brand: [{ required: true, message: '品牌是必填项', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getVehicleDetail()
  },
  methods: {
    getVehicleDetail() {
      const che_liang_id = this.$route.params.che_liang_id  
      this.loading = true
      fetchVehicle(che_liang_id).then(response => {
        this.vehicle = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    handleEdit() {
      this.editForm = { 
        ...this.vehicle,
        auto_id: this.vehicle.auto_id,
        che_liang_id: this.vehicle.che_liang_id
      }
      this.editDialogVisible = true
    },
    handleSaveEdit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          updateVehicle(this.editForm).then(() => {
            this.vehicle = { ...this.editForm }
            this.editDialogVisible = false
            this.$message.success('更新成功')
          }).catch(error => {
            console.error('更新失败：', error)
            this.$message.error('更新失败')
          })
        }
      })
    },
    handleDelete() {
      this.$confirm('确认删除这辆车吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteVehicle(this.vehicle.che_liang_id).then(() => {
          this.$message.success('删除成功')
          this.$router.push('/vehicle-management')
        }).catch(error => {
          console.error('删除失败：', error)
          this.$message.error('删除失败')
        })
      })
    },
    handleAddMaintenance() {
      this.maintenanceForm = {
        date: new Date(),
        type: '',
        content: '',
        cost: 0
      }
      this.maintenanceDialogVisible = true
    },
    handleSaveMaintenance() {
      this.maintenanceRecords.push({ ...this.maintenanceForm })
      this.maintenanceDialogVisible = false
      this.$message.success('添加成功')
    },
    handleDeleteMaintenance(index) {
      this.$confirm('确认删除这条维护记录吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.maintenanceRecords.splice(index, 1)
        this.$message.success('删除成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-detail {
  .detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0 20px;
      flex: 1;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .vehicle-info-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .vehicle-info {
    .info-item {
      margin-bottom: 15px;
      
      label {
        font-weight: bold;
        color: #606266;
        margin-right: 10px;
      }
      
      .description {
        margin: 5px 0;
        color: #909399;
        line-height: 1.5;
      }
    }
  }
  
  .maintenance-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .car-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
    
    .no-image {
      color: #909399;
      font-style: italic;
    }
    
    ::v-deep .el-image {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
    
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
      font-size: 20px;
    }
  }
}
</style> 