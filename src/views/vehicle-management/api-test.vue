<template>
  <div class="api-test">
    <div class="app-container">
      <h1>车辆管理API测试页面</h1>
      
      <div class="test-section">
        <h2>测试API排序功能</h2>
        
        <div class="form-group">
          <label>排序字段:</label>
          <el-select v-model="testQuery.order_by" placeholder="选择排序字段">
            <el-option label="ID" value="auto_id" />
            <el-option label="车牌号" value="license_plate" />
            <el-option label="型号" value="model" />
            <el-option label="品牌" value="brand" />
            <el-option label="颜色" value="color" />
            <el-option label="年份" value="year" />
            <el-option label="状态更新时间" value="status_updated_at" />
          </el-select>
        </div>
        
        <div class="form-group">
          <label>排序方向:</label>
          <el-radio-group v-model="testQuery.order_direction">
            <el-radio label="asc">升序</el-radio>
            <el-radio label="desc">降序</el-radio>
          </el-radio-group>
        </div>
        
        <div class="form-group">
          <label>页码:</label>
          <el-input-number v-model="testQuery.page" :min="1" />
        </div>
        
        <div class="form-group">
          <label>每页数量:</label>
          <el-input-number v-model="testQuery.size" :min="1" :max="100" />
        </div>
        
        <el-button type="primary" @click="testAPI" :loading="loading">测试API</el-button>
        <el-button type="info" @click="clearResults">清除结果</el-button>
      </div>
      
      <div class="results-section" v-if="results">
        <h3>API测试结果:</h3>
        
        <div class="api-info">
          <p><strong>API URL:</strong> {{ apiUrl }}</p>
          <p><strong>请求参数:</strong> {{ JSON.stringify(testQuery, null, 2) }}</p>
          <p><strong>响应状态:</strong> {{ results.success ? '成功' : '失败' }}</p>
          <p><strong>响应时间:</strong> {{ responseTime }}ms</p>
        </div>
        
        <div class="data-preview" v-if="results.success">
          <h4>数据预览 (前5条):</h4>
          <el-table :data="results.data.slice(0, 5)" border>
            <el-table-column prop="auto_id" label="ID" width="80" />
            <el-table-column prop="license_plate" label="车牌号" width="120" />
            <el-table-column prop="model" label="型号" width="120" />
            <el-table-column prop="brand" label="品牌" width="100" />
            <el-table-column prop="color" label="颜色" width="80" />
            <el-table-column prop="year" label="年份" width="80" />
            <el-table-column prop="status_updated_at" label="状态更新时间" width="160">
              <template slot-scope="{row}">
                {{ row.status_updated_at || '未更新' }}
              </template>
            </el-table-column>
          </el-table>
          <p><strong>总数据量:</strong> {{ results.total }}</p>
        </div>
        
        <div class="error-info" v-if="!results.success">
          <h4>错误信息:</h4>
          <pre>{{ JSON.stringify(results.error, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { fetchVehicleList } from '@/api/vehicle'

export default {
  name: 'ApiTest',
  data() {
    return {
      loading: false,
      testQuery: {
        page: 1,
        size: 20,
        order_by: 'auto_id',
        order_direction: 'desc'
      },
      results: null,
      apiUrl: '',
      responseTime: 0
    }
  },
  methods: {
    async testAPI() {
      this.loading = true
      const startTime = Date.now()
      
      try {
        // 构建API URL用于显示
        const baseUrl = 'http://************:15679/drive_ops/api/v1/vehicles'
        const params = new URLSearchParams(this.testQuery)
        this.apiUrl = `${baseUrl}?${params.toString()}`
        
        console.log('测试API调用:', this.testQuery)
        const response = await fetchVehicleList(this.testQuery)
        
        this.responseTime = Date.now() - startTime
        
        if (response.success) {
          this.results = {
            success: true,
            data: response.data.items || response.data,
            total: response.data.total || response.total
          }
          this.$message.success('API测试成功')
        } else {
          this.results = {
            success: false,
            error: response.message || '未知错误'
          }
          this.$message.error('API测试失败: ' + response.message)
        }
      } catch (error) {
        this.responseTime = Date.now() - startTime
        this.results = {
          success: false,
          error: error.message || error
        }
        this.$message.error('API测试失败: ' + error.message)
        console.error('API测试错误:', error)
      } finally {
        this.loading = false
      }
    },
    
    clearResults() {
      this.results = null
      this.apiUrl = ''
      this.responseTime = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.api-test {
  .app-container {
    padding: 20px;
  }
  
  .test-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    h2 {
      margin-bottom: 20px;
      color: #303133;
    }
    
    .form-group {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      label {
        width: 120px;
        font-weight: bold;
        color: #606266;
      }
      
      .el-select, .el-input-number {
        width: 200px;
      }
    }
  }
  
  .results-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
    }
    
    .api-info {
      background: #f0f9ff;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 20px;
      
      p {
        margin: 8px 0;
        font-size: 14px;
        
        strong {
          color: #303133;
        }
      }
    }
    
    .data-preview {
      margin-top: 20px;
      
      h4 {
        color: #303133;
        margin-bottom: 10px;
      }
      
      .el-table {
        margin-bottom: 15px;
      }
    }
    
    .error-info {
      background: #fef2f2;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #f87171;
      
      h4 {
        color: #dc2626;
        margin-bottom: 10px;
      }
      
      pre {
        color: #dc2626;
        font-size: 12px;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style> 