# 🌍 车辆状态管理系统国际化设置

## 📋 概述

车辆状态管理系统已成功集成国际化支持，**默认语言为缅甸语**，同时支持中文切换。系统会根据用户的 `accept-language` 头部信息自动选择合适的语言。

## 🔧 已实现功能

### ✅ 核心功能
- **默认缅甸语支持** - 系统启动时默认使用缅甸语
- **中文支持** - 完整的中文翻译
- **动态语言切换** - 无需刷新页面即可切换语言
- **状态持久化** - 语言选择会保存在 Cookie 中
- **完整的车辆状态翻译** - 所有10种车辆状态都有对应翻译

### ✅ 翻译内容
- 车辆状态名称和描述
- 界面按钮和标签
- 表单验证消息
- 成功/错误提示
- 对话框标题和内容
- 表格列标题

## 🚀 快速开始

### 1. 访问国际化演示页面
```
/vehicle-management/i18n-demo
```

### 2. 在代码中使用国际化

```vue
<template>
  <!-- 使用$t()函数进行翻译 -->
  <el-button>{{ $t('vehicle.common.search') }}</el-button>
  
  <!-- 动态属性也支持国际化 -->
  <el-input :placeholder="$t('vehicle.list.searchPlaceholder')" />
  
  <!-- 表格列标题 -->
  <el-table-column :label="$t('vehicle.list.licensePlate')" />
</template>

<script>
export default {
  methods: {
    showMessage() {
      // 在JavaScript中使用翻译
      this.$message.success(this.$t('vehicle.status.updateSuccess'))
    },
    
    getStatusName(status) {
      // 检查翻译是否存在
      const i18nKey = `vehicle.status.${status}`
      if (this.$te(i18nKey)) {
        return this.$t(i18nKey)
      }
      return status
    }
  }
}
</script>
```

### 3. 切换语言

```javascript
// 通过代码切换语言
this.$i18n.locale = 'my' // 切换到缅甸语
this.$i18n.locale = 'zh' // 切换到中文

// 同时更新Store状态
this.$store.dispatch('app/setLanguage', 'my')
```

## 📊 支持的语言

| 语言代码 | 语言名称 | 状态 | 覆盖率 |
|----------|----------|------|--------|
| `my` | မြန်မာ (缅甸语) | ✅ 默认 | 100% |
| `zh` | 中文 | ✅ 完整 | 100% |

## 🎯 翻译的车辆状态

### 缅甸语 (my)
- `active`: ပုံမှန်အသုံးပြုမှု
- `inactive`: ရပ်ဆိုင်းအသုံးပြုမှု
- `maintenance`: ပြုပြင်ထိန်းသိမ်းမှု
- `parked`: ရပ်ထားမှု
- `in_transit`: သယ်ပို့နေမှု
- `reserved`: ကြိုတင်မှာယူမှု
- `scrapped`: ဖျက်သိမ်းမှု
- `sold`: ရောင်းချပြီးမှု
- `lost`: ပျောက်ဆုံးမှု
- `damaged`: ပျက်စီးမှု

### 中文 (zh)
- `active`: 正常使用
- `inactive`: 停用
- `maintenance`: 维修中
- `parked`: 停放中
- `in_transit`: 运输中
- `reserved`: 预约中
- `scrapped`: 报废
- `sold`: 已售出
- `lost`: 丢失
- `damaged`: 损坏

## 🔧 系统配置

### 语言检测逻辑
1. 检查用户Cookie中保存的语言设置
2. 检查浏览器的 `accept-language` 头部
3. 默认使用缅甸语 (`my`)

### 文件结构
```
src/
├── lang/
│   ├── index.js          # 国际化配置入口
│   ├── my.js            # 缅甸语翻译
│   └── zh.js            # 中文翻译
├── store/modules/
│   └── app.js           # 语言状态管理
└── components/
    └── LangSelect.vue   # 语言选择组件
```

## 🛠️ 如何添加新语言

1. **创建语言文件**
```javascript
// src/lang/en.js
export default {
  vehicle: {
    status: {
      active: 'Active',
      inactive: 'Inactive',
      // ... 其他翻译
    }
  }
}
```

2. **更新语言配置**
```javascript
// src/lang/index.js
import enLocale from './en'
import elementEnLocale from 'element-ui/lib/locale/lang/en'

const messages = {
  my: { ...myLocale, ...elementMyLocale },
  zh: { ...zhLocale, ...elementZhLocale },
  en: { ...enLocale, ...elementEnLocale }
}
```

3. **更新语言选择组件**
```vue
<!-- src/components/LangSelect.vue -->
<el-dropdown-item command="en">English</el-dropdown-item>
```

## 🎨 样式自定义

### 缅甸语字体支持
```scss
// 为缅甸语添加合适的字体
body[lang="my"] {
  font-family: 'Noto Sans Myanmar', 'Myanmar Text', sans-serif;
}
```

### 右到左语言支持
```scss
// 如果需要支持阿拉伯语等RTL语言
.rtl {
  direction: rtl;
  text-align: right;
}
```

## 📱 响应式支持

语言选择器在不同设备上都能正常工作：

- **桌面端**: 下拉菜单形式
- **移动端**: 自动适应触摸操作
- **小屏幕**: 优化显示效果

## 🔍 调试技巧

### 查看当前语言
```javascript
console.log('当前语言:', this.$i18n.locale)
console.log('可用语言:', this.$i18n.availableLocales)
```

### 检查翻译是否存在
```javascript
if (this.$te('vehicle.status.active')) {
  console.log('翻译存在:', this.$t('vehicle.status.active'))
} else {
  console.log('翻译不存在')
}
```

### 查看所有翻译
```javascript
console.log('所有翻译:', this.$i18n.messages)
```

## 🐛 常见问题

### Q: 页面刷新后语言重置了？
A: 确保在 `getLanguage()` 函数中正确读取Cookie，并且Cookie没有过期。

### Q: 某些文字没有翻译？
A: 检查是否使用了 `$t()` 函数，并确保翻译键存在于语言文件中。

### Q: Element UI 组件没有翻译？
A: 确保在 `main.js` 中正确设置了Element UI的国际化配置。

### Q: 缅甸语显示乱码？
A: 检查页面字符编码是否为UTF-8，并确保字体支持缅甸语字符。

## 📈 性能优化

### 懒加载翻译
```javascript
// 大型应用可以考虑懒加载翻译文件
const messages = {
  my: () => import('./my.js'),
  zh: () => import('./zh.js')
}
```

### 缓存翻译结果
```javascript
// 在组件中缓存翻译结果
computed: {
  translatedStatus() {
    return this.$t('vehicle.status.active')
  }
}
```

## 🎉 完成！

您的车辆状态管理系统现在已经支持完整的国际化功能，**默认使用缅甸语**，可以无缝切换到中文，为不同语言的用户提供本地化体验。

访问 `/vehicle-management/i18n-demo` 查看完整的国际化演示！ 