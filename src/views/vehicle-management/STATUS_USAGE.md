# 车辆状态管理系统使用说明

## 📋 概述

车辆状态管理系统已成功集成到您的车辆管理平台中。该系统提供了完整的状态跟踪、管理和统计功能。

## 🎯 功能特性

### ✅ 已实现的功能

1. **状态管理页面** (`/vehicle-management/status`)
   - 状态统计卡片显示
   - 状态筛选和搜索
   - 批量状态更新
   - 实时状态统计

2. **状态管理组件** (`VehicleStatusManager`)
   - 状态显示
   - 快速编辑模式
   - 状态更新对话框
   - 状态变更通知

3. **API 集成**
   - 获取状态选项
   - 根据状态筛选车辆
   - 更新车辆状态
   - 获取状态统计

4. **界面增强**
   - 车辆列表页面集成状态显示
   - 状态筛选器
   - 美观的状态标签和图标

## 🚀 快速开始

### 1. 访问状态管理页面

```javascript
// 通过路由访问
this.$router.push('/vehicle-management/status')

// 或者从车辆列表页面点击"状态管理"按钮
```

### 2. 使用状态管理组件

```vue
<template>
  <vehicle-status-manager
    :vehicle-id="vehicleId"
    :current-status="currentStatus"
    :remark="statusRemark"
    :quick-edit="true"
    @status-updated="handleStatusUpdated"
  />
</template>

<script>
import VehicleStatusManager from '@/components/VehicleStatusManager'

export default {
  components: { VehicleStatusManager },
  methods: {
    handleStatusUpdated(data) {
      console.log('状态已更新:', data)
      // 处理状态更新逻辑
    }
  }
}
</script>
```

### 3. API 调用示例

```javascript
import { 
  getVehicleStatusOptions, 
  getVehiclesByStatus, 
  updateVehicleStatus, 
  getVehicleStatusStats 
} from '@/api/vehicle'

// 获取状态选项
const statusOptions = await getVehicleStatusOptions()

// 根据状态获取车辆
const vehicles = await getVehiclesByStatus({ status: 'active', page: 1, size: 10 })

// 更新车辆状态
await updateVehicleStatus(vehicleId, {
  status: 'maintenance',
  status_remark: '定期保养'
})

// 获取状态统计
const stats = await getVehicleStatusStats()
```

## 📊 支持的状态类型

| 状态值 | 中文名称 | 颜色 | 图标 | 使用场景 |
|--------|----------|------|------|----------|
| `active` | 正常使用 | 绿色 | check-circle | 车辆正常运行 |
| `inactive` | 停用 | 黄色 | pause-circle | 车辆暂停使用 |
| `maintenance` | 维修中 | 蓝色 | tools | 车辆维修保养 |
| `parked` | 停放中 | 紫色 | car | 车辆停放 |
| `in_transit` | 运输中 | 青色 | truck | 车辆运输中 |
| `reserved` | 预约中 | 粉色 | calendar | 车辆已预约 |
| `scrapped` | 报废 | 灰色 | delete | 车辆报废 |
| `sold` | 已售出 | 绿色 | money | 车辆已售出 |
| `lost` | 丢失 | 红色 | warning | 车辆丢失 |
| `damaged` | 损坏 | 红色 | close | 车辆损坏 |

## 🎨 样式定制

### 状态颜色定制

在 `src/styles/vehicle-status.scss` 中修改状态颜色：

```scss
$status-colors: (
  'active': #52c41a,
  'inactive': #faad14,
  'maintenance': #1890ff,
  // ... 其他状态
);
```

### 状态标签样式

```scss
.vehicle-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  // 自定义样式
}
```

## 📱 响应式设计

系统已适配移动端设备：

- 状态卡片在小屏幕上自动调整布局
- 筛选器在移动端显示为堆叠模式
- 状态标签在触摸设备上优化交互

## 🔧 配置选项

### 组件配置

```vue
<vehicle-status-manager
  :vehicle-id="vehicleId"          // 车辆ID（必需）
  :current-status="currentStatus"  // 当前状态（必需）
  :remark="statusRemark"          // 状态备注（可选）
  :quick-edit="true"              // 启用快速编辑（可选）
  @status-updated="handleUpdate"   // 状态更新回调（可选）
/>
```

### 状态筛选配置

```javascript
// 在车辆列表页面
listQuery: {
  page: 1,
  size: 20,
  status: 'active',  // 状态筛选
  q: ''             // 搜索关键词
}
```

## 📈 统计功能

### 状态统计卡片

系统会自动显示各状态的车辆数量：

```javascript
// 获取统计数据
const stats = {
  'active': 15,
  'maintenance': 3,
  'parked': 8,
  'inactive': 2
}
```

### 统计图表

可以扩展添加图表组件：

```vue
<template>
  <div class="status-chart">
    <!-- 这里可以集成 ECharts 或其他图表库 -->
  </div>
</template>
```

## 🛠️ 扩展功能

### 添加新状态

1. 在后端API中添加新的状态常量
2. 在前端更新状态颜色映射
3. 添加对应的图标和样式

### 状态变更历史

可以扩展添加状态变更记录：

```javascript
// 扩展API
export function getVehicleStatusHistory(vehicleId) {
  return request({
    url: `/api/v1/vehicles/${vehicleId}/status-history`,
    method: 'get'
  }, 'shwethe_drive_ops')
}
```

### 批量状态更新

```javascript
// 批量更新状态
export function batchUpdateVehicleStatus(vehicleIds, statusData) {
  return request({
    url: '/api/v1/vehicles/batch-status',
    method: 'put',
    data: { vehicle_ids: vehicleIds, ...statusData }
  }, 'shwethe_drive_ops')
}
```

## 🐛 常见问题

### Q: 状态更新后页面没有刷新？
A: 确保在状态更新后调用 `getList()` 方法或使用 `handleStatusUpdated` 回调更新本地数据。

### Q: 状态颜色显示不正确？
A: 检查 `vehicle-status.scss` 样式文件是否正确导入到主样式文件中。

### Q: 移动端状态标签太小？
A: 在 `vehicle-status.scss` 中调整响应式样式的字体大小和内边距。

### Q: 如何添加自定义状态？
A: 需要同时在后端API和前端代码中添加新状态的定义、颜色和图标映射。

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 确认API端点是否正确响应
3. 检查网络请求是否成功
4. 查看组件props是否正确传递

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 初始版本发布
- ✅ 基础状态管理功能
- ✅ 状态统计和筛选
- ✅ 响应式设计
- ✅ 组件化架构

### 计划中的功能
- 🔄 状态变更历史记录
- 🔄 批量状态操作
- 🔄 状态变更通知
- 🔄 高级筛选和搜索
- 🔄 导出功能

---

**🎉 恭喜！您的车辆状态管理系统已经成功部署并可以使用了！** 