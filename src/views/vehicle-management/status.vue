<template>
  <div class="vehicle-status-management">
    <div class="app-container">
      <!-- 状态统计卡片 -->
      <div class="status-stats-container">
        <el-row :gutter="20">
          <el-col :span="6" v-for="stat in statusStats" :key="stat.status">
            <div class="status-card" :style="{ borderColor: stat.color }">
              <div class="status-icon" :style="{ backgroundColor: stat.color }">
                <i :class="`el-icon-${stat.icon}`"></i>
              </div>
              <div class="status-info">
                <h3>{{ stat.count }}</h3>
                <p>{{ stat.status_name }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选器 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.q"
          :placeholder="$t('vehicle.list.searchPlaceholder')"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          :placeholder="$t('vehicle.status.selectStatus')"
          clearable
          style="width: 150px"
          class="filter-item"
        >
          <el-option
            v-for="option in statusOptions"
            :key="option.status"
            :label="getStatusName(option.status)"
            :value="option.status"
          />
        </el-select>
        <el-select
          v-model="listQuery.type_id"
          :placeholder="$t('vehicle.list.searchByType')"
          clearable
          style="width: 150px"
          class="filter-item"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in vehicleTypes"
            :key="type.type_id"
            :label="type.type_name"
            :value="type.type_id"
          />
        </el-select>
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('vehicle.common.search') }}
        </el-button>
        <el-button
          class="filter-item"
          type="success"
          icon="el-icon-refresh"
          @click="refreshStats"
        >
          {{ $t('vehicle.statistics.refreshStats') }}
        </el-button>
        <el-button
          class="filter-item"
          type="info"
          icon="el-icon-info"
          @click="debugQuery"
          style="margin-left: 5px;"
        >
          调试查询
        </el-button>
      </div>

      <!-- 车辆列表 -->
      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="auto_id" sortable="custom" align="center" width="80">
          <template slot-scope="{row}">
            <span>{{ row.auto_id }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.licensePlate')" width="150px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.license_plate }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.model')" width="150px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.model }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.brand')" width="120px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.brand }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.status.current')" width="120px" align="center">
          <template slot-scope="{row}">
            <el-tag
              :type="getStatusTagType(row.status)"
              :color="getStatusColor(row.status)"
              :style="{ color: 'white' }"
            >
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.status.remark')" min-width="200px">
          <template slot-scope="{row}">
            <span>{{ row.status_remark || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.updateTime')" width="180px" align="center">
          <template slot-scope="{row}">
            <span>{{ row.updated_at | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('vehicle.list.actions')" align="center" width="200" class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleUpdateStatus(row)">
              {{ $t('vehicle.status.update') }}
            </el-button>
            <el-button type="info" size="mini" @click="handleViewDetail(row)">
              {{ $t('vehicle.common.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />

      <!-- 状态更新对话框 -->
      <el-dialog
        :title="$t('vehicle.dialog.updateStatus')"
        :visible.sync="dialogVisible"
        width="500px"
        @close="resetForm"
      >
        <el-form ref="statusForm" :model="statusForm" :rules="statusRules" label-width="80px">
          <el-form-item :label="$t('vehicle.list.licensePlate')">
            <el-input v-model="statusForm.license_plate" disabled />
          </el-form-item>
          <el-form-item :label="$t('vehicle.status.current')">
            <el-tag
              :color="getStatusColor(statusForm.current_status)"
              :style="{ color: 'white' }"
            >
              {{ getStatusName(statusForm.current_status) }}
            </el-tag>
          </el-form-item>
          <el-form-item :label="$t('vehicle.status.new')" prop="status">
            <el-select v-model="statusForm.status" :placeholder="$t('vehicle.status.selectNewStatus')">
              <el-option
                v-for="option in statusOptions"
                :key="option.status"
                :label="getStatusName(option.status)"
                :value="option.status"
              >
                <span style="float: left">{{ getStatusName(option.status) }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ option.description }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('vehicle.status.remark')">
            <el-input
              v-model="statusForm.status_remark"
              type="textarea"
              :rows="3"
              :placeholder="$t('vehicle.status.enterRemark')"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('vehicle.common.cancel') }}</el-button>
          <el-button type="primary" @click="submitStatusUpdate">{{ $t('vehicle.common.confirm') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { 
  fetchVehicleList,
  getVehiclesByStatus, 
  getVehicleStatusOptions, 
  getVehicleStatusStats, 
  updateVehicleStatus,
  getVehicleTypes
} from '@/api/vehicle'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'VehicleStatusManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 20,
        status: '',
        q: '',
        type_id: undefined
      },
      statusOptions: [],
      vehicleTypes: [],
      statusStats: [],
      dialogVisible: false,
      statusForm: {
        che_liang_id: '',
        license_plate: '',
        current_status: '',
        status: '',
        status_remark: ''
      },
      statusRules: {
        status: [
          { required: true, message: this.$t('vehicle.form.statusRequired'), trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getStatusOptions()
    this.getVehicleTypes()
    this.getStatusStats()
    this.getList()
  },
  methods: {
    async getStatusOptions() {
      try {
        const response = await getVehicleStatusOptions()
        this.statusOptions = response.data
      } catch (error) {
        console.error(this.$t('vehicle.message.getStatusOptionsError'), error)
        this.$message.error(this.$t('vehicle.message.getStatusOptionsError'))
      }
    },
    async getVehicleTypes() {
      try {
        const response = await getVehicleTypes()
        this.vehicleTypes = response.data
      } catch (error) {
        console.error('获取车辆类型失败', error)
      }
    },
    async getStatusStats() {
      try {
        const response = await getVehicleStatusStats()
        const stats = response.data
        this.statusStats = this.statusOptions.map(option => ({
          ...option,
          count: stats[option.status] || 0
        }))
      } catch (error) {
        console.error(this.$t('vehicle.message.getStatsError'), error)
        this.$message.error(this.$t('vehicle.message.getStatsError'))
      }
    },
    async getList() {
      this.listLoading = true
      console.log('status.vue - 当前查询参数:', this.listQuery)
      
      // 清理空的参数
      const cleanQuery = this.cleanQueryParams(this.listQuery)
      console.log('status.vue - 清理后的参数:', cleanQuery)
      
      try {
        // 使用 fetchVehicleList 来支持所有筛选参数，包括 type_id
        const response = await fetchVehicleList(cleanQuery)
        if (response.data.items) {
          this.list = response.data.items
          this.total = response.data.total
        } else {
          this.list = response.data
          this.total = response.total
        }
      } catch (error) {
        console.error(this.$t('vehicle.message.getListError'), error)
        this.$message.error(this.$t('vehicle.message.getListError'))
      } finally {
        this.listLoading = false
      }
    },
    debugQuery() {
      console.log('=== 状态页面调试查询参数 ===')
      console.log('原始 listQuery:', this.listQuery)
      console.log('vehicleTypes:', this.vehicleTypes)
      console.log('statusOptions:', this.statusOptions)
      
      const cleanQuery = this.cleanQueryParams(this.listQuery)
      console.log('清理后的查询参数:', cleanQuery)
      
      // 显示在页面上
      this.$alert(`
        原始参数: ${JSON.stringify(this.listQuery, null, 2)}
        
        清理后参数: ${JSON.stringify(cleanQuery, null, 2)}
        
        车辆类型数量: ${this.vehicleTypes.length}
      `, '状态页面调试信息', {
        confirmButtonText: '确定'
      })
    },
    handleTypeChange(typeId) {
      console.log('状态页面 - 车辆类型变更:', typeId)
      console.log('更新后的listQuery:', this.listQuery)
      // 自动触发搜索
      this.handleFilter()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    refreshStats() {
      this.getStatusStats()
    },
    handleUpdateStatus(row) {
      this.statusForm = {
        che_liang_id: row.che_liang_id,
        license_plate: row.license_plate,
        current_status: row.status,
        status: '',
        status_remark: ''
      }
      this.dialogVisible = true
    },
    handleViewDetail(row) {
      this.$router.push({
        path: '/vehicle-management/detail',
        query: { id: row.che_liang_id }
      })
    },
    async submitStatusUpdate() {
      this.$refs.statusForm.validate(async (valid) => {
        if (valid) {
          try {
            await updateVehicleStatus(this.statusForm.che_liang_id, {
              status: this.statusForm.status,
              status_remark: this.statusForm.status_remark
            })
            this.$message.success(this.$t('vehicle.status.updateSuccess'))
            this.dialogVisible = false
            this.getList()
            this.getStatusStats()
          } catch (error) {
            this.$message.error(this.$t('vehicle.status.updateError') + ': ' + error.message)
          }
        }
      })
    },
    resetForm() {
      this.$refs.statusForm && this.$refs.statusForm.resetFields()
    },
    cleanQueryParams(query) {
      // 清理空的参数，只保留有效值
      const cleanQuery = {}
      Object.keys(query).forEach(key => {
        const value = query[key]
        if (value !== undefined && value !== null && value !== '') {
          cleanQuery[key] = value
        }
      })
      return cleanQuery
    },
    getStatusColor(status) {
      const statusMap = {
        'active': '#52c41a',
        'inactive': '#faad14',
        'maintenance': '#1890ff',
        'parked': '#722ed1',
        'in_transit': '#13c2c2',
        'reserved': '#eb2f96',
        'scrapped': '#8c8c8c',
        'sold': '#52c41a',
        'lost': '#f5222d',
        'damaged': '#ff4d4f'
      }
      return statusMap[status] || '#8c8c8c'
    },
    getStatusName(status) {
      // 优先使用国际化翻译
      const i18nKey = `vehicle.status.${status}`
      if (this.$te(i18nKey)) {
        return this.$t(i18nKey)
      }
      // 如果没有国际化翻译，使用API返回的状态名
      const option = this.statusOptions.find(o => o.status === status)
      return option ? option.status_name : status
    },
    getStatusTagType(status) {
      const typeMap = {
        'active': 'success',
        'inactive': 'warning',
        'maintenance': 'primary',
        'parked': '',
        'in_transit': 'info',
        'reserved': 'danger',
        'scrapped': 'info',
        'sold': 'success',
        'lost': 'danger',
        'damaged': 'danger'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-status-management {
  .status-stats-container {
    margin-bottom: 20px;
    
    .status-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-left: 4px solid #ddd;
      
      .status-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        i {
          font-size: 24px;
          color: white;
        }
      }
      
      .status-info {
        h3 {
          margin: 0 0 5px 0;
          font-size: 24px;
          font-weight: bold;
          color: #333;
        }
        
        p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }
      }
    }
  }
  
  .filter-container {
    margin-bottom: 20px;
    
    .filter-item {
      margin-right: 10px;
    }
  }
}
</style> 