<template>
  <div class="app-container">
    <!-- 货币说明面板 -->
    <el-alert
      title="货币说明"
      type="info"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
      class="currency-info-panel"
    >
      <template slot="description">
        <div style="display: flex; gap: 30px; align-items: center;">
          <div style="display: flex; align-items: center;">
            <span style="color: #E6A23C; font-weight: bold; margin-right: 8px;">฿</span>
            <span>默认值使用<strong>泰币(THB)</strong></span>
          </div>
          <div style="display: flex; align-items: center;">
            <span style="color: #F56C6C; font-weight: bold; margin-right: 8px;">K</span>
            <span>子默认值使用<strong>缅币(MMK)</strong></span>
          </div>
        </div>
      </template>
    </el-alert>

    <!-- 页签切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="岗位列表" name="positions">
        <div class="filter-container">
          <el-input v-model="positionQuery.name" placeholder="岗位名称" style="width: 200px" class="filter-item" />
          <el-select v-model="positionQuery.department_id" placeholder="部门" clearable style="width: 200px" class="filter-item">
            <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handlePositionFilter">
            搜索
          </el-button>
          <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreatePosition">
            新增岗位
          </el-button>
        </div>

        <!-- 岗位统计卡片 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">岗位总数</div>
                <div class="card-value">{{ positionStats.total }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">启用岗位</div>
                <div class="card-value">{{ positionStats.active }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">已设薪资模板</div>
                <div class="card-value">{{ positionStats.withTemplate }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">部门数量</div>
                <div class="card-value">{{ positionStats.departments }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 岗位表格 -->
        <el-table
          :key="positionTableKey"
          v-loading="positionLoading"
          :data="positionList"
          border
          fit
          highlight-current-row
          style="width: 100%;"
          @row-click="handlePositionRowClick"
        >
          <el-table-column label="岗位编码" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.position_code || row.code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗位名称" width="150px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.position_name || row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="部门" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.department ? row.department.dept_name : getDepartmentNameById(row.department_id) || '未分配' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基础薪资" width="100px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.base_position_salary ? formatCurrency(row.base_position_salary) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="薪资范围" width="150px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.min_salary && row.max_salary">
                {{ formatCurrency(row.min_salary) }} - {{ formatCurrency(row.max_salary) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="岗位描述" min-width="200px" align="left">
            <template slot-scope="{row}">
              <span>{{ row.description }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80px" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ formatDate(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180px">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" @click="viewPositionTemplates(row)">
                查看薪资模板
              </el-button>
              <el-button type="success" size="mini" @click="createTemplateForPosition(row)">
                创建模板
              </el-button>
              <el-button type="info" size="mini" @click="handleUpdatePosition(row)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="positionTotal > 0"
          :total="positionTotal"
          :page.sync="positionQuery.page"
          :limit.sync="positionQuery.limit"
          @pagination="getPositionList"
        />
      </el-tab-pane>

      <el-tab-pane label="岗位薪资模板" name="templates">
        <div class="filter-container">
          <el-select v-model="templateQuery.position_id" placeholder="选择岗位" clearable style="width: 200px" class="filter-item">
            <el-option v-for="item in positionOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="templateQuery.item_id" placeholder="薪资项目类型" clearable style="width: 200px" class="filter-item">
            <el-option v-for="item in salaryItemTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleTemplateFilter">
            搜索
          </el-button>
          <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreateTemplate">
            新增薪资模板
          </el-button>
        </div>

        <!-- 薪资模板统计 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">模板总数</div>
                <div class="card-value">{{ templateStats.total }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">涉及岗位</div>
                <div class="card-value">{{ templateStats.positions }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">薪资项目类型</div>
                <div class="card-value">{{ templateStats.itemTypes }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">平均模板数/岗位</div>
                <div class="card-value">{{ templateStats.avgPerPosition }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 薪资模板树状表格 -->
        <el-table
          :key="templateTableKey"
          v-loading="templateLoading"
          :data="templateTreeData"
          border
          fit
          highlight-current-row
          style="width: 100%;"
          row-key="id"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        >
          <el-table-column label="岗位/模板" width="200px" align="left">
            <template slot-scope="{row}">
              <div v-if="row.type === 'position'" class="position-row">
                <i class="el-icon-office-building" />
                <span class="position-name">{{ row.name }}</span>
                <el-tag size="mini" style="margin-left: 10px;">{{ row.department }}</el-tag>
              </div>
              <div v-else class="template-row">
                <i class="el-icon-document" />
                <span>{{ row.itemName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="薪资项目类型" width="150px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'">
                <el-tag :type="getItemTypeTagColor(row.itemType)">
                  {{ row.itemType }}
                </el-tag>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="默认值(泰币)" width="120px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'" class="default-value">
                <el-tooltip content="泰币(THB)" placement="top">
                  <span class="currency-display">{{ formatThaiCurrency(row.defaultValue) }}</span>
                </el-tooltip>
              </span>
              <span v-else-if="row.type === 'position'" class="total-value">
                <el-tooltip content="泰币(THB)总计" placement="top">
                  <span class="currency-display">总计: {{ formatThaiCurrency(row.totalValue) }}</span>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="子默认值(缅币)" width="120px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'" class="sub-default-value">
                <el-tooltip content="缅币(MMK)" placement="top">
                  <span class="currency-display">{{ formatMyanmarCurrency(row.subDefaultValue) }}</span>
                </el-tooltip>
              </span>
              <span v-else-if="row.type === 'position'" class="total-sub-value">
                <el-tooltip content="缅币(MMK)总计" placement="top">
                  <span class="currency-display">子项总计: {{ formatMyanmarCurrency(row.totalSubValue) }}</span>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'">{{ formatDate(row.createdAt) }}</span>
              <span v-else-if="row.type === 'position'">{{ formatDate(row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200px">
            <template slot-scope="{row}">
              <div v-if="row.type === 'template'">
                <el-button type="primary" size="mini" @click="handleUpdateTemplate(row)">
                  编辑
                </el-button>
                <el-button size="mini" type="info" @click="handleViewTemplate(row)">
                  查看
                </el-button>
                <el-button size="mini" type="danger" @click="handleDeleteTemplate(row)">
                  删除
                </el-button>
              </div>
              <div v-else-if="row.type === 'position'">
                <el-button type="success" size="mini" @click="createTemplateForPosition(row)">
                  添加薪资项目
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="templateTotal > 0"
          :total="templateTotal"
          :page.sync="templateQuery.page"
          :limit.sync="templateQuery.limit"
          @pagination="getTemplateList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑岗位对话框 -->
    <el-dialog :title="positionDialogTitle" :visible.sync="positionDialogVisible" width="800px">
      <el-form ref="positionForm" :rules="positionRules" :model="positionTemp" label-position="left" label-width="120px" style="width: 90%; margin-left:50px;">
        <el-form-item label="岗位编码" prop="code">
          <el-input v-model="positionTemp.code" placeholder="请输入岗位编码" />
        </el-form-item>
        <el-form-item label="岗位名称" prop="name">
          <el-input v-model="positionTemp.name" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="部门" prop="department_id">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-select v-model="positionTemp.department_id" placeholder="请选择部门" style="flex: 1;">
              <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" icon="el-icon-s-grid" size="small" @click="openDepartmentSelector">
              树形选择
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="薪资范围">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="min_salary">
                <el-input-number
                  v-model="positionTemp.min_salary"
                  :min="0"
                  :precision="0"
                  style="width: 100%"
                  placeholder="最低薪资"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="max_salary">
                <el-input-number
                  v-model="positionTemp.max_salary"
                  :min="0"
                  :precision="0"
                  style="width: 100%"
                  placeholder="最高薪资"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="基础薪资" prop="base_position_salary">
          <el-input-number
            v-model="positionTemp.base_position_salary"
            :min="0"
            :precision="0"
            style="width: 100%"
            placeholder="请输入基础薪资"
          />
        </el-form-item>
        <el-form-item label="岗位描述" prop="description">
          <el-input
            v-model="positionTemp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入岗位描述"
          />
        </el-form-item>
        <el-form-item label="任职要求" prop="requirements">
          <el-input
            v-model="positionTemp.requirements"
            type="textarea"
            :rows="3"
            placeholder="请输入任职要求"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="positionTemp.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="positionDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="positionDialogStatus==='create'?createPositionData():updatePositionData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 部门树形选择器对话框 -->
    <department-selector
      :visible.sync="departmentSelectorVisible"
      :data="departmentTreeData"
      :multiple="false"
      @confirm="handleDepartmentSelected"
    />

    <!-- 添加/编辑薪资模板对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="120px" style="width: 90%; margin-left:50px;">
        <el-form-item label="岗位" prop="position_id">
          <el-select
            v-model="temp.position_id"
            placeholder="请选择岗位"
            style="width: 100%"
            filterable
            clearable
            :disabled="dialogStatus === 'update' || isPositionPreset"
          >
            <el-option
              v-for="item in positionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.department }}</span>
            </el-option>
          </el-select>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            <span v-if="dialogStatus === 'create' && !isPositionPreset">当前选中的岗位ID: {{ temp.position_id }}</span>
            <span v-else-if="dialogStatus === 'create' && isPositionPreset" class="readonly-tip">已为指定岗位创建模板，岗位信息不可修改</span>
            <span v-else-if="dialogStatus === 'update'" class="readonly-tip">编辑模式下岗位信息不可修改</span>
          </div>
        </el-form-item>
        <el-form-item label="薪资项目类型" prop="item_id">
          <el-select
            v-model="temp.item_id"
            placeholder="请选择薪资项目类型"
            style="width: 100%"
            :disabled="dialogStatus === 'update'"
            filterable
          >
            <el-option
              v-for="item in salaryItemTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 12px">{{ item.itemKey }}</span>
            </el-option>
          </el-select>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            <span v-if="dialogStatus === 'update'" class="readonly-tip">编辑模式下薪资项目类型不可修改</span>
            <span v-else>支持搜索薪资项目类型名称</span>
          </div>
        </el-form-item>
        <el-form-item label="默认值(泰币)" prop="default_value">
          <el-input-number
            v-model="temp.default_value"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入默认值（泰币）"
            class="thai-currency-input"
          >
            <template slot="prepend">฿</template>
          </el-input-number>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            该值将作为该岗位此薪资项目的默认金额（泰币฿）
          </div>
        </el-form-item>
        <el-form-item label="子默认值(缅币)" prop="sub_default_value">
          <el-input-number
            v-model="temp.sub_default_value"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入子默认值（缅币）"
            class="myanmar-currency-input"
          >
            <template slot="append">K</template>
          </el-input-number>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            该值将作为该岗位此薪资项目的子项默认金额（缅币K）
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看薪资模板详情对话框 -->
    <el-dialog title="薪资模板详情" :visible.sync="dialogViewVisible" width="600px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="ID">{{ viewData.id }}</el-descriptions-item>
        <el-descriptions-item label="岗位名称">{{ viewData.positionName }}</el-descriptions-item>
        <el-descriptions-item label="岗位编码">{{ viewData.positionCode }}</el-descriptions-item>
        <el-descriptions-item label="薪资项目类型">
          <el-tag :type="getItemTypeTagColor(viewData.item_id)">
            {{ viewData.itemTypeName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="默认值(泰币)">
          <span class="default-value">
            <el-tooltip content="泰币(THB) - Thai Baht" placement="top">
              <span class="currency-display">{{ formatThaiCurrency(viewData.default_value) }}</span>
            </el-tooltip>
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="子默认值(缅币)">
          <span class="sub-default-value">
            <el-tooltip content="缅币(MMK) - Myanmar Kyat" placement="top">
              <span class="currency-display">{{ formatMyanmarCurrency(viewData.sub_default_value) }}</span>
            </el-tooltip>
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewData.created_at }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewData.updated_at }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import DepartmentSelector from '@/components/DepartmentTreeView/DepartmentSelector.vue'
import {
  getPositionList,
  createPosition,
  updatePosition,
  deletePosition,
  getPositionStats,
  getPositionSalaryTemplateList,
  createPositionSalaryTemplate,
  updatePositionSalaryTemplate,
  deletePositionSalaryTemplate,
  getPositionSalaryTemplateDetail,
  getPositionOptions
} from '@/api/labor-project/positions'
import { getSalaryItemTypeList } from '@/api/labor-project/salaries'
import { getDepartmentTree } from '@/api/labor-project/departments'

export default {
  name: 'PositionManagement',
  components: { Pagination, DepartmentSelector },
  directives: { waves },
  data() {
    return {
      activeTab: 'positions', // 默认显示岗位列表

      // 岗位相关数据
      positionTableKey: 0,
      positionLoading: true,
      positionList: [],
      positionTotal: 0,
      positionQuery: {
        page: 1,
        limit: 20,
        name: undefined,
        department_id: undefined // 修改为 department_id
      },
      positionStats: {
        total: 0,
        active: 0,
        withTemplate: 0,
        departments: 0
      },
      selectedPosition: null,

      // 岗位对话框相关
      positionDialogVisible: false,
      positionDialogStatus: '',
      positionDialogTitle: '',
      positionTemp: {
        id: undefined,
        code: '',
        name: '',
        department_id: '', // 修改为 department_id
        min_salary: 0,
        max_salary: 0,
        base_position_salary: 0,
        description: '',
        requirements: '',
        status: 'active'
      },
      positionRules: {
        code: [
          { required: true, message: '岗位编码是必填项', trigger: 'blur' },
          { min: 2, max: 20, message: '岗位编码长度在2到20个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '岗位名称是必填项', trigger: 'blur' },
          { min: 2, max: 50, message: '岗位名称长度在2到50个字符', trigger: 'blur' }
        ],
        department_id: [
          { required: true, message: '部门是必选项', trigger: 'change' }
        ],
        min_salary: [
          { type: 'number', min: 0, message: '最低薪资必须是大于等于0的数字', trigger: 'blur' }
        ],
        max_salary: [
          { type: 'number', min: 0, message: '最高薪资必须是大于等于0的数字', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && this.positionTemp.min_salary && value < this.positionTemp.min_salary) {
                callback(new Error('最高薪资不能低于最低薪资'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        base_position_salary: [
          { type: 'number', min: 0, message: '基础薪资必须是大于等于0的数字', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态是必选项', trigger: 'change' }
        ]
      },

      // 薪资模板相关数据
      templateTableKey: 0,
      templateLoading: true,
      templateTreeData: [],
      templateTotal: 0,
      templateQuery: {
        page: 1,
        limit: 20,
        position_id: undefined,
        item_id: undefined
      },
      templateStats: {
        total: 0,
        positions: 0,
        itemTypes: 0,
        avgPerPosition: 0
      },

      positionOptions: [],
      salaryItemTypeOptions: [],
      departmentOptions: [], // 部门选项

      // 部门树形选择器相关
      departmentSelectorVisible: false,
      departmentTreeData: [],
      temp: {
        id: undefined,
        position_id: undefined,
        item_id: undefined,
        default_value: 0,
        sub_default_value: 0
      },
      dialogFormVisible: false,
      dialogViewVisible: false,
      dialogStatus: '',
      isPositionPreset: false, // 标识是否预设了岗位
      textMap: {
        update: '编辑岗位薪资模板',
        create: '新增岗位薪资模板'
      },
      viewData: {},
      rules: {
        position_id: [
          { required: true, message: '岗位是必选项', trigger: 'change' },
          { type: 'number', message: '请选择有效的岗位', trigger: 'change' }
        ],
        item_id: [
          { required: true, message: '薪资项目类型是必选项', trigger: 'change' },
          { type: 'number', message: '请选择有效的薪资项目类型', trigger: 'change' }
        ],
        default_value: [
          { required: true, message: '默认值是必填项', trigger: 'blur' },
          { type: 'number', min: 0, message: '默认值必须是大于等于0的数字', trigger: 'blur' }
        ],
        sub_default_value: [
          { required: true, message: '子默认值是必填项', trigger: 'blur' },
          { type: 'number', min: 0, message: '子默认值必须是大于等于0的数字', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    console.log('🚀 岗位管理页面初始化开始...')

    // 先加载基础数据 - 按顺序加载，确保部门数据在岗位数据之前加载
    await this.loadDepartmentOptions()
    await this.loadDepartmentTreeData()
    await this.loadSalaryItemTypeOptions()
    await this.loadPositionOptions()

    // 然后根据当前标签页加载相应数据
    if (this.activeTab === 'positions') {
      this.getPositionList()
    } else if (this.activeTab === 'templates') {
      this.getTemplateList()
    }

    console.log('🚀 岗位管理页面初始化完成')
  },
  methods: {
    // 获取岗位列表
    async getPositionList() {
      this.positionLoading = true
      try {
        console.log('🔍 正在获取岗位列表...')
        const response = await getPositionList(this.positionQuery)
        console.log('✅ 岗位列表响应:', response)

        if (response && response.data) {
          // API返回的数据直接在data字段下
          this.positionList = response.data || []
          // 分页信息在根级别
          this.positionTotal = response.total || 0
        } else {
          this.positionList = []
          this.positionTotal = 0
        }

        console.log('📋 处理后的岗位列表:', this.positionList)
        console.log('📊 岗位总数:', this.positionTotal)

        this.updatePositionStats()
      } catch (error) {
        console.error('❌ 获取岗位列表失败:', error)
        this.$message.error('获取岗位列表失败: ' + error.message)
        this.positionList = []
        this.positionTotal = 0
      } finally {
        this.positionLoading = false
      }
    },

    // 获取薪资模板列表
    async getTemplateList() {
      this.templateLoading = true
      try {
        console.log('🔍 正在获取岗位薪资模板列表...')
        const response = await getPositionSalaryTemplateList(this.templateQuery)
        console.log('✅ 岗位薪资模板列表响应:', response)

        if (response && response.data) {
          const templates = Array.isArray(response.data) ? response.data : []
          this.templateTreeData = this.processTemplateData(templates)
          // 分页信息可能在根级别
          this.templateTotal = response.total || response.pagination?.total || templates.length
        } else {
          this.templateTreeData = []
          this.templateTotal = 0
        }

        console.log('📋 处理后的岗位薪资模板列表:', this.templateTreeData)
        console.log('📊 岗位薪资模板总数:', this.templateTotal)

        this.updateTemplateStats()
      } catch (error) {
        console.error('❌ 获取岗位薪资模板列表失败:', error)
        this.$message.error('获取岗位薪资模板列表失败: ' + error.message)
        this.templateTreeData = []
        this.templateTotal = 0
      } finally {
        this.templateLoading = false
      }
    },

    // 加载岗位选项
    async loadPositionOptions() {
      try {
        console.log('🔄 开始加载岗位选项...')
        const response = await getPositionOptions()
        console.log('✅ 岗位选项响应:', response)

        if (response && response.data) {
          // 适配实际API：data可能是数组或者包含positions的对象
          const positions = Array.isArray(response.data) ? response.data : (response.data.positions || [])

          this.positionOptions = positions.map(pos => {
            // 优先使用岗位自带的部门信息，否则通过ID获取
            const departmentName = pos.department
              ? pos.department.dept_name
              : this.getDepartmentNameById(pos.department_id) || '未分配部门'

            return {
              label: `${pos.position_name || pos.name} (${departmentName})`,
              value: pos.id,
              name: pos.position_name || pos.name,
              code: pos.position_code || pos.code,
              department: departmentName,
              department_id: pos.department_id
            }
          })

          console.log('📋 处理后的岗位选项:', this.positionOptions)
        } else {
          console.warn('⚠️ 岗位数据格式异常:', response)
          this.positionOptions = []
        }
      } catch (error) {
        console.error('❌ 获取岗位列表失败:', error)
        this.$message.error('获取岗位列表失败，请刷新页面重试')
        this.positionOptions = []
      }
    },

    // 加载薪资项目类型选项
    async loadSalaryItemTypeOptions() {
      try {
        // 使用分页参数获取薪资项目类型列表
        const response = await getSalaryItemTypeList({
          page: 1,
          limit: 50 // 获取更多数据，确保包含所有类型
        })
        console.log('✅ 薪资项目类型列表响应:', response)

        if (response && response.data) {
          // 新的API响应结构，数据直接在data字段中
          const itemTypes = response.data || []
          this.salaryItemTypeOptions = itemTypes.map(item => ({
            label: `${item.item_name} (${item.type})`, // 显示名称和类型
            value: item.id,
            type: item.type,
            itemKey: item.item_key,
            description: item.description
          }))
        }

        console.log('📋 薪资项目类型选项:', this.salaryItemTypeOptions)
      } catch (error) {
        console.error('❌ 获取薪资项目类型列表失败:', error)
        this.salaryItemTypeOptions = []
      }
    },

    // 加载部门选项
    async loadDepartmentOptions() {
      try {
        console.log('🔄 开始加载部门选项...')
        const response = await getDepartmentTree({})
        console.log('✅ 部门树响应:', response)

        if (response && response.data) {
          const departments = response.data || []
          this.departmentOptions = this.flattenDepartmentTree(departments)
        }

        console.log('📋 部门选项:', this.departmentOptions)
      } catch (error) {
        console.error('❌ 获取部门选项失败:', error)
        this.$message.error('获取部门列表失败，请刷新页面重试')
        // 如果API调用失败，使用默认的部门选项
        this.departmentOptions = [
          { label: '技术部', value: 1 },
          { label: '销售部', value: 2 },
          { label: '人事部', value: 3 },
          { label: '财务部', value: 4 }
        ]
      }
    },

    // 将部门树结构扁平化为选项列表
    flattenDepartmentTree(departments, prefix = '') {
      const options = []

      departments.forEach(dept => {
        const deptName = dept.dept_name || dept.name
        const displayName = prefix ? `${prefix} - ${deptName}` : deptName

        options.push({
          label: displayName,
          value: dept.id // 使用部门ID作为值
        })

        // 递归处理子部门
        if (dept.children && dept.children.length > 0) {
          const childOptions = this.flattenDepartmentTree(dept.children, displayName)
          options.push(...childOptions)
        }
      })

      return options
    },

    // 筛选操作
    handlePositionFilter() {
      this.positionQuery.page = 1
      this.getPositionList()
    },

    handleTemplateFilter() {
      this.templateQuery.page = 1
      this.getTemplateList()
    },

    handleTabClick(tab) {
      if (tab.paneName === 'templates') {
        this.getTemplateList()
      } else if (tab.paneName === 'positions') {
        this.getPositionList()
      }
    },

    // 岗位行点击
    handlePositionRowClick(row) {
      this.selectedPosition = row
      this.activeTab = 'templates'
      this.templateQuery.position_id = row.id
      this.getTemplateList()
    },

    // 查看岗位薪资模板
    viewPositionTemplates(position) {
      this.selectedPosition = position
      this.activeTab = 'templates'
      this.templateQuery.position_id = position.id
      this.getTemplateList()
    },

    // 为岗位创建薪资模板
    async createTemplateForPosition(position) {
      console.log('🔄 为岗位创建薪资模板:', position)

      // 确保岗位选项已加载
      if (this.positionOptions.length === 0) {
        await this.loadPositionOptions()
      }

      this.temp = {
        id: undefined,
        position_id: position.id || position.position_id,
        item_id: undefined,
        default_value: 0,
        sub_default_value: 0
      }

      console.log('🔄 设置的temp数据:', this.temp)

      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.isPositionPreset = true

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        console.log('🔄 当前选中的岗位ID:', this.temp.position_id)
      })
    },

    // 创建岗位
    handleCreatePosition() {
      this.resetPositionTemp()
      this.positionDialogStatus = 'create'
      this.positionDialogTitle = '新增岗位'
      this.positionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['positionForm'].clearValidate()
      })
    },

    // 编辑岗位
    handleUpdatePosition(row) {
      // 映射API返回的数据结构到表单结构
      this.positionTemp = {
        id: row.id,
        code: row.position_code || row.code,
        name: row.position_name || row.name,
        department_id: row.department_id,
        min_salary: row.min_salary || 0,
        max_salary: row.max_salary || 0,
        base_position_salary: row.base_position_salary || 0,
        description: row.description,
        requirements: row.requirements,
        status: row.status
      }
      this.positionDialogStatus = 'update'
      this.positionDialogTitle = '编辑岗位'
      this.positionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['positionForm'].clearValidate()
      })
    },

    // 创建薪资模板
    async handleCreateTemplate() {
      console.log('🔄 打开新增岗位薪资模板对话框')

      if (this.positionOptions.length === 0) {
        console.log('🔄 岗位选项为空，重新加载...')
        await this.loadPositionOptions()
      }

      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.isPositionPreset = false

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        console.log('🔄 对话框已打开，岗位选项数量:', this.positionOptions.length)
      })
    },

    // 编辑薪资模板
    async handleUpdateTemplate(row) {
      console.log('🔄 准备编辑岗位薪资模板:', row)

      if (this.positionOptions.length === 0) {
        console.log('🔄 岗位选项为空，重新加载...')
        await this.loadPositionOptions()
      }

      if (row.type === 'template') {
        this.temp = {
          id: row.id.replace('template-', ''),
          position_id: parseInt(row.position_id),
          item_id: parseInt(row.item_id),
          default_value: parseFloat(row.defaultValue),
          sub_default_value: parseFloat(row.subDefaultValue)
        }
      } else {
        this.temp = {
          id: row.id,
          position_id: parseInt(row.position_id),
          item_id: parseInt(row.item_id),
          default_value: parseFloat(row.default_value),
          sub_default_value: parseFloat(row.sub_default_value)
        }
      }

      console.log('🔄 设置的编辑数据:', this.temp)

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.isPositionPreset = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        console.log('🔄 当前选中的岗位ID:', this.temp.position_id)
      })
    },

    // 查看薪资模板详情
    handleViewTemplate(row) {
      if (row.type === 'template') {
        this.viewData = {
          id: row.id.replace('template-', ''),
          position_id: row.position_id,
          positionName: this.getPositionName(row.position_id),
          positionCode: this.getPositionCode(row.position_id),
          item_id: row.item_id,
          itemTypeName: row.itemName,
          default_value: row.defaultValue,
          sub_default_value: row.subDefaultValue,
          created_at: this.formatDate(row.createdAt),
          updated_at: this.formatDate(row.updatedAt)
        }
      } else {
        this.viewData = {
          id: row.id,
          position_id: row.position_id,
          positionName: row.position ? row.position.name : this.getPositionName(row.position_id),
          positionCode: row.position ? row.position.code : this.getPositionCode(row.position_id),
          item_id: row.item_id,
          itemTypeName: row.item ? row.item.item_name : this.getItemTypeName(row.item_id),
          default_value: row.default_value,
          sub_default_value: row.sub_default_value,
          created_at: this.formatDate(row.created_at),
          updated_at: this.formatDate(row.updated_at)
        }
      }

      this.dialogViewVisible = true
    },

    // 删除薪资模板
    handleDeleteTemplate(row) {
      this.$confirm('确定要删除该岗位的薪资模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const templateId = row.type === 'template' ? row.id.replace('template-', '') : row.id
          const response = await deletePositionSalaryTemplate(templateId)
          console.log('✅ 删除岗位薪资模板响应:', response)
          this.$notify({
            title: '成功',
            message: response.message || '岗位薪资模板删除成功',
            type: 'success',
            duration: 2000
          })
          this.getTemplateList()
        } catch (error) {
          console.error('删除岗位薪资模板失败:', error)
          this.$message.error('删除岗位薪资模板失败: ' + error.message)
        }
      })
    },

    // 创建岗位数据
    createPositionData() {
      this.$refs['positionForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 构建符合后端API的数据结构
            const data = {
              position_code: this.positionTemp.code,
              position_name: this.positionTemp.name,
              department_id: this.positionTemp.department_id,
              min_salary: this.positionTemp.min_salary || 0,
              max_salary: this.positionTemp.max_salary || 0,
              base_position_salary: this.positionTemp.base_position_salary || 0,
              description: this.positionTemp.description,
              requirements: this.positionTemp.requirements,
              status: this.positionTemp.status
            }

            const response = await createPosition(data)
            console.log('✅ 创建岗位响应:', response)
            this.positionDialogVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '岗位创建成功',
              type: 'success',
              duration: 2000
            })
            this.getPositionList()
            await this.loadPositionOptions() // 重新加载岗位选项
          } catch (error) {
            console.error('创建岗位失败:', error)
            this.$message.error('创建岗位失败: ' + error.message)
          }
        }
      })
    },

    // 更新岗位数据
    updatePositionData() {
      this.$refs['positionForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 构建符合后端API的数据结构
            const data = {
              position_code: this.positionTemp.code,
              position_name: this.positionTemp.name,
              department_id: this.positionTemp.department_id,
              min_salary: this.positionTemp.min_salary || 0,
              max_salary: this.positionTemp.max_salary || 0,
              base_position_salary: this.positionTemp.base_position_salary || 0,
              description: this.positionTemp.description,
              requirements: this.positionTemp.requirements,
              status: this.positionTemp.status
            }

            const response = await updatePosition(this.positionTemp.id, data)
            console.log('✅ 更新岗位响应:', response)
            this.positionDialogVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '岗位更新成功',
              type: 'success',
              duration: 2000
            })
            this.getPositionList()
            await this.loadPositionOptions()
          } catch (error) {
            console.error('更新岗位失败:', error)
            this.$message.error('更新岗位失败: ' + error.message)
          }
        }
      })
    },

    // 验证和转换数据类型
    validateAndConvertData(data) {
      return {
        position_id: parseInt(data.position_id),
        item_id: parseInt(data.item_id),
        default_value: parseFloat(data.default_value) || 0,
        sub_default_value: parseFloat(data.sub_default_value) || 0
      }
    },

    // 创建薪资模板数据
    createData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            const templateData = this.validateAndConvertData(this.temp)

            if (isNaN(templateData.position_id) || isNaN(templateData.item_id)) {
              this.$message.error('请选择有效的岗位和薪资项目类型')
              return
            }

            console.log('🔄 准备创建岗位薪资模板:', templateData)
            const response = await createPositionSalaryTemplate(templateData)
            console.log('✅ 创建岗位薪资模板响应:', response)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '岗位薪资模板创建成功',
              type: 'success',
              duration: 2000
            })

            this.getTemplateList()
          } catch (error) {
            console.error('创建岗位薪资模板失败:', error)
            this.$message.error('创建岗位薪资模板失败: ' + error.message)
          }
        }
      })
    },

    // 更新薪资模板数据
    updateData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            const templateData = {
              default_value: parseFloat(this.temp.default_value) || 0,
              sub_default_value: parseFloat(this.temp.sub_default_value) || 0
            }

            if (!this.temp.id) {
              this.$message.error('缺少薪资模板ID')
              return
            }

            console.log('🔄 准备更新岗位薪资模板:', templateData)
            console.log('🔄 使用模板ID:', this.temp.id)
            const response = await updatePositionSalaryTemplate(this.temp.id, templateData)
            console.log('✅ 更新岗位薪资模板响应:', response)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '岗位薪资模板更新成功',
              type: 'success',
              duration: 2000
            })

            this.getTemplateList()
          } catch (error) {
            console.error('更新岗位薪资模板失败:', error)
            this.$message.error('更新岗位薪资模板失败: ' + error.message)
          }
        }
      })
    },

    // 辅助方法
    resetPositionTemp() {
      this.positionTemp = {
        id: undefined,
        code: '',
        name: '',
        department_id: '', // 修改为 department_id
        min_salary: 0,
        max_salary: 0,
        base_position_salary: 0,
        description: '',
        requirements: '',
        status: 'active'
      }
    },

    resetTemp() {
      this.temp = {
        id: undefined,
        position_id: undefined,
        item_id: undefined,
        default_value: 0,
        sub_default_value: 0
      }
    },

    getPositionName(positionId) {
      const position = this.positionOptions.find(pos => pos.value === positionId)
      return position ? position.name : ''
    },

    getPositionCode(positionId) {
      const position = this.positionOptions.find(pos => pos.value === positionId)
      return position ? position.code : ''
    },

    getItemTypeName(itemId) {
      const itemType = this.salaryItemTypeOptions.find(item => item.value === itemId)
      return itemType ? itemType.label : ''
    },

    getItemTypeTagColor(itemType) {
      if (!itemType) return 'default'

      const colorMap = {
        '固定薪资类': 'success',
        '固定薪资': 'success',
        '浮动薪资': 'warning',
        '扣除项目': 'danger',
        '奖金': 'primary',
        'other': 'info'
      }
      return colorMap[itemType] || 'default'
    },

    // 根据部门ID获取部门名称
    getDepartmentNameById(departmentId) {
      if (!departmentId) return ''

      const department = this.departmentOptions.find(dept => dept.value === departmentId)
      return department ? department.label : `部门${departmentId}`
    },

    formatCurrency(value) {
      if (!value) return '0.00'
      return Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    },

    formatThaiCurrency(value) {
      if (!value) return '฿0.00'
      return '฿' + Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    },

    // 部门选择器相关方法
    openDepartmentSelector() {
      this.departmentSelectorVisible = true
    },

    handleDepartmentSelected(selectedDepartment) {
      if (selectedDepartment) {
        this.positionTemp.department_id = selectedDepartment.id
        this.$message.success(`已选择部门：${selectedDepartment.dept_name}`)
      }
    },

    async loadDepartmentTreeData() {
      try {
        // 使用您提供的部门树形数据结构
        const mockTreeData = [
          {
            'id': 4,
            'dept_code': '001',
            'dept_name': 'အရောင်းဌာန ',
            'parent_id': null,
            'description': '',
            'created_at': '2025-07-12T15:48:36.321889+08:00',
            'updated_at': '2025-07-12T15:48:36.321889+08:00',
            'children': [
              {
                'id': 5,
                'dept_code': '0001',
                'dept_name': 'လက်လီအရောင်းအဖွဲ့',
                'parent_id': 4,
                'description': '',
                'created_at': '2025-07-12T15:59:39.35837+08:00',
                'updated_at': '2025-07-12T17:10:20.850312+08:00'
              },
              {
                'id': 6,
                'dept_code': '0002',
                'dept_name': 'လက်ကားအရောင်းအဖွဲ့',
                'parent_id': 4,
                'description': '',
                'created_at': '2025-07-12T16:16:53.971102+08:00',
                'updated_at': '2025-07-12T17:10:27.620516+08:00'
              }
            ]
          },
          {
            'id': 7,
            'dept_code': '002',
            'dept_name': 'အဝယ်ဌာန',
            'parent_id': null,
            'description': '',
            'created_at': '2025-07-12T16:17:35.766482+08:00',
            'updated_at': '2025-07-12T16:17:35.766482+08:00'
          },
          {
            'id': 8,
            'dept_code': '003',
            'dept_name': 'ငွေစာရင်းဌာန',
            'parent_id': null,
            'description': '',
            'created_at': '2025-07-12T16:18:41.243112+08:00',
            'updated_at': '2025-07-12T16:18:41.243112+08:00',
            'children': [
              {
                'id': 11,
                'dept_code': '0031',
                'dept_name': 'အရောင်းစာရင်းအဖွဲ့(N)',
                'parent_id': 8,
                'description': '',
                'created_at': '2025-07-12T17:12:43.664724+08:00',
                'updated_at': '2025-07-12T17:12:43.664724+08:00'
              },
              {
                'id': 12,
                'dept_code': '0032',
                'dept_name': 'အဝယ်စာရင်းအဖွဲ့(S)',
                'parent_id': 8,
                'description': '',
                'created_at': '2025-07-12T17:13:14.621884+08:00',
                'updated_at': '2025-07-12T17:13:14.621884+08:00'
              }
            ]
          },
          {
            'id': 9,
            'dept_code': '004',
            'dept_name': 'စတိုးဌာန(G)/门店部',
            'parent_id': null,
            'description': '',
            'created_at': '2025-07-12T16:57:24.520649+08:00',
            'updated_at': '2025-07-12T16:57:24.520649+08:00'
          },
          {
            'id': 10,
            'dept_code': '005',
            'dept_name': 'HRဌာန',
            'parent_id': null,
            'description': '',
            'created_at': '2025-07-12T17:06:07.825975+08:00',
            'updated_at': '2025-07-12T17:06:07.825975+08:00'
          }
        ]

        this.departmentTreeData = mockTreeData
        console.log('✅ 部门树形数据加载成功:', this.departmentTreeData)
      } catch (error) {
        console.error('获取部门树形数据失败:', error)
        this.$message.error('获取部门树形数据失败')
      }
    },

    formatMyanmarCurrency(value) {
      if (!value) return '0.00K'
      return Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,') + 'K'
    },

    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    updatePositionStats() {
      this.positionStats.total = this.positionTotal
      this.positionStats.active = this.positionList.filter(pos => pos.status === 'active').length
      this.positionStats.withTemplate = this.positionList.filter(pos => pos.id).length

      // 计算部门数量
      const departmentSet = new Set()
      this.positionList.forEach(pos => {
        if (pos.department_id) {
          departmentSet.add(pos.department_id)
        }
      })
      this.positionStats.departments = departmentSet.size
    },

    updateTemplateStats() {
      this.templateStats.total = this.templateTotal

      // 计算涉及的岗位数量
      const positionSet = new Set()
      this.templateTreeData.forEach(item => {
        if (item.type === 'position') {
          positionSet.add(item.id)
        } else if (item.position_id) {
          positionSet.add(item.position_id)
        }
      })
      this.templateStats.positions = positionSet.size

      this.templateStats.itemTypes = this.salaryItemTypeOptions.length
      this.templateStats.avgPerPosition = this.templateStats.positions > 0
        ? (this.templateStats.total / this.templateStats.positions).toFixed(1) : 0
    },

    processTemplateData(templates) {
      // 按岗位分组构建树状结构
      const positionMap = new Map()

      templates.forEach(template => {
        const positionId = template.position_id
        const position = template.position

        if (!positionMap.has(positionId)) {
          // 优先使用岗位自带的部门信息
          const departmentName = position && position.department
            ? position.department.dept_name
            : (position
              ? this.getDepartmentNameById(position.department_id) || position.department
              : this.getDepartmentNameById(template.department_id) || '')

          positionMap.set(positionId, {
            id: `position-${positionId}`,
            type: 'position',
            position_id: positionId,
            name: position ? (position.position_name || position.name) : `岗位 ${positionId}`,
            code: position ? (position.position_code || position.code) : '',
            department: departmentName,
            department_id: position ? position.department_id : template.department_id,
            description: position ? position.description : '',
            totalValue: 0,
            totalSubValue: 0,
            children: []
          })
        }

        const positionNode = positionMap.get(positionId)
        positionNode.children.push({
          id: `template-${template.id}`,
          type: 'template',
          position_id: positionId,
          item_id: template.item_id,
          itemName: template.item ? template.item.item_name : `项目 ${template.item_id}`,
          itemType: template.item ? template.item.type : 'other',
          defaultValue: template.default_value,
          subDefaultValue: template.sub_default_value,
          createdAt: template.created_at,
          updatedAt: template.updated_at
        })

        // 计算岗位的总薪资
        positionNode.totalValue += template.default_value || 0
        positionNode.totalSubValue += template.sub_default_value || 0
      })

      return Array.from(positionMap.values())
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
