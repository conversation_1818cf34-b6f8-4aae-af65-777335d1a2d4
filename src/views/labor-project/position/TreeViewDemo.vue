<template>
  <div class="tree-view-demo">
    <div class="demo-header">
      <h1>部门树形结构演示</h1>
      <p>此页面展示了基于您提供的部门数据的TreeView组件功能</p>
    </div>

    <el-row :gutter="20">
      <!-- 基础树形视图 -->
      <el-col :span="8">
        <el-card>
          <div slot="header" class="card-header">
            <span>基础树形视图</span>
            <el-button size="small" @click="refreshBasicTree">刷新</el-button>
          </div>
          <department-tree-view
            ref="basicTree"
            :data="treeData"
            :default-expand-all="true"
            @node-click="handleBasicNodeClick"
            @change="handleBasicTreeChange"
          />
        </el-card>
      </el-col>

      <!-- 带复选框的树形视图 -->
      <el-col :span="8">
        <el-card>
          <div slot="header" class="card-header">
            <span>多选树形视图</span>
            <el-button size="small" @click="clearMultipleSelection">清空选择</el-button>
          </div>
          <department-tree-view
            ref="multipleTree"
            :data="treeData"
            :show-checkbox="true"
            :multiple="true"
            :default-expand-all="true"
            @change="handleMultipleTreeChange"
          />
        </el-card>
      </el-col>

      <!-- 选择器对话框演示 -->
      <el-col :span="8">
        <el-card>
          <div slot="header" class="card-header">
            <span>选择器对话框</span>
          </div>
          <div class="selector-demo">
            <el-button type="primary" @click="openSingleSelector">单选对话框</el-button>
            <el-button type="success" @click="openMultipleSelector">多选对话框</el-button>

            <div class="selection-result">
              <h4>单选结果：</h4>
              <el-tag v-if="singleSelection" type="primary">
                {{ singleSelection.dept_name }} ({{ singleSelection.dept_code }})
              </el-tag>
              <span v-else class="no-selection">未选择</span>

              <h4>多选结果：</h4>
              <div v-if="multipleSelection.length > 0">
                <el-tag
                  v-for="dept in multipleSelection"
                  :key="dept.id"
                  type="success"
                  style="margin-right: 8px; margin-bottom: 8px;"
                >
                  {{ dept.dept_name }} ({{ dept.dept_code }})
                </el-tag>
              </div>
              <span v-else class="no-selection">未选择</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据结构展示 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="card-header">
            <span>数据结构展示</span>
            <el-button size="small" @click="toggleDataView">
              {{ showDataView ? '隐藏' : '显示' }}数据结构
            </el-button>
          </div>
          <div v-if="showDataView" class="data-structure">
            <pre>{{ JSON.stringify(treeData, null, 2) }}</pre>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作日志 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="card-header">
            <span>操作日志</span>
            <el-button size="small" @click="clearLogs">清空日志</el-button>
          </div>
          <div class="operation-logs">
            <div
              v-for="(log, index) in operationLogs"
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 选择器对话框 -->
    <department-selector
      :visible.sync="selectorVisible"
      :data="treeData"
      :multiple="selectorMultiple"
      :value="selectorValue"
      @confirm="handleSelectorConfirm"
    />
  </div>
</template>

<script>
import DepartmentTreeView from '@/components/DepartmentTreeView/index.vue'
import DepartmentSelector from '@/components/DepartmentTreeView/DepartmentSelector.vue'

export default {
  name: 'TreeViewDemo',
  components: {
    DepartmentTreeView,
    DepartmentSelector
  },
  data() {
    return {
      // 树形数据
      treeData: [
        {
          'id': 4,
          'dept_code': '001',
          'dept_name': 'အရောင်းဌာန ',
          'parent_id': null,
          'description': '',
          'created_at': '2025-07-12T15:48:36.321889+08:00',
          'updated_at': '2025-07-12T15:48:36.321889+08:00',
          'children': [
            {
              'id': 5,
              'dept_code': '0001',
              'dept_name': 'လက်လီအရောင်းအဖွဲ့',
              'parent_id': 4,
              'description': '',
              'created_at': '2025-07-12T15:59:39.35837+08:00',
              'updated_at': '2025-07-12T17:10:20.850312+08:00'
            },
            {
              'id': 6,
              'dept_code': '0002',
              'dept_name': 'လက်ကားအရောင်းအဖွဲ့',
              'parent_id': 4,
              'description': '',
              'created_at': '2025-07-12T16:16:53.971102+08:00',
              'updated_at': '2025-07-12T17:10:27.620516+08:00'
            }
          ]
        },
        {
          'id': 7,
          'dept_code': '002',
          'dept_name': 'အဝယ်ဌာန',
          'parent_id': null,
          'description': '',
          'created_at': '2025-07-12T16:17:35.766482+08:00',
          'updated_at': '2025-07-12T16:17:35.766482+08:00'
        },
        {
          'id': 8,
          'dept_code': '003',
          'dept_name': 'ငွေစာရင်းဌာန',
          'parent_id': null,
          'description': '',
          'created_at': '2025-07-12T16:18:41.243112+08:00',
          'updated_at': '2025-07-12T16:18:41.243112+08:00',
          'children': [
            {
              'id': 11,
              'dept_code': '0031',
              'dept_name': 'အရောင်းစာရင်းအဖွဲ့(N)',
              'parent_id': 8,
              'description': '',
              'created_at': '2025-07-12T17:12:43.664724+08:00',
              'updated_at': '2025-07-12T17:12:43.664724+08:00'
            },
            {
              'id': 12,
              'dept_code': '0032',
              'dept_name': 'အဝယ်စာရင်းအဖွဲ့(S)',
              'parent_id': 8,
              'description': '',
              'created_at': '2025-07-12T17:13:14.621884+08:00',
              'updated_at': '2025-07-12T17:13:14.621884+08:00'
            }
          ]
        },
        {
          'id': 9,
          'dept_code': '004',
          'dept_name': 'စတိုးဌာန(G)/门店部',
          'parent_id': null,
          'description': '',
          'created_at': '2025-07-12T16:57:24.520649+08:00',
          'updated_at': '2025-07-12T16:57:24.520649+08:00'
        },
        {
          'id': 10,
          'dept_code': '005',
          'dept_name': 'HRဌာန',
          'parent_id': null,
          'description': '',
          'created_at': '2025-07-12T17:06:07.825975+08:00',
          'updated_at': '2025-07-12T17:06:07.825975+08:00'
        }
      ],

      // 选择结果
      singleSelection: null,
      multipleSelection: [],

      // 选择器对话框
      selectorVisible: false,
      selectorMultiple: false,
      selectorValue: null,

      // 数据视图
      showDataView: false,

      // 操作日志
      operationLogs: []
    }
  },
  mounted() {
    this.addLog('success', '页面加载完成，TreeView组件已初始化')
  },
  methods: {
    // 基础树形视图事件
    handleBasicNodeClick(data, node) {
      this.addLog('info', `基础树形视图：点击了节点 "${data.dept_name}" (${data.dept_code})`)
    },

    handleBasicTreeChange(selection) {
      if (selection) {
        this.addLog('success', `基础树形视图：选中了 "${selection.dept_name}" (${selection.dept_code})`)
      }
    },

    // 多选树形视图事件
    handleMultipleTreeChange(selections) {
      const count = selections.length
      this.addLog('success', `多选树形视图：选中了 ${count} 个部门`)
      if (count > 0) {
        const names = selections.map(s => `"${s.dept_name}"`).join(', ')
        this.addLog('info', `选中的部门：${names}`)
      }
    },

    // 选择器对话框
    openSingleSelector() {
      this.selectorMultiple = false
      this.selectorValue = this.singleSelection
      this.selectorVisible = true
      this.addLog('info', '打开单选对话框')
    },

    openMultipleSelector() {
      this.selectorMultiple = true
      this.selectorValue = this.multipleSelection
      this.selectorVisible = true
      this.addLog('info', '打开多选对话框')
    },

    handleSelectorConfirm(selection) {
      if (this.selectorMultiple) {
        this.multipleSelection = selection || []
        this.addLog('success', `多选对话框：选择了 ${this.multipleSelection.length} 个部门`)
      } else {
        this.singleSelection = selection
        this.addLog('success', `单选对话框：选择了 "${selection ? selection.dept_name : '无'}"`)
      }
    },

    // 工具方法
    refreshBasicTree() {
      this.$refs.basicTree.clearSelection()
      this.addLog('info', '刷新基础树形视图')
    },

    clearMultipleSelection() {
      if (this.$refs.multipleTree) {
        this.$refs.multipleTree.clearSelection()
      }
      this.addLog('info', '清空多选树形视图选择')
    },

    toggleDataView() {
      this.showDataView = !this.showDataView
      this.addLog('info', `${this.showDataView ? '显示' : '隐藏'}数据结构`)
    },

    clearLogs() {
      this.operationLogs = []
    },

    addLog(type, message) {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.operationLogs.unshift({
        type,
        message,
        time
      })

      // 保持日志数量在合理范围内
      if (this.operationLogs.length > 100) {
        this.operationLogs = this.operationLogs.slice(0, 100)
      }
    }
  }
}
</script>

<style scoped>
.tree-view-demo {
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selector-demo {
  text-align: center;
}

.selector-demo .el-button {
  margin: 0 10px 20px 0;
}

.selection-result {
  text-align: left;
  margin-top: 20px;
}

.selection-result h4 {
  margin: 15px 0 10px 0;
  color: #409eff;
}

.no-selection {
  color: #909399;
  font-style: italic;
}

.data-structure {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.data-structure pre {
  margin: 0;
  font-size: 12px;
  color: #333;
}

.operation-logs {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  width: 80px;
  font-size: 12px;
  color: #909399;
  margin-right: 10px;
}

.log-message {
  flex: 1;
  font-size: 13px;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.info .log-message {
  color: #409eff;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.log-item.error .log-message {
  color: #f56c6c;
}

/* 滚动条样式 */
.data-structure::-webkit-scrollbar,
.operation-logs::-webkit-scrollbar {
  width: 6px;
}

.data-structure::-webkit-scrollbar-track,
.operation-logs::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.data-structure::-webkit-scrollbar-thumb,
.operation-logs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.data-structure::-webkit-scrollbar-thumb:hover,
.operation-logs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
