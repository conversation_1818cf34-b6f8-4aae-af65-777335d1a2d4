.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.box-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}

.default-value {
  font-weight: bold;
  color: #E6A23C;
}

.salary-amount {
  font-weight: bold;
  color: #E6A23C;
}

.total-value {
  font-weight: bold;
  color: #409EFF;
}

.sub-default-value {
  font-weight: bold;
  color: #F56C6C;
}

.total-sub-value {
  font-weight: bold;
  color: #F56C6C;
}

// 货币说明面板样式
.currency-info-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .el-alert__icon {
    color: #fff;
  }
  
  .el-alert__title {
    color: #fff;
    font-weight: bold;
  }
  
  .el-alert__description {
    color: #fff;
  }
}

// 货币输入框样式
.thai-currency-input {
  .el-input-group__prepend {
    background-color: #FEF7E8;
    border-color: #E6A23C;
    color: #E6A23C;
    font-weight: bold;
    font-size: 16px;
  }
  
  .el-input__inner {
    border-color: #E6A23C;
    
    &:focus {
      border-color: #E6A23C;
      box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
    }
  }
}

.myanmar-currency-input {
  .el-input-group__append {
    background-color: #FEF0F0;
    border-color: #F56C6C;
    color: #F56C6C;
    font-weight: bold;
    font-size: 16px;
  }
  
  .el-input__inner {
    border-color: #F56C6C;
    
    &:focus {
      border-color: #F56C6C;
      box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
    }
  }
}

// 货币样式
.thai-currency {
  color: #E6A23C;
  font-weight: bold;
}

.myanmar-currency {
  color: #F56C6C;
  font-weight: bold;
}

// 工具提示样式
.el-tooltip__popper {
  max-width: 200px;
  font-size: 12px;
  
  &.is-dark {
    background-color: #303133;
    border-color: #303133;
  }
}

// 货币显示样式
.currency-display {
  cursor: help;
  transition: all 0.3s;
  
  &:hover {
    transform: scale(1.05);
  }
}

.position-row {
  display: flex;
  align-items: center;
  padding: 5px 0;

  .el-icon-office-building {
    margin-right: 8px;
    color: #409EFF;
    font-size: 16px;
  }

  .position-name {
    font-weight: bold;
    color: #303133;
    margin-right: 10px;
  }
}

.template-row {
  display: flex;
  align-items: center;
  padding: 5px 0;
  margin-left: 20px;

  .el-icon-document {
    margin-right: 8px;
    color: #67C23A;
    font-size: 14px;
  }

  span {
    color: #606266;
  }
}

// 表格样式优化
.el-table {
  border-radius: 8px;
  overflow: hidden;

  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }

  td {
    border-bottom: 1px solid #EBEEF5;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}

// 分页样式
.el-pagination {
  text-align: center;
  margin-top: 20px;
}

// 对话框样式
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #EBEEF5;
}

.el-dialog__body {
  padding: 30px 20px;
}

.readonly-tip {
  color: #E6A23C;
  font-weight: 500;
  font-style: italic;
}

// 标签页样式
.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav-wrap {
    padding: 0 10px;
  }

  .el-tabs__item {
    font-weight: 600;
    color: #909399;

    &.is-active {
      color: #409EFF;
    }
  }
}

// 按钮样式优化
.el-button {
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 600;
    color: #606266;
  }

  .el-input, .el-select {
    .el-input__inner {
      border-radius: 4px;
      border: 1px solid #DCDFE6;
      transition: border-color 0.3s;

      &:focus {
        border-color: #409EFF;
      }
    }
  }
}

// 卡片统计样式
.el-card {
  .el-card__body {
    padding: 20px;
  }
}

// 消息提示样式
.el-message {
  border-radius: 4px;
}

// 确认框样式
.el-message-box {
  border-radius: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .filter-container {
    .filter-item {
      width: 100%;
      margin-bottom: 10px;
    }
  }

  .el-table {
    font-size: 12px;
  }

  .position-row, .template-row {
    font-size: 12px;
  }
} 