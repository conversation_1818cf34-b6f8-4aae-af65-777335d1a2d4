/**
 * 岗位管理功能测试
 */

import {
  getPositionList,
  createPosition,
  updatePosition,
  deletePosition,
  getPositionStats,
  getPositionSalaryTemplateList,
  createPositionSalaryTemplate,
  updatePositionSalaryTemplate,
  deletePositionSalaryTemplate
} from '@/api/labor-project/positions'

// 测试岗位管理API
export async function testPositionAPI() {
  console.log('🧪 开始测试岗位管理API...')

  try {
    // 1. 测试获取岗位列表
    console.log('📋 测试获取岗位列表...')
    const positionList = await getPositionList({ page: 1, limit: 10 })
    console.log('✅ 岗位列表获取成功:', positionList)

    // 2. 测试创建岗位
    console.log('➕ 测试创建岗位...')
    const newPosition = {
      code: 'TEST_001',
      name: '测试岗位',
      department: '技术部',
      description: '这是一个测试岗位',
      status: 'active'
    }
    const createResult = await createPosition(newPosition)
    console.log('✅ 岗位创建成功:', createResult)

    // 3. 测试获取岗位统计
    console.log('📊 测试获取岗位统计...')
    const stats = await getPositionStats()
    console.log('✅ 岗位统计获取成功:', stats)

    console.log('🎉 岗位管理API测试完成!')
  } catch (error) {
    console.error('❌ 岗位管理API测试失败:', error)
  }
}

// 测试岗位薪资模板API
export async function testPositionSalaryTemplateAPI() {
  console.log('🧪 开始测试岗位薪资模板API...')

  try {
    // 1. 测试获取薪资模板列表
    console.log('📋 测试获取薪资模板列表...')
    const templateList = await getPositionSalaryTemplateList({ page: 1, limit: 10 })
    console.log('✅ 薪资模板列表获取成功:', templateList)

    // 2. 测试创建薪资模板
    console.log('➕ 测试创建薪资模板...')
    const newTemplate = {
      position_id: 1,
      item_id: 1,
      default_value: 15000.00,
      sub_default_value: 2000.00
    }
    const createResult = await createPositionSalaryTemplate(newTemplate)
    console.log('✅ 薪资模板创建成功:', createResult)

    console.log('🎉 岗位薪资模板API测试完成!')
  } catch (error) {
    console.error('❌ 岗位薪资模板API测试失败:', error)
  }
}

// 测试货币格式化函数
export function testCurrencyFormat() {
  console.log('🧪 开始测试货币格式化...')

  // 测试泰币格式化
  const formatThaiCurrency = (value) => {
    if (!value) return '฿0.00'
    return '฿' + Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
  }

  // 测试缅币格式化
  const formatMyanmarCurrency = (value) => {
    if (!value) return '0.00K'
    return Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,') + 'K'
  }

  // 测试数据
  const testValues = [0, 1000, 15000, 123456.78, 1234567.89]

  testValues.forEach(value => {
    console.log(`${value} => 泰币: ${formatThaiCurrency(value)}, 缅币: ${formatMyanmarCurrency(value)}`)
  })

  console.log('🎉 货币格式化测试完成!')
}

// 执行所有测试
export async function runAllTests() {
  console.log('🚀 开始执行所有测试...')

  await testPositionAPI()
  await testPositionSalaryTemplateAPI()
  testCurrencyFormat()

  console.log('🎊 所有测试执行完成!')
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.positionTests = {
    testPositionAPI,
    testPositionSalaryTemplateAPI,
    testCurrencyFormat,
    runAllTests
  }

  console.log('🔧 岗位管理测试工具已加载到 window.positionTests')
  console.log('💡 使用方法：')
  console.log('  - window.positionTests.testPositionAPI() - 测试岗位API')
  console.log('  - window.positionTests.testPositionSalaryTemplateAPI() - 测试薪资模板API')
  console.log('  - window.positionTests.testCurrencyFormat() - 测试货币格式化')
  console.log('  - window.positionTests.runAllTests() - 运行所有测试')
}
