# 岗位管理功能说明

## 📋 功能概述

岗位管理模块提供了完整的岗位信息管理和岗位薪资模板管理功能，支持岗位的创建、编辑、查看、删除以及为岗位设置薪资模板。

## 🎯 主要功能

### 1. 岗位管理
- **岗位列表**：显示所有岗位信息，支持按部门、岗位名称筛选
- **新增岗位**：创建新的岗位，包含岗位编码、名称、部门、描述等信息
- **编辑岗位**：修改现有岗位信息
- **岗位统计**：显示岗位总数、启用岗位、已设薪资模板等统计数据

### 2. 岗位薪资模板管理
- **薪资模板列表**：树状结构显示岗位及其对应的薪资模板
- **新增薪资模板**：为岗位创建薪资项目模板
- **编辑薪资模板**：修改薪资模板的默认值和子默认值
- **模板统计**：显示模板总数、涉及岗位数等统计信息

## 💰 货币支持

### 双货币制度
- **默认值**：使用泰币(THB)，符号为 `฿`
- **子默认值**：使用缅币(MMK)，符号为 `K`

### 货币显示
- 表格中使用不同颜色区分两种货币
- 输入框带有对应的货币符号
- 支持千位分隔符格式化显示

## 🛠️ 使用方法

### 岗位管理操作

#### 1. 查看岗位列表
- 默认显示所有岗位
- 可按部门筛选岗位
- 可按岗位名称搜索

#### 2. 新增岗位
1. 点击"新增岗位"按钮
2. 填写岗位信息：
   - 岗位编码（必填）
   - 岗位名称（必填）
   - 部门（必选）
   - 岗位描述（可选）
   - 状态（启用/停用）
3. 点击"确定"保存

#### 3. 编辑岗位
1. 在岗位列表中点击"编辑"按钮
2. 修改岗位信息
3. 点击"确定"保存

### 薪资模板管理操作

#### 1. 为岗位创建薪资模板
1. 方式一：在岗位列表中点击"创建模板"
2. 方式二：切换到"岗位薪资模板"标签页，点击"新增薪资模板"
3. 填写模板信息：
   - 岗位（预设或选择）
   - 薪资项目类型
   - 默认值（泰币）
   - 子默认值（缅币）
4. 点击"确定"保存

#### 2. 编辑薪资模板
1. 在薪资模板列表中点击"编辑"按钮
2. 修改默认值和子默认值（岗位和薪资项目类型不可修改）
3. 点击"确定"保存

#### 3. 查看薪资模板详情
1. 在薪资模板列表中点击"查看"按钮
2. 查看完整的模板信息

## 📊 统计信息

### 岗位统计
- **岗位总数**：系统中所有岗位数量
- **启用岗位**：状态为启用的岗位数量
- **已设薪资模板**：已配置薪资模板的岗位数量
- **部门数量**：岗位涉及的部门数量

### 薪资模板统计
- **模板总数**：所有薪资模板数量
- **涉及岗位**：配置了薪资模板的岗位数量
- **薪资项目类型**：可用的薪资项目类型数量
- **平均模板数/岗位**：每个岗位平均的薪资模板数量

## 🔧 技术特性

### 前端技术
- **Vue.js**：响应式UI框架
- **Element UI**：组件库
- **Vuex**：状态管理
- **Vue Router**：路由管理

### 后端API
- **RESTful API**：标准化API设计
- **数据验证**：完整的输入验证
- **错误处理**：友好的错误提示
- **分页支持**：大数据量分页加载

### 用户体验
- **响应式设计**：适配不同屏幕尺寸
- **实时验证**：表单实时验证
- **友好提示**：操作成功/失败提示
- **工具提示**：悬停显示详细信息

## 📝 注意事项

1. **岗位编码**：必须唯一，不能重复
2. **薪资模板**：编辑时只能修改金额，不能修改岗位和薪资项目类型
3. **货币精度**：金额支持小数点后两位
4. **删除操作**：删除前会弹出确认提示
5. **数据同步**：操作后会自动刷新相关数据

## 🚀 快速开始

1. 访问岗位管理页面：`/labor-project/position`
2. 首先创建岗位基础信息
3. 为岗位配置薪资模板
4. 查看统计信息了解配置情况

## �� 支持

如有问题请联系技术支持团队。 