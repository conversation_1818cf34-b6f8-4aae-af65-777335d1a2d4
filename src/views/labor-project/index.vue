<template>
  <div class="labor-project-container">
    <div class="header">
      <h1>劳动管理系统</h1>
      <p>Labor Management System</p>
    </div>

    <div class="dashboard-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">
            <div class="card-panel">
              <div class="card-panel-icon-wrapper icon-people">
                <svg-icon icon-class="peoples" class="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">员工总数</div>
                <div class="card-panel-num">{{ dashboardData.totalEmployees }}</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">
            <div class="card-panel">
              <div class="card-panel-icon-wrapper icon-message">
                <svg-icon icon-class="message" class="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">今日出勤</div>
                <div class="card-panel-num">{{ dashboardData.todayAttendance }}</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">
            <div class="card-panel">
              <div class="card-panel-icon-wrapper icon-money">
                <svg-icon icon-class="money" class="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">待处理请假</div>
                <div class="card-panel-num">{{ dashboardData.pendingLeaves }}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/employee')">
            <div class="module-icon">
              <i class="el-icon-user" />
            </div>
            <div class="module-title">员工管理</div>
            <div class="module-desc">Employee Management</div>
            <div class="module-debug" @click.stop="navigateTo('/labor-project/employee-debug')">
              <el-button size="mini" type="text">调试模式</el-button>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/attendance')">
            <div class="module-icon">
              <i class="el-icon-time" />
            </div>
            <div class="module-title">考勤管理</div>
            <div class="module-desc">Attendance Management</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/leave')">
            <div class="module-icon">
              <i class="el-icon-date" />
            </div>
            <div class="module-title">请假管理</div>
            <div class="module-desc">Leave Management</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/salary')">
            <div class="module-icon">
              <i class="el-icon-money" />
            </div>
            <div class="module-title">薪资管理</div>
            <div class="module-desc">Salary Management</div>
            <div class="module-actions">
              <el-button-group>
                <el-button size="mini" type="text" @click.stop="navigateTo('/labor-project/salary')">薪资记录</el-button>
                <el-button size="mini" type="text" @click.stop="navigateTo('/labor-project/salary-item-types')">项目类型</el-button>
              </el-button-group>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/overtime')">
            <div class="module-icon">
              <i class="el-icon-clock" />
            </div>
            <div class="module-title">加班管理</div>
            <div class="module-desc">Overtime Management</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/department')">
            <div class="module-icon">
              <i class="el-icon-office-building" />
            </div>
            <div class="module-title">部门管理</div>
            <div class="module-desc">Department Management</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/reports')">
            <div class="module-icon">
              <i class="el-icon-data-line" />
            </div>
            <div class="module-title">报表统计</div>
            <div class="module-desc">Reports & Statistics</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card module-card" @click.native="navigateTo('/labor-project/settings')">
            <div class="module-icon">
              <i class="el-icon-setting" />
            </div>
            <div class="module-title">系统设置</div>
            <div class="module-desc">System Settings</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LaborProject',
  data() {
    return {
      dashboardData: {
        totalEmployees: 156,
        todayAttendance: 142,
        pendingLeaves: 8
      }
    }
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.labor-project-container {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  h1 {
    color: #303133;
    margin-bottom: 10px;
  }
  p {
    color: #909399;
    font-size: 16px;
  }
}

.card-panel {
  height: 108px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
  border-color: rgba(0, 0, 0, .05);

  &:hover {
    .card-panel-icon-wrapper {
      color: #fff;
    }
    .icon-people {
      background: #40c9c6;
    }
    .icon-message {
      background: #36a3f7;
    }
    .icon-money {
      background: #f4516c;
    }
  }

  .card-panel-icon-wrapper {
    float: left;
    height: 108px;
    width: 108px;
    text-align: center;
    line-height: 108px;
    color: #fff;
    font-size: 48px;
    transition: all 0.38s ease-out;
    border-radius: 6px;
  }

  .icon-people {
    background: #40c9c6;
  }

  .icon-message {
    background: #36a3f7;
  }

  .icon-money {
    background: #f4516c;
  }

  .card-panel-description {
    float: right;
    font-weight: bold;
    margin: 26px 26px 26px 0;

    .card-panel-text {
      line-height: 18px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
      margin-bottom: 12px;
    }

    .card-panel-num {
      font-size: 20px;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.module-card {
  height: 150px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .module-icon {
    font-size: 48px;
    color: #409EFF;
    margin-bottom: 15px;
  }

  .module-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
  }

  .module-desc {
    font-size: 14px;
    color: #909399;
  }
}

.box-card {
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  position: relative;
}

.module-debug {
  position: absolute;
  bottom: 5px;
  right: 5px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.module-card:hover .module-debug {
  opacity: 1;
}

.module-actions {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.module-card:hover .module-actions {
  opacity: 1;
}

.module-actions .el-button-group {
  display: flex;
  gap: 5px;
}

.module-actions .el-button {
  font-size: 12px;
  padding: 2px 8px;
  border: 1px solid #409EFF;
  border-radius: 12px;
  color: #409EFF;
  background: rgba(255, 255, 255, 0.9);
}

.module-actions .el-button:hover {
  background: #409EFF;
  color: white;
}
</style>
