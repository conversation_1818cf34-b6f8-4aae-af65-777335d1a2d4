<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.employeeName" placeholder="申请人姓名" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.leaveType" placeholder="请假类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in leaveTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="审批状态" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        style="width: 300px"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        申请请假
      </el-button>
      <el-button v-waves :loading="downloadLoading" class="filter-item" type="success" icon="el-icon-download" @click="handleDownload">
        导出报表
      </el-button>
    </div>

    <!-- 请假统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>待审批</span>
          </div>
          <div class="text item">
            <span class="number pending">{{ statistics.pendingCount }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>已通过</span>
          </div>
          <div class="text item">
            <span class="number approved">{{ statistics.approvedCount }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>已拒绝</span>
          </div>
          <div class="text item">
            <span class="number rejected">{{ statistics.rejectedCount }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>本月请假</span>
          </div>
          <div class="text item">
            <span class="number total">{{ statistics.monthlyCount }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="申请编号" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.leaveId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employeeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.department }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请假类型" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getLeaveTypeColor(row.leaveType)">
            {{ row.leaveType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.startDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.endDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请假天数" width="100px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.days }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审批人" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.approver || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.applyDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="info" size="mini" @click="handleView(row)">
            查看
          </el-button>
          <el-button v-if="row.status === '待审批'" type="success" size="mini" @click="handleApprove(row)">
            审批
          </el-button>
          <el-button v-if="row.status === '待审批'" type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row,$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 申请请假对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="700px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 90%; margin-left:50px;">
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请人" prop="employeeId">
              <el-select v-model="temp.employeeId" placeholder="请选择申请人" style="width: 100%">
                <el-option v-for="item in employeeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请假类型" prop="leaveType">
              <el-select v-model="temp.leaveType" placeholder="请选择请假类型" style="width: 100%">
                <el-option v-for="item in leaveTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker v-model="temp.startDate" type="date" placeholder="请选择开始时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker v-model="temp.endDate" type="date" placeholder="请选择结束时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="请假天数" prop="days">
          <el-input-number v-model="temp.days" :min="0.5" :max="30" :step="0.5" placeholder="请输入请假天数" />
        </el-form-item>
        <el-form-item label="请假原因" prop="reason">
          <el-input v-model="temp.reason" :autosize="{ minRows: 3, maxRows: 6}" type="textarea" placeholder="请输入请假原因" />
        </el-form-item>
        <el-form-item label="紧急联系人">
          <el-input v-model="temp.emergencyContact" placeholder="请输入紧急联系人" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="temp.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog title="请假审批" :visible.sync="dialogApprovalVisible" width="600px">
      <el-form ref="approvalForm" :model="approvalData" label-position="left" label-width="100px" style="width: 90%; margin-left:50px;">
        <el-form-item label="申请人">
          <span>{{ approvalData.employeeName }}</span>
        </el-form-item>
        <el-form-item label="请假类型">
          <el-tag :type="getLeaveTypeColor(approvalData.leaveType)">
            {{ approvalData.leaveType }}
          </el-tag>
        </el-form-item>
        <el-form-item label="请假时间">
          <span>{{ approvalData.startDate }} 至 {{ approvalData.endDate }}</span>
        </el-form-item>
        <el-form-item label="请假天数">
          <span>{{ approvalData.days }} 天</span>
        </el-form-item>
        <el-form-item label="请假原因">
          <span>{{ approvalData.reason }}</span>
        </el-form-item>
        <el-form-item label="审批结果" prop="approvalResult">
          <el-radio-group v-model="approvalData.approvalResult">
            <el-radio label="通过">通过</el-radio>
            <el-radio label="拒绝">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input v-model="approvalData.approvalComment" :autosize="{ minRows: 3, maxRows: 6}" type="textarea" placeholder="请输入审批意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogApprovalVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="submitApproval">
          提交审批
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看请假详情对话框 -->
    <el-dialog title="请假详情" :visible.sync="dialogViewVisible" width="700px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请编号">{{ viewData.leaveId }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ viewData.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ viewData.department }}</el-descriptions-item>
        <el-descriptions-item label="请假类型">
          <el-tag :type="getLeaveTypeColor(viewData.leaveType)">
            {{ viewData.leaveType }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ viewData.startDate }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ viewData.endDate }}</el-descriptions-item>
        <el-descriptions-item label="请假天数">{{ viewData.days }} 天</el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getStatusType(viewData.status)">
            {{ viewData.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批人">{{ viewData.approver || '-' }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ viewData.applyDate }}</el-descriptions-item>
        <el-descriptions-item label="审批时间">{{ viewData.approvalDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="紧急联系人">{{ viewData.emergencyContact }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ viewData.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="请假原因" :span="2">{{ viewData.reason }}</el-descriptions-item>
        <el-descriptions-item label="审批意见" :span="2">{{ viewData.approvalComment || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'LeaveManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        leaveType: undefined,
        status: undefined,
        dateRange: []
      },
      statistics: {
        pendingCount: 8,
        approvedCount: 45,
        rejectedCount: 3,
        monthlyCount: 56
      },
      leaveTypeOptions: [
        { label: '年假', value: '年假' },
        { label: '病假', value: '病假' },
        { label: '事假', value: '事假' },
        { label: '婚假', value: '婚假' },
        { label: '产假', value: '产假' },
        { label: '陪产假', value: '陪产假' },
        { label: '丧假', value: '丧假' }
      ],
      statusOptions: [
        { label: '待审批', value: '待审批' },
        { label: '已通过', value: '已通过' },
        { label: '已拒绝', value: '已拒绝' }
      ],
      employeeOptions: [
        { label: '张三 (EMP001)', value: 'EMP001' },
        { label: '李四 (EMP002)', value: 'EMP002' },
        { label: '王五 (EMP003)', value: 'EMP003' }
      ],
      temp: {
        employeeId: '',
        leaveType: '',
        startDate: '',
        endDate: '',
        days: 1,
        reason: '',
        emergencyContact: '',
        contactPhone: ''
      },
      approvalData: {
        employeeName: '',
        leaveType: '',
        startDate: '',
        endDate: '',
        days: 0,
        reason: '',
        approvalResult: '通过',
        approvalComment: ''
      },
      dialogFormVisible: false,
      dialogApprovalVisible: false,
      dialogViewVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑请假申请',
        create: '申请请假'
      },
      viewData: {},
      downloadLoading: false,
      rules: {
        employeeId: [{ required: true, message: '申请人是必选项', trigger: 'change' }],
        leaveType: [{ required: true, message: '请假类型是必选项', trigger: 'change' }],
        startDate: [{ required: true, message: '开始时间是必选项', trigger: 'change' }],
        endDate: [{ required: true, message: '结束时间是必选项', trigger: 'change' }],
        days: [{ required: true, message: '请假天数是必填项', trigger: 'blur' }],
        reason: [{ required: true, message: '请假原因是必填项', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      // 模拟API调用
      setTimeout(() => {
        this.list = [
          {
            id: 1,
            leaveId: 'LEAVE001',
            employeeName: '张三',
            department: '技术部',
            leaveType: '年假',
            startDate: '2024-01-20',
            endDate: '2024-01-22',
            days: 3,
            status: '待审批',
            approver: '',
            applyDate: '2024-01-15',
            approvalDate: '',
            reason: '春节回家探亲',
            emergencyContact: '张某某',
            contactPhone: '13800138001',
            approvalComment: ''
          },
          {
            id: 2,
            leaveId: 'LEAVE002',
            employeeName: '李四',
            department: '销售部',
            leaveType: '病假',
            startDate: '2024-01-16',
            endDate: '2024-01-16',
            days: 1,
            status: '已通过',
            approver: '王经理',
            applyDate: '2024-01-15',
            approvalDate: '2024-01-15',
            reason: '发烧感冒，需要休息',
            emergencyContact: '李某某',
            contactPhone: '13800138002',
            approvalComment: '注意休息，早日康复'
          },
          {
            id: 3,
            leaveId: 'LEAVE003',
            employeeName: '王五',
            department: '人事部',
            leaveType: '事假',
            startDate: '2024-01-18',
            endDate: '2024-01-18',
            days: 1,
            status: '已拒绝',
            approver: '张经理',
            applyDate: '2024-01-17',
            approvalDate: '2024-01-17',
            reason: '私人事务',
            emergencyContact: '王某某',
            contactPhone: '13800138003',
            approvalComment: '工作繁忙，建议调整请假时间'
          }
        ]
        this.total = 3
        this.listLoading = false
      }, 1000)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row) {
      this.viewData = Object.assign({}, row)
      this.dialogViewVisible = true
    },
    handleApprove(row) {
      this.approvalData = Object.assign({}, row)
      this.dialogApprovalVisible = true
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该请假申请吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.list.splice(index, 1)
        this.total--
        this.$notify({
          title: '成功',
          message: '删除成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024
          this.temp.leaveId = 'LEAVE' + ('000' + this.temp.id).slice(-3)
          this.temp.status = '待审批'
          this.temp.applyDate = new Date().toISOString().split('T')[0]
          this.list.unshift(this.temp)
          this.total++
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '申请提交成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          const index = this.list.findIndex(v => v.id === this.temp.id)
          this.list.splice(index, 1, tempData)
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    submitApproval() {
      const index = this.list.findIndex(v => v.id === this.approvalData.id)
      if (index !== -1) {
        this.list[index].status = this.approvalData.approvalResult === '通过' ? '已通过' : '已拒绝'
        this.list[index].approver = '当前审批人'
        this.list[index].approvalDate = new Date().toISOString().split('T')[0]
        this.list[index].approvalComment = this.approvalData.approvalComment
        this.dialogApprovalVisible = false
        this.$notify({
          title: '成功',
          message: '审批完成',
          type: 'success',
          duration: 2000
        })
      }
    },
    resetTemp() {
      this.temp = {
        employeeId: '',
        leaveType: '',
        startDate: '',
        endDate: '',
        days: 1,
        reason: '',
        emergencyContact: '',
        contactPhone: ''
      }
    },
    handleDownload() {
      this.downloadLoading = true
      setTimeout(() => {
        this.downloadLoading = false
        this.$notify({
          title: '成功',
          message: '导出成功',
          type: 'success',
          duration: 2000
        })
      }, 1000)
    },
    getStatusType(status) {
      const statusMap = {
        '待审批': 'warning',
        '已通过': 'success',
        '已拒绝': 'danger'
      }
      return statusMap[status]
    },
    getLeaveTypeColor(type) {
      const typeMap = {
        '年假': 'primary',
        '病假': 'danger',
        '事假': 'info',
        '婚假': 'success',
        '产假': 'warning',
        '陪产假': 'warning',
        '丧假': ''
      }
      return typeMap[type] || 'info'
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.box-card {
  text-align: center;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.number {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
}
.number.pending {
  color: #E6A23C;
}
.number.approved {
  color: #67C23A;
}
.number.rejected {
  color: #F56C6C;
}
.number.total {
  color: #909399;
}
.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 5px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
