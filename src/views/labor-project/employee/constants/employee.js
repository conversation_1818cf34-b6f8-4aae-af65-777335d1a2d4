// 员工管理相关常量定义

// 状态选项
export const STATUS_OPTIONS = [
  { label: '在职', value: 'active' },
  { label: '离职', value: 'inactive' }
]

// 默认工资类型选项
export const DEFAULT_SALARY_TYPE_OPTIONS = [
  { label: '月薪制', value: 1 },
  { label: '按天计薪', value: 2 }
]

// 性别选项
export const GENDER_OPTIONS = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
  { label: '其他', value: 'other' }
]

// 性别映射
export const GENDER_MAP = {
  'male': '男',
  'female': '女',
  'other': '其他'
}

// 状态映射
export const STATUS_MAP = {
  'active': '在职',
  'inactive': '离职'
}

// 工资类型标签颜色映射
export const SALARY_TYPE_COLOR_MAP = {
  '月薪制': 'success',
  '按天计薪': 'warning'
}

// 表单验证规则
export const FORM_RULES = {
  name: [{ required: true, message: '姓名是必填项', trigger: 'blur' }],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  gender: [{ required: true, message: '性别是必填项', trigger: 'change' }],
  departmentPath: [{ required: true, message: '部门是必填项', trigger: 'change' }],
  positionId: [{ required: true, message: '职位是必填项', trigger: 'change' }],
  salaryTypeId: [{ required: true, message: '工资类型是必填项', trigger: 'change' }],
  status: [{ required: true, message: '状态是必填项', trigger: 'change' }],
  employeesId: [{ required: true, message: '员工编号关联是必填项', trigger: 'change' }]
}

// 表格列配置
export const TABLE_COLUMNS = [
  { label: '员工编号', prop: 'employees_id', width: '120px' },
  { label: '姓名', prop: 'name', width: '120px' },
  { label: '性别', prop: 'gender', width: '80px' },
  { label: '部门', prop: 'department', width: '150px' },
  { label: '职位', prop: 'position', width: '150px' },
  { label: '基本工资', prop: 'base_salary', width: '120px' },
  { label: '工资类型', prop: 'salary_type', width: '120px' },
  { label: '状态', prop: 'status', width: '100px' },
  { label: '操作', prop: 'actions', width: '200px' }
]

// 默认员工表单数据
export const DEFAULT_EMPLOYEE_FORM = {
  name: '',
  email: '',
  phone: '',
  gender: '',
  department: '',
  departmentId: null,
  departmentPath: [],
  positionId: null,
  positionName: '',
  salaryTypeId: 1,
  status: 'active',
  idCard: '',
  address: '',
  remark: '',
  employeesId: '',
  employeesName: ''
}

// 对话框标题映射
export const DIALOG_TITLE_MAP = {
  create: '新增员工',
  update: '编辑员工'
} 