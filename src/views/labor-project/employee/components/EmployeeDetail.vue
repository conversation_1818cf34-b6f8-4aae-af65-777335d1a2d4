<template>
  <el-dialog title="员工详情" :visible.sync="visible" width="800px">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="姓名">{{ data.name || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ formatGender(data.gender) }}</el-descriptions-item>
      <el-descriptions-item label="邮箱">{{ data.email || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="电话">{{ data.phone || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="部门">{{ data.department }}</el-descriptions-item>
      <el-descriptions-item label="职位">{{ data.positionName || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="工资类型">
        <el-tag :type="getSalaryTypeTagColor(data.salaryTypeId)">
          {{ getSalaryTypeLabel(data.salaryTypeId) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="data.status === 'active' ? 'success' : 'danger'">
          {{ formatStatus(data.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="身份证号">{{ data.idCard || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="员工编号关联">{{ data.employeesId || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="关联员工名称">{{ data.employeesName || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="家庭住址" :span="2">{{ data.address || '未设置' }}</el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">{{ data.remark || '暂无备注' }}</el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
export default {
  name: 'EmployeeDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    formatGender: {
      type: Function,
      required: true
    },
    formatStatus: {
      type: Function,
      required: true
    },
    getSalaryTypeTagColor: {
      type: Function,
      required: true
    },
    getSalaryTypeLabel: {
      type: Function,
      required: true
    }
  },
  emits: ['update:visible']
}
</script>

<style scoped>
</style> 