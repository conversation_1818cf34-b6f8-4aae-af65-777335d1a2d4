<template>
  <el-table
    :key="tableKey"
    v-loading="loading"
    :data="data"
    border
    fit
    highlight-current-row
    style="width: 100%;"
  >
    <el-table-column label="员工编号" width="120px" align="center">
      <template slot-scope="{row}">
        <span>{{ row.employees_id || row.id }}</span>
      </template>
    </el-table-column>
    <el-table-column label="姓名" width="120px" align="center">
      <template slot-scope="{row}">
        <span>{{ row.name || '待完善' }}</span>
      </template>
    </el-table-column>
    <el-table-column label="性别" width="80px" align="center">
      <template slot-scope="{row}">
        <span>{{ formatGender(row.gender) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="部门" width="150px" align="center">
      <template slot-scope="{row}">
        <el-tag type="info" size="small">
          {{ row.department ? row.department.dept_name : getDepartmentName(row) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="职位" width="150px" align="center">
      <template slot-scope="{row}">
        <span>{{ row.position_info ? row.position_info.position_name : (row.position_name || row.positionName || '未设置') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="基本工资" width="120px" align="center">
      <template slot-scope="{row}">
        <span>{{ formatCurrency(row.base_salary) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="工资类型" width="120px" align="center">
      <template slot-scope="{row}">
        <el-tag :type="getSalaryTypeTagColor(row.salary_type_id)">
          {{ row.salary_type ? row.salary_type.type_name : getSalaryTypeLabel(row.salary_type_id) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="状态" width="100px" align="center">
      <template slot-scope="{row}">
        <el-tag :type="(row.status === '在职' || row.status === 'active') ? 'success' : 'danger'">
          {{ row.status === 'active' ? '在职' : (row.status === 'inactive' ? '离职' : row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" width="200px" class-name="small-padding fixed-width">
      <template slot-scope="{row,$index}">
        <el-button type="primary" size="mini" @click="$emit('edit', row)">
          编辑v1
        </el-button>
        <el-button size="mini" type="info" @click="$emit('view', row)">
          查看
        </el-button>
        <el-button size="mini" type="danger" @click="handleDelete(row, $index)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'EmployeeTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    formatGender: {
      type: Function,
      required: true
    },
    formatCurrency: {
      type: Function,
      required: true
    },
    getSalaryTypeTagColor: {
      type: Function,
      required: true
    },
    getSalaryTypeLabel: {
      type: Function,
      required: true
    },
    getDepartmentName: {
      type: Function,
      required: true
    }
  },
  emits: ['edit', 'view', 'delete'],
  data() {
    return {
      tableKey: 0
    }
  },
  methods: {
    handleDelete(row, index) {
      this.$confirm('确定要删除该员工吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete', { row, index })
      }).catch(() => {
        // 用户取消删除
      })
    },
    
    // 刷新表格
    refresh() {
      this.tableKey += 1
    }
  }
}
</script>

<style scoped>
.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}
</style> 