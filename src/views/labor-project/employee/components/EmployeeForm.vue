<template>
  <el-dialog 
    :title="dialogTitle" 
    :visible.sync="visible" 
    width="800px" 
    @close="handleClose"
  >
    <el-form 
      ref="dataForm" 
      :rules="rules" 
      :model="formData" 
      label-position="left" 
      label-width="100px" 
      style="width: 90%; margin-left:50px;"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入姓名" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="formData.gender" placeholder="请选择性别">
              <el-option v-for="item in genderOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱地址" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入电话号码" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="部门" prop="departmentPath">
            <el-cascader
              v-model="formData.departmentPath"
              :options="departmentOptions"
              :props="{ value: 'id', label: 'name', children: 'children', checkStrictly: true, emitPath: false }"
              placeholder="请选择部门"
              clearable
              style="width: 100%"
              @change="handleDepartmentChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="positionId">
            <el-select 
              v-model="formData.positionId" 
              placeholder="请选择职位" 
              style="width: 100%" 
              filterable 
              clearable 
              @change="handlePositionChange"
            >
              <el-option
                v-for="item in positionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.department }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="员工编号关联" prop="employeesId">
            <jia-yi-name-select
              ref="employeesIdSelect"
              v-model="formData.employeesId"
              placeholder="请搜索选择员工编号..."
              :clearable="true"
              :page-size="10"
              @change="handleEmployeesIdChange"
              @select="handleEmployeesIdSelect"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联员工名称">
            <el-input v-model="formData.employeesName" :disabled="true" placeholder="选择员工编号后自动填充" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="工资类型" prop="salaryTypeId">
            <el-select v-model="formData.salaryTypeId" placeholder="请选择工资类型">
              <el-option v-for="item in salaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="formData.idCard" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="家庭住址" prop="address">
        <el-input v-model="formData.address" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input 
          v-model="formData.remark" 
          :autosize="{ minRows: 2, maxRows: 4}" 
          type="textarea" 
          placeholder="请输入备注" 
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import JiaYiNameSelect from '@/components/select/jia_yi_name/jia_yi_name.vue'
import { GENDER_OPTIONS, STATUS_OPTIONS } from '../constants/employee'

export default {
  name: 'EmployeeForm',
  components: {
    JiaYiNameSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '员工信息'
    },
    formData: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    },
    departmentOptions: {
      type: Array,
      default: () => []
    },
    positionOptions: {
      type: Array,
      default: () => []
    },
    salaryTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  emits: [
    'update:visible', 
    'submit', 
    'close', 
    'department-change', 
    'position-change', 
    'employees-id-change', 
    'employees-id-select'
  ],
  data() {
    return {
      genderOptions: GENDER_OPTIONS,
      statusOptions: STATUS_OPTIONS
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$emit('submit')
        }
      })
    },
    
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    
    handleDepartmentChange(value) {
      this.$emit('department-change', value)
    },
    
    handlePositionChange(value) {
      this.$emit('position-change', value)
    },
    
    handleEmployeesIdChange(value) {
      this.$emit('employees-id-change', value)
    },
    
    handleEmployeesIdSelect(selectedItem) {
      this.$emit('employees-id-select', selectedItem)
    },
    
    // 清除表单验证
    clearValidate() {
      if (this.$refs.dataForm) {
        this.$refs.dataForm.clearValidate()
      }
    },
    
    // 重置员工编号关联组件
    resetEmployeesIdSelect() {
      if (this.$refs.employeesIdSelect) {
        this.$refs.employeesIdSelect.reset()
      }
    },
    
    // 强制重新加载员工编号关联组件
    forceReloadEmployeesIdSelect() {
      if (this.$refs.employeesIdSelect) {
        this.$refs.employeesIdSelect.forceReload()
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 