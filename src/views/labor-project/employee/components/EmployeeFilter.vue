<template>
  <div class="filter-container">
    <el-cascader
      v-model="localQuery.department"
      :options="departmentOptions"
      :props="{ value: 'id', label: 'name', children: 'children', checkStrictly: true, emitPath: false }"
      placeholder="部门"
      clearable
      style="width: 200px"
      class="filter-item"
      @change="emitFilter"
    />
    <el-select 
      v-model="localQuery.status" 
      placeholder="状态" 
      clearable 
      style="width: 120px" 
      class="filter-item"
      @change="emitFilter"
    >
      <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-select 
      v-model="localQuery.salaryTypeId" 
      placeholder="工资类型" 
      clearable 
      style="width: 150px" 
      class="filter-item"
      @change="emitFilter"
    >
      <el-option v-for="item in salaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-button 
      v-waves 
      class="filter-item" 
      type="primary" 
      icon="el-icon-search" 
      @click="handleFilter"
    >
      搜索
    </el-button>
    <el-button 
      class="filter-item" 
      style="margin-left: 10px;" 
      type="primary" 
      icon="el-icon-plus" 
      @click="$emit('create')"
    >
      新增员工
    </el-button>
    <el-button 
      v-waves 
      :loading="downloadLoading" 
      class="filter-item" 
      type="success" 
      icon="el-icon-download" 
      @click="$emit('download')"
    >
      导出Excel
    </el-button>
    <el-button 
      class="filter-item" 
      type="info" 
      icon="el-icon-setting" 
      @click="$emit('debug')"
    >
      调试模式
    </el-button>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { STATUS_OPTIONS } from '../constants/employee'

export default {
  name: 'EmployeeFilter',
  directives: { waves },
  props: {
    query: {
      type: Object,
      required: true
    },
    departmentOptions: {
      type: Array,
      default: () => []
    },
    salaryTypeOptions: {
      type: Array,
      default: () => []
    },
    downloadLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['filter', 'create', 'download', 'debug'],
  data() {
    return {
      localQuery: { ...this.query },
      statusOptions: STATUS_OPTIONS
    }
  },
  watch: {
    query: {
      handler(newQuery) {
        this.localQuery = { ...newQuery }
      },
      deep: true
    }
  },
  methods: {
    handleFilter() {
      this.$emit('filter', { ...this.localQuery })
    },
    emitFilter() {
      this.$nextTick(() => {
        this.$emit('filter', { ...this.localQuery })
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
</style> 