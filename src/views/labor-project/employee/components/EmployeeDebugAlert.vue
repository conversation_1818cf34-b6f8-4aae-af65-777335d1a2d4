<template>
  <el-alert
    v-if="show"
    title="暂无员工数据"
    :description="description"
    type="warning"
    style="margin-bottom: 20px;"
    :closable="false"
  >
    <template slot="default">
      <div style="margin-top: 10px;">
        <el-button size="small" type="primary" @click="$emit('test-api')">测试API连接</el-button>
        <el-button size="small" type="info" @click="$emit('debug')">调试模式</el-button>
      </div>
    </template>
  </el-alert>
</template>

<script>
export default {
  name: 'EmployeeDebugAlert',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    description: {
      type: String,
      default: '请检查后端服务和代理配置，或访问 /labor-project/employee-debug 查看调试信息'
    }
  },
  emits: ['test-api', 'debug']
}
</script>

<style scoped>
</style> 