<template>
  <div class="app-container">
    <div class="filter-container">
      <el-cascader
        v-model="listQuery.department"
        :options="departmentOptions"
        :props="{ value: 'id', label: 'name', children: 'children', checkStrictly: true, emitPath: false }"
        placeholder="部门"
        clearable
        style="width: 200px"
        class="filter-item"
      />
      <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.salaryTypeId" placeholder="工资类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in salaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        新增员工
      </el-button>
      <el-button v-waves :loading="downloadLoading" class="filter-item" type="success" icon="el-icon-download" @click="handleDownload">
        导出Excel
      </el-button>
      <el-button class="filter-item" type="info" icon="el-icon-setting" @click="$router.push('/labor-project/employee-debug')">
        调试模式
      </el-button>
    </div>

    <!-- 调试信息 -->
    <el-alert
      v-if="list.length === 0 && !listLoading"
      title="暂无员工数据"
      :description="`请检查后端服务和代理配置，或访问 /labor-project/employee-debug 查看调试信息`"
      type="warning"
      style="margin-bottom: 20px;"
      :closable="false"
    >
      <template slot="default">
        <div style="margin-top: 10px;">
          <el-button size="small" type="primary" @click="testApiConnection">测试API连接</el-button>
          <el-button size="small" type="info" @click="$router.push('/labor-project/employee-debug')">调试模式</el-button>
        </div>
      </template>
    </el-alert>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="员工编号" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employees_id || row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.name || '待完善' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="性别" width="80px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatGender(row.gender) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" width="150px" align="center">
        <template slot-scope="{row}">
          <el-tag type="info" size="small" style="font-family: 'Noto Sans Myanmar', 'Myanmar Text', sans-serif;">
            <span v-if="row.department && row.department.dept_name" :title="row.department.dept_name">
              {{ row.department.dept_name }}
            </span>
            <span v-else :title="getDepartmentName(row)">
              {{ getDepartmentName(row) }}
            </span>
            <!-- 调试信息按钮 -->
            <el-button 
              type="text" 
              size="mini" 
              icon="el-icon-info" 
              @click="debugRowData(row)"
              style="margin-left: 5px; padding: 0; font-size: 12px;"
              title="查看调试信息"
            />
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="职位" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.position_info ? row.position_info.position_name : (row.position_name || row.positionName || '未设置') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="基本工资" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatCurrency(row.base_salary) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工资类型" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getSalaryTypeTagColor(row.salary_type_id)">
            {{ row.salary_type ? row.salary_type.type_name : getSalaryTypeLabel(row.salary_type_id) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="(row.status === '在职' || row.status === 'active') ? 'success' : 'danger'">
            {{ row.status === 'active' ? '在职' : (row.status === 'inactive' ? '离职' : row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 隐藏员工编号关联列 -->


      <!-- <el-table-column label="员工编号关联" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employees_id || row.employeesId || '未设置' }}</span>
        </template>
      </el-table-column> -->



      <el-table-column label="操作" align="center" width="200px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="info" @click="handleView(row)">
            查看
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row,$index)">
            删除
          </el-button>
        </template>
      </el-table-column>



    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 添加/编辑员工对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="800px" @close="handleDialogClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 90%; margin-left:50px;">
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="temp.gender" placeholder="请选择性别">
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="temp.email" placeholder="请输入邮箱地址" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="temp.phone" placeholder="请输入电话号码" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门" prop="departmentPath">
              <el-cascader
                v-model="temp.departmentPath"
                :options="departmentOptions"
                :props="{ value: 'id', label: 'name', children: 'children', checkStrictly: true, emitPath: true }"
                placeholder="请选择部门"
                clearable
                style="width: 100%"
                @change="handleDepartmentChange"
              />
              <!-- 临时调试信息 -->
              <div v-if="dialogStatus === 'update'" style="margin-top: 5px; font-size: 12px; color: #999;">
                <el-button size="mini" type="text" @click="showDepartmentDebugInfo">
                  🔍 调试部门数据
                </el-button>
                <br>
                <span>当前路径: {{ JSON.stringify(temp.departmentPath) }}</span><br>
                <span>路径长度: {{ temp.departmentPath ? temp.departmentPath.length : 0 }}</span><br>
                <span>最终部门ID: {{ temp.departmentPath && temp.departmentPath.length > 0 ? temp.departmentPath[temp.departmentPath.length - 1] : temp.departmentId }}</span><br>
                <span>部门名称: {{ temp.department }}</span><br>
                <span>选项数量: {{ departmentOptions.length }}</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="positionId">
              <el-select v-model="temp.positionId" placeholder="请选择职位" style="width: 100%" filterable clearable @change="handlePositionChange">
                <el-option
                  v-for="item in positionOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.department }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="员工编号关联" prop="employeesId">
              <jia-yi-name-select
                ref="employeesIdSelect"
                v-model="temp.employeesId"
                placeholder="请搜索选择员工编号..."
                :clearable="true"
                :page-size="10"
                @change="handleEmployeesIdChange"
                @select="handleEmployeesIdSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联员工名称">
              <el-input v-model="temp.employeesName" :disabled="true" placeholder="选择员工编号后自动填充" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工资类型" prop="salaryTypeId">
              <el-select v-model="temp.salaryTypeId" placeholder="请选择工资类型">
                <el-option v-for="item in salaryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="temp.status" placeholder="请选择状态">
                <el-option label="在职" value="active" />
                <el-option label="离职" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="temp.idCard" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="家庭住址" prop="address">
          <el-input v-model="temp.address" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看员工详情对话框 -->
    <el-dialog title="员工详情" :visible.sync="dialogViewVisible" width="800px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">{{ viewData.name || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ formatGender(viewData.gender) }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ viewData.email || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="电话">{{ viewData.phone || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ viewData.department }}</el-descriptions-item>
        <el-descriptions-item label="职位">{{ viewData.positionName || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="工资类型">
          <el-tag :type="getSalaryTypeTagColor(viewData.salaryTypeId)">
            {{ getSalaryTypeLabel(viewData.salaryTypeId) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewData.status === 'active' ? 'success' : 'danger'">
            {{ formatStatus(viewData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ viewData.idCard }}</el-descriptions-item>
        <el-descriptions-item label="员工编号关联">{{ viewData.employeesId || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="关联员工名称">{{ viewData.employeesName || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="家庭住址" :span="2">{{ viewData.address }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import JiaYiNameSelect from '@/components/select/jia_yi_name/jia_yi_name.vue'
import { getEmployeeList, getEmployeeListV2, createEmployee, updateEmployee, deleteEmployee, getEmployeeDetail, getEmployeeDetailV2 } from '@/api/labor-project/employees'
import { getSalaryTypeList } from '@/api/labor-project/salaries'
import { getDepartmentTree } from '@/api/labor-project/departments'
import { getPositionOptions, getPositionsByDepartment } from '@/api/labor-project/positions'

export default {
  name: 'EmployeeManagement',
  components: {
    Pagination,
    JiaYiNameSelect
  },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        department: undefined,
        departmentId: undefined,
        status: undefined,
        salaryTypeId: undefined
      },
      departmentOptions: [],
      positionOptions: [],
      statusOptions: [
        { label: '在职', value: 'active' },
        { label: '离职', value: 'inactive' }
      ],
      salaryTypeOptions: [
        { label: '月薪制', value: 1 },
        { label: '按天计薪', value: 2 }
      ],
      temp: {
        name: '',
        email: '',
        phone: '',
        gender: '',
        department: '',
        departmentId: null,
        departmentPath: [],
        positionId: null,
        positionName: '',
        salaryTypeId: 1, // 默认为月薪制
        status: 'active',
        idCard: '',
        address: '',
        remark: '',
        employeesId: '', // 员工编号关联ID
        employeesName: '' // 关联员工名称
      },
      dialogFormVisible: false,
      dialogViewVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑员工',
        create: '新增员工'
      },
      viewData: {},
      downloadLoading: false,
      rules: {
        name: [{ required: true, message: '姓名是必填项', trigger: 'blur' }],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        gender: [{ required: true, message: '性别是必填项', trigger: 'change' }],
        departmentPath: [{ required: true, message: '部门是必填项', trigger: 'change' }],
        positionId: [{ required: true, message: '职位是必填项', trigger: 'change' }],
        salaryTypeId: [{ required: true, message: '工资类型是必填项', trigger: 'change' }],
        status: [{ required: true, message: '状态是必填项', trigger: 'change' }],
        employeesId: [{ required: true, message: '员工编号关联是必填项', trigger: 'change' }]
      }
    }
  },
  created() {
    this.loadMyanmarFont()
    this.getList()
    this.loadSalaryTypes()
    this.loadDepartments()
    this.loadPositions()
  },
  methods: {
    // 加载缅甸语字体
    loadMyanmarFont() {
      // 动态加载 Google Fonts 中的缅甸语字体
      const link = document.createElement('link')
      link.href = 'https://fonts.googleapis.com/css2?family=Noto+Sans+Myanmar:wght@400;500;700&display=swap'
      link.rel = 'stylesheet'
      
      // 检查是否已经加载了这个字体
      const existingLink = document.querySelector(`link[href="${link.href}"]`)
      if (!existingLink) {
        document.head.appendChild(link)
        console.log('✅ 缅甸语字体已加载')
      }
      
      // 作为备用方案，尝试加载本地系统字体
      const style = document.createElement('style')
      style.textContent = `
        @font-face {
          font-family: 'Myanmar Fallback';
          src: local('Myanmar Text'), local('Noto Sans Myanmar'), local('Padauk');
          unicode-range: U+1000-109F, U+AA60-AA7F, U+A9E0-A9FF;
        }
      `
      document.head.appendChild(style)
    },
    async getList() {
      this.listLoading = true
      try {
        console.log('🔍 正在获取员工列表...')
        
        // 构建查询参数，将页面筛选条件映射到API参数
        const queryParams = {
          page: this.listQuery.page,
          limit: this.listQuery.limit,
          department_id: this.listQuery.department || this.listQuery.departmentId,
          status: this.listQuery.status,
          salary_type_id: this.listQuery.salaryTypeId
        }
        
        // 移除空值参数
        Object.keys(queryParams).forEach(key => {
          if (queryParams[key] === undefined || queryParams[key] === null || queryParams[key] === '') {
            delete queryParams[key]
          }
        })
        
        console.log('📤 请求参数:', queryParams)
        const response = await getEmployeeListV2(queryParams)
        console.log('✅ API响应:', response)

        // 处理V2 API响应格式：{ success: true, data: { employees: [...], pagination: {...} } }
        if (response && response.success && response.data) {
          this.list = response.data.employees || []
          this.total = response.data.pagination ? response.data.pagination.total : this.list.length
          
          console.log('📋 处理后的员工列表:', this.list)
          console.log('📊 总数:', this.total)
        } else {
          console.warn('⚠️ 响应格式异常:', response)
          this.list = []
          this.total = 0
        }
      } catch (error) {
        console.error('❌ 获取员工列表失败:', error)
        this.$message.error('获取员工列表失败: ' + error.message)

        // 显示更友好的错误提示
        if (error.message.includes('Network Error') || error.message.includes('网络错误')) {
          this.$message.error('网络连接失败，请检查后端服务和代理配置')
        }

        this.list = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        // 重置员工编号关联组件
        if (this.$refs['employeesIdSelect']) {
          this.$refs['employeesIdSelect'].reset()
        }
      })
    },
    async handleUpdate(row) {
      console.log('🔍 开始编辑员工，ID:', row.id)
      try {
        console.log('📝 开始编辑员工，ID:', row.id)
        
        // 首先确保部门选项已加载完成
        await this.ensureDepartmentOptionsLoaded()
        
        // 调用 v2 API 获取员工详情
        const response = await getEmployeeDetailV2(row.id)
        console.log('✅ 获取员工详情响应:', response)
        
        let employeeData = null
        
        // 处理API响应数据
        if (response && response.success && response.data) {
          employeeData = response.data
        } else if (response && response.data) {
          employeeData = response.data
        } else {
          // 如果API调用失败，降级使用行数据
          console.warn('⚠️ API响应异常，使用行数据:', response)
          employeeData = row
        }
        
        console.log('🔍 员工详情原始数据:', employeeData)
        console.log('🔍 员工详情数据字段:', Object.keys(employeeData))
        
        // 映射API字段到表单字段
        const departmentId = employeeData.department_id || (employeeData.department ? employeeData.department.id : null)
        const departmentName = employeeData.department ? employeeData.department.dept_name : (employeeData.department || '')
        
        console.log('🔍 部门映射信息:')
        console.log('  - 原始 department_id:', employeeData.department_id)
        console.log('  - 原始 department 对象:', employeeData.department)
        console.log('  - 映射后 departmentId:', departmentId)
        console.log('  - 映射后 departmentName:', departmentName)
        console.log('  - 当前 departmentOptions:', this.departmentOptions)
        
        // 使用新的路径构建函数
        const departmentPath = this.buildDepartmentPathFromApiData(employeeData)
        console.log('🔍 构建的部门路径:', departmentPath)
        
        this.temp = {
          id: employeeData.id,
          name: employeeData.name || '',
          email: employeeData.email || '',
          phone: employeeData.phone || '',
          gender: employeeData.gender || '',
          department: departmentName,
          departmentId: departmentId,
          departmentPath: departmentPath,
          positionId: employeeData.position_id || null,
          positionName: employeeData.position_info ? employeeData.position_info.position_name : (employeeData.position || employeeData.position_name || ''),
          salaryTypeId: employeeData.salary_type_id || (employeeData.salary_type ? employeeData.salary_type.id : 1),
          status: employeeData.status || 'active',
          idCard: employeeData.id_card || employeeData.idCard || '',
          address: employeeData.address || '',
          remark: employeeData.remark || '',
          employeesId: employeeData.employees_id || employeeData.employeesId || '',
          employeesName: employeeData.employees_name || employeeData.employeesName || (employeeData.name || '')
        }
        
        console.log('📋 最终映射的表单数据:', this.temp)
        console.log('📋 最终 departmentPath:', this.temp.departmentPath)
        
        console.log('📋 映射后的表单数据:', this.temp)
        console.log('🔍 具体字段值检查:')
        console.log('  - gender:', employeeData.gender)
        console.log('  - department:', employeeData.department)
        console.log('  - department_id:', employeeData.department_id)
        console.log('  - position_id:', employeeData.position_id)
        console.log('  - position_info:', employeeData.position_info)
        console.log('  - salary_type_id:', employeeData.salary_type_id)
        console.log('  - status:', employeeData.status)
        console.log('  - employees_id:', employeeData.employees_id)
        
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
        
        this.$nextTick(async () => {
          this.$refs['dataForm'].clearValidate()
          // 强制重新加载员工编号关联组件
          if (this.$refs['employeesIdSelect']) {
            this.$refs['employeesIdSelect'].forceReload()
          }
          
          // 如果有部门ID，加载该部门的职位选项
          if (this.temp.departmentId) {
            console.log('🔄 加载部门职位选项，部门ID:', this.temp.departmentId)
            await this.loadPositionsByDepartment(this.temp.departmentId)
          }
        })
        
      } catch (error) {
        console.error('❌ 获取员工详情失败:', error)
        this.$message.error('获取员工详情失败: ' + error.message)
        
        // 确保部门选项已加载
        await this.ensureDepartmentOptionsLoaded()
        
        // 如果API调用失败，降级使用行数据
        const departmentId = row.department_id || (row.department ? row.department.id : null)
        const departmentName = row.department ? row.department.dept_name : (row.department || '')
        
        console.log('🔍 降级数据部门映射信息:')
        console.log('  - 行数据 department_id:', row.department_id)
        console.log('  - 行数据 department 对象:', row.department)
        
        // 使用新的路径构建函数处理降级数据
        const departmentPath = this.buildDepartmentPathFromApiData(row)
        console.log('🔍 降级数据构建的部门路径:', departmentPath)
        
        this.temp = {
          id: row.id,
          name: row.name || '',
          email: row.email || '',
          phone: row.phone || '',
          gender: row.gender,
          department: departmentName,
          departmentId: departmentId,
          departmentPath: departmentPath,
          positionId: row.position_id || (row.position_info ? row.position_info.id : null),
          positionName: row.position_info ? row.position_info.position_name : (row.position_name || ''),
          salaryTypeId: row.salary_type_id || (row.salary_type ? row.salary_type.id : 1),
          status: row.status,
          idCard: row.id_card || row.idCard,
          address: row.address,
          remark: row.remark,
          employeesId: row.employees_id || row.employeesId || '',
          employeesName: row.employees_name || row.employeesName || ''
        }
        
        console.log('📋 降级数据最终映射结果:', this.temp)
        
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
        
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
          if (this.$refs['employeesIdSelect']) {
            this.$refs['employeesIdSelect'].forceReload()
          }
        })
      }
    },
    handleView(row) {
      // 映射V2 API字段到显示字段
      this.viewData = {
        id: row.id,
        name: row.name || '',
        email: row.email || '',
        phone: row.phone || '',
        gender: row.gender,
        department: row.department ? row.department.dept_name : (row.department || ''),
        positionName: row.position_info ? row.position_info.position_name : (row.position_name || ''),
        salaryTypeId: row.salary_type_id || (row.salary_type ? row.salary_type.id : 1),
        status: row.status,
        idCard: row.id_card || row.idCard,
        address: row.address,
        remark: row.remark,
        employeesId: row.employees_id || row.employeesId || '',
        employeesName: row.employees_name || row.employeesName || ''
      }
      this.dialogViewVisible = true
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该员工吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const response = await deleteEmployee(row.id)
          console.log('✅ 删除员工响应:', response)
          this.$notify({
            title: '成功',
            message: response.message || '员工删除成功',
            type: 'success',
            duration: 2000
          })
          // 刷新列表
          this.getList()
        } catch (error) {
          console.error('删除员工失败:', error)
          this.$message.error('删除员工失败: ' + error.message)
        }
      })
    },
    async createData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 转换数据格式为API要求的格式
            // 确保使用正确的部门ID（从路径数组的最后一个元素获取）
            let departmentId = this.temp.departmentId
            if (Array.isArray(this.temp.departmentPath) && this.temp.departmentPath.length > 0) {
              departmentId = this.temp.departmentPath[this.temp.departmentPath.length - 1]
            }
            
            const employeeData = {
              name: this.temp.name,
              email: this.temp.email,
              phone: this.temp.phone,
              gender: this.temp.gender,
              department: this.temp.department,
              department_id: departmentId,
              position_id: this.temp.positionId,
              salary_type_id: this.temp.salaryTypeId || 1,
              status: this.temp.status,
              id_card: this.temp.idCard,
              address: this.temp.address,
              remark: this.temp.remark,
              employees_id: this.temp.employeesId
            }
            
            console.log('📤 创建员工提交的数据:', employeeData)

            const response = await createEmployee(employeeData)
            console.log('✅ 创建员工响应:', response)

            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '员工创建成功',
              type: 'success',
              duration: 2000
            })

            // 刷新列表
            this.getList()
          } catch (error) {
            console.error('创建员工失败:', error)
            this.$message.error('创建员工失败: ' + error.message)
          }
        }
      })
    },
    async updateData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 转换数据格式为API要求的格式
            // 确保使用正确的部门ID（从路径数组的最后一个元素获取）
            let departmentId = this.temp.departmentId
            if (Array.isArray(this.temp.departmentPath) && this.temp.departmentPath.length > 0) {
              departmentId = this.temp.departmentPath[this.temp.departmentPath.length - 1]
            }
            
            const employeeData = {
              name: this.temp.name,
              email: this.temp.email,
              phone: this.temp.phone,
              gender: this.temp.gender,
              department: this.temp.department,
              department_id: departmentId,
              position_id: this.temp.positionId,
              salary_type_id: this.temp.salaryTypeId || 1,
              status: this.temp.status,
              id_card: this.temp.idCard,
              address: this.temp.address,
              remark: this.temp.remark,
              employees_id: this.temp.employeesId
            }
            
            console.log('📤 更新员工提交的数据:', employeeData)

            const response = await updateEmployee(this.temp.id, employeeData)
            console.log('✅ 更新员工响应:', response)

            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '员工信息更新成功',
              type: 'success',
              duration: 2000
            })

            // 刷新列表
            this.getList()
          } catch (error) {
            console.error('更新员工失败:', error)
            this.$message.error('更新员工失败: ' + error.message)
          }
        }
      })
    },
    resetTemp() {
      this.temp = {
        name: '',
        email: '',
        phone: '',
        gender: '',
        department: '',
        departmentId: null,
        departmentPath: [],
        positionId: null,
        positionName: '',
        salaryTypeId: 1, // 默认为月薪制
        status: 'active',
        idCard: '',
        address: '',
        remark: '',
        employeesId: '', // 员工编号关联ID
        employeesName: '' // 关联员工名称
      }
    },

    // 处理员工编号关联变化
    handleEmployeesIdChange(value) {
      console.log('员工编号关联变化:', value)
      this.temp.employeesId = value
    },

    // 处理员工编号关联选择
    handleEmployeesIdSelect(selectedItem) {
      console.log('选择的员工编号项:', selectedItem)
      if (selectedItem) {
        this.temp.employeesName = selectedItem.name
        this.temp.employeesId = selectedItem.id
      } else {
        this.temp.employeesName = ''
      }
    },
    handleDownload() {
      this.downloadLoading = true
      // 模拟下载
      setTimeout(() => {
        this.downloadLoading = false
        this.$notify({
          title: '成功',
          message: '导出成功',
          type: 'success',
          duration: 2000
        })
      }, 1000)
    },

    // 快速API连接测试
    async testApiConnection() {
      try {
        const response = await fetch('/labor-project/api/v1/employees')
        if (response.ok) {
          const data = await response.json()
          this.$message.success('API连接成功，数据已获取')
          console.log('API测试成功:', data)
        } else {
          this.$message.error(`API连接失败: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        this.$message.error('网络连接错误: ' + error.message)
        console.error('API测试失败:', error)
      }
    },

    // 格式化日期显示
    formatDate(dateString) {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    // 格式化日期为表单输入格式
    formatDateForInput(dateString) {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return date.toISOString().split('T')[0]
      } catch (error) {
        return ''
      }
    },

    // 格式化货币
    formatCurrency(amount) {
      if (!amount && amount !== 0) return '¥0'
      return '¥' + Number(amount).toLocaleString()
    },

    // 获取工资类型标签颜色
    getSalaryTypeTagColor(salaryTypeId) {
      const option = this.salaryTypeOptions.find(item => item.value === salaryTypeId)
      if (!option) return 'default'

      // 根据工资类型返回不同颜色
      const colorMap = {
        '月薪制': 'success', // 绿色
        '按天计薪': 'warning' // 橙色
      }
      return colorMap[option.label] || 'default'
    },

    // 获取工资类型标签文本
    getSalaryTypeLabel(salaryTypeId) {
      const option = this.salaryTypeOptions.find(item => item.value === salaryTypeId)
      return option ? option.label : '未设置'
    },

    // 动态加载工资类型列表
    async loadSalaryTypes() {
      try {
        const response = await getSalaryTypeList({})
        console.log('✅ 工资类型列表响应:', response)

        // 处理响应数据
        if (response && response.data && Array.isArray(response.data)) {
          this.salaryTypeOptions = response.data.map(item => ({
            label: item.type_name,
            value: item.id
          }))
        }

        console.log('📋 工资类型选项:', this.salaryTypeOptions)
      } catch (error) {
        console.error('❌ 获取工资类型列表失败:', error)
        // 如果获取失败，使用默认值
        this.salaryTypeOptions = [
          { label: '月薪制', value: 1 },
          { label: '按天计薪', value: 2 }
        ]
      }
    },

    // 动态加载部门列表
    async loadDepartments() {
      try {
        const response = await getDepartmentTree({})
        console.log('✅ 部门列表响应:', response)

        // 处理响应数据，构建级联选择器需要的数据结构
        if (response && response.data) {
          this.departmentOptions = this.buildDepartmentTree(response.data)
        }

        console.log('📋 部门选项:', this.departmentOptions)
      } catch (error) {
        console.error('❌ 获取部门列表失败:', error)
        // 如果获取失败，使用默认值
        this.departmentOptions = [
          {
            id: 1,
            name: '技术部',
            children: [
              { id: 11, name: '前端开发组', children: [] },
              { id: 12, name: '后端开发组', children: [] },
              { id: 13, name: '测试组', children: [] }
            ]
          },
          {
            id: 2,
            name: '销售部',
            children: [
              { id: 21, name: '华北销售组', children: [] },
              { id: 22, name: '华南销售组', children: [] }
            ]
          },
          { id: 3, name: '人事部', children: [] },
          { id: 4, name: '财务部', children: [] },
          { id: 5, name: '市场部', children: [] }
        ]
      }
    },

    // 构建部门树结构
    buildDepartmentTree(departments) {
      return departments.map(dept => ({
        id: dept.id,
        name: dept.dept_name || dept.name,
        children: dept.children && dept.children.length > 0 ? this.buildDepartmentTree(dept.children) : []
      }))
    },

    // 加载职位选项
    async loadPositions() {
      try {
        const response = await getPositionOptions()
        console.log('✅ 职位列表响应:', response)
        
        if (response && response.data) {
          this.positionOptions = response.data.map(position => ({
            value: position.id,
            label: position.position_name,
            department: position.department ? position.department.dept_name : '未知部门',
            positionCode: position.position_code
          }))
          console.log('📋 职位选项:', this.positionOptions)
        }
      } catch (error) {
        console.error('❌ 加载职位选项失败:', error)
        this.$message.error('加载职位选项失败: ' + error.message)
      }
    },

    // 根据部门ID加载职位选项
    async loadPositionsByDepartment(departmentId) {
      try {
        console.log('🔄 根据部门ID加载职位:', departmentId)
        const response = await getPositionsByDepartment(departmentId)
        console.log('✅ 部门职位列表响应:', response)
        
        if (response && response.data) {
          // 处理API响应数据
          const positions = Array.isArray(response.data) ? response.data : []
          
          this.positionOptions = positions.map(position => ({
            value: position.id,
            label: position.position_name || position.name,
            department: position.department ? position.department.dept_name : '当前部门',
            positionCode: position.position_code || position.code
          }))
          
          console.log('📋 部门职位选项:', this.positionOptions)
          
          if (positions.length === 0) {
            this.$message.info('该部门暂无可用职位')
          }
        } else {
          this.positionOptions = []
          this.$message.warning('该部门暂无职位数据')
        }
      } catch (error) {
        console.error('❌ 根据部门加载职位失败:', error)
        this.$message.error('获取部门职位失败: ' + error.message)
        // 发生错误时清空职位选项
        this.positionOptions = []
      }
    },

    // 处理部门选择变化
    async handleDepartmentChange(value) {
      console.log('🔄 部门选择变化，接收到的值:', value)
      
      if (value && Array.isArray(value) && value.length > 0) {
        // value 现在是一个路径数组，最后一个元素是选中的部门ID
        const selectedDepartmentId = value[value.length - 1]
        this.temp.departmentId = selectedDepartmentId
        this.temp.departmentPath = [...value] // 保存完整路径
        
        // 根据选中的部门ID找到部门名称
        const departmentName = this.findDepartmentName(this.departmentOptions, selectedDepartmentId)
        this.temp.department = departmentName
        
        console.log('部门选择结果:', {
          path: value,
          selectedId: selectedDepartmentId,
          name: departmentName
        })
        
        // 清空当前选中的职位
        this.temp.positionId = null
        this.temp.positionName = ''
        
        // 根据部门ID获取该部门的岗位
        await this.loadPositionsByDepartment(selectedDepartmentId)
      } else {
        // 清空选择
        this.temp.departmentId = null
        this.temp.department = ''
        this.temp.departmentPath = []
        this.temp.positionId = null
        this.temp.positionName = ''
        
        console.log('部门选择已清空')
        
        // 恢复加载所有职位
        this.loadPositions()
      }
    },

    // 查找部门名称
    findDepartmentName(departments, id) {
      for (const dept of departments) {
        if (dept.id === id) {
          return dept.name
        }
        if (dept.children && dept.children.length > 0) {
          const childName = this.findDepartmentName(dept.children, id)
          if (childName) return childName
        }
      }
      return ''
    },

    // 检查部门路径是否存在于部门选项中
    checkDepartmentPathExists(departments, departmentId) {
      for (const dept of departments) {
        if (dept.id === departmentId) {
          return true
        }
        if (dept.children && dept.children.length > 0) {
          if (this.checkDepartmentPathExists(dept.children, departmentId)) {
            return true
          }
        }
      }
      return false
    },

    // 确保部门选项已加载
    async ensureDepartmentOptionsLoaded() {
      if (this.departmentOptions.length === 0) {
        console.log('🔄 部门选项为空，重新加载...')
        await this.loadDepartments()
      }
      console.log('✅ 部门选项已加载，数量:', this.departmentOptions.length)
    },

    // 显示部门调试信息
    showDepartmentDebugInfo() {
      const debugInfo = {
        '表单部门路径': this.temp.departmentPath,
        '表单部门ID': this.temp.departmentId,
        '表单部门名称': this.temp.department,
        '部门选项总数': this.departmentOptions.length,
        '部门选项列表': this.departmentOptions,
        '路径是否存在': this.temp.departmentPath.length > 0 ? this.checkDepartmentPathExists(this.departmentOptions, this.temp.departmentPath[0]) : false
      }
      
      console.log('🔍 部门调试信息详情:', debugInfo)
      
      // 扁平化显示所有部门选项，便于查找
      const flatDepartments = this.flattenDepartments(this.departmentOptions)
      console.log('📋 扁平化部门列表:', flatDepartments)
      
      this.$alert(
        `部门调试信息：
        
        表单数据：
        - 部门路径：${JSON.stringify(this.temp.departmentPath)}
        - 部门ID：${this.temp.departmentId}
        - 部门名称：${this.temp.department}
        
        选项数据：
        - 部门选项总数：${this.departmentOptions.length}
        - 当前路径是否存在：${this.temp.departmentPath.length > 0 ? this.checkDepartmentPathExists(this.departmentOptions, this.temp.departmentPath[0]) : '无路径'}
        
        详细信息已输出到控制台（F12查看）`,
        '部门数据调试',
        {
          confirmButtonText: '确定',
          type: 'info'
        }
      )
    },

    // 扁平化部门树结构
    flattenDepartments(departments, level = 0) {
      let result = []
      departments.forEach(dept => {
        result.push({
          id: dept.id,
          name: dept.name,
          level: level,
          fullPath: '  '.repeat(level) + dept.name
        })
        if (dept.children && dept.children.length > 0) {
          result = result.concat(this.flattenDepartments(dept.children, level + 1))
        }
      })
      return result
    },

    // 根据部门ID查找完整的级联路径
    findDepartmentPath(departments, targetId, currentPath = []) {
      for (const dept of departments) {
        const newPath = [...currentPath, dept.id]
        
        // 如果找到目标部门，返回路径
        if (dept.id === targetId) {
          console.log(`🔍 找到部门 ${targetId} 的路径:`, newPath)
          return newPath
        }
        
        // 如果有子部门，递归查找
        if (dept.children && dept.children.length > 0) {
          const foundPath = this.findDepartmentPath(dept.children, targetId, newPath)
          if (foundPath) {
            return foundPath
          }
        }
      }
      
      return null
    },

    // 根据API部门数据构建级联路径
    buildDepartmentPathFromApiData(employeeData) {
      const departmentId = employeeData.department_id || (employeeData.department ? employeeData.department.id : null)
      
      if (!departmentId || this.departmentOptions.length === 0) {
        console.log('🔍 无部门ID或部门选项为空')
        return []
      }
      
      // 先尝试从级联选择器选项中查找路径
      const cascaderPath = this.findDepartmentPath(this.departmentOptions, departmentId)
      if (cascaderPath) {
        console.log(`🔍 从级联选择器找到部门 ${departmentId} 的路径:`, cascaderPath)
        return cascaderPath
      }
      
      // 如果从级联选择器没找到，尝试从API数据中构建
      // 检查是否有parent_id信息
      if (employeeData.department && employeeData.department.parent_id) {
        const parentId = employeeData.department.parent_id
        console.log(`🔍 从API数据构建路径: parent_id=${parentId}, dept_id=${departmentId}`)
        
        // 验证父部门是否存在于选项中
        const parentPath = this.findDepartmentPath(this.departmentOptions, parentId)
        if (parentPath) {
          const fullPath = [...parentPath, departmentId]
          console.log(`🔍 构建的完整路径:`, fullPath)
          return fullPath
        } else {
          console.warn(`⚠️ 父部门 ${parentId} 在选项中不存在`)
        }
      }
      
      // 如果都找不到，返回单个ID数组（用于一级部门）
      console.log(`🔍 使用单个部门ID作为路径: [${departmentId}]`)
      return [departmentId]
    },

    // 处理职位选择变化
    handlePositionChange(value) {
      console.log('🔄 职位选择变化:', value)
      if (value) {
        const selectedPosition = this.positionOptions.find(option => option.value === value)
        if (selectedPosition) {
          this.temp.positionName = selectedPosition.label
          console.log('👔 设置职位:', this.temp.positionName, '职位ID:', this.temp.positionId)
        }
      } else {
        this.temp.positionName = ''
      }
    },

    // 处理对话框关闭
    handleDialogClose() {
      // 清理员工编号关联组件状态
      this.$nextTick(() => {
        if (this.$refs['employeesIdSelect']) {
          this.$refs['employeesIdSelect'].reset()
        }
      })
    },

    // 获取部门名称
    getDepartmentName(row) {
      try {
        console.log('🔧 getDepartmentName 被调用，输入数据:', row)
        
        // 优先级1: 如果部门是对象且有dept_name
        if (row.department && typeof row.department === 'object') {
          const deptName = row.department.dept_name || row.department.name
          console.log('📋 从 department 对象获取部门名称:', deptName)
          if (deptName) {
            return deptName
          }
        }

        // 优先级2: 如果部门是字符串但看起来像JSON，尝试解析
        if (typeof row.department === 'string' && row.department.startsWith('{')) {
          try {
            const deptObj = JSON.parse(row.department)
            const deptName = deptObj.dept_name || deptObj.name
            console.log('📋 从 JSON 字符串解析部门名称:', deptName)
            if (deptName) {
              return deptName
            }
          } catch (parseError) {
            console.warn('⚠️ JSON 解析失败:', parseError)
          }
        }

        // 优先级3: 直接字段查找
        const fallbackName = row.dept_name || row.department || row.dept_code
        console.log('📋 使用后备部门名称:', fallbackName)
        
        return fallbackName || '未分配'
      } catch (error) {
        console.error('❌ 解析部门数据失败:', error, '行数据:', row)
        return '数据错误'
      }
    },

    // 调试行数据
    debugRowData(row) {
      console.log('🔍 完整行数据调试:', row)
      
      // 专门分析部门数据
      console.log('📋 部门数据详细分析:')
      console.log('  - row.department:', row.department)
      console.log('  - row.department 类型:', typeof row.department)
      
      if (row.department) {
        console.log('  - row.department.dept_name:', row.department.dept_name)
        console.log('  - row.department.id:', row.department.id)
        console.log('  - row.department.dept_code:', row.department.dept_code)
        
        // 检查字符串编码
        if (row.department.dept_name) {
          console.log('  - 部门名称字符长度:', row.department.dept_name.length)
          console.log('  - 部门名称字符编码:', [...row.department.dept_name].map(char => char.charCodeAt(0)))
          console.log('  - 部门名称 UTF-8 bytes:', new TextEncoder().encode(row.department.dept_name))
        }
      }
      
      // 其他部门相关字段
      const deptFields = {
        dept_name: row.dept_name,
        department: row.department,
        dept_code: row.dept_code,
        dept_id: row.dept_id,
        department_id: row.department_id
      }
      console.log('📋 所有部门相关字段:', deptFields)
      
      // 尝试通过getDepartmentName函数获取结果
      const departmentNameResult = this.getDepartmentName(row)
      console.log('🔧 getDepartmentName 函数结果:', departmentNameResult)
      
      this.$message.info(`部门调试信息已输出到控制台，请按F12查看。部门名称: ${row.department?.dept_name || '无'}`)
    },

    // 格式化性别显示
    formatGender(gender) {
      const genderMap = {
        'male': '男',
        'female': '女',
        'other': '其他'
      }
      return genderMap[gender] || gender || '未设置'
    },

    // 格式化状态显示
    formatStatus(status) {
      const statusMap = {
        'active': '在职',
        'inactive': '离职'
      }
      return statusMap[status] || status || '未设置'
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}

/* 支持缅甸语字体显示 */
.el-tag, .el-table, .el-cascader, .el-cascader-panel, .el-cascader-node, .el-select-dropdown {
  font-family: 'Noto Sans Myanmar', 'Myanmar Fallback', 'Myanmar Text', 'Myanmar3', 'Pyidaungsu', 'Padauk', 'Microsoft Himalaya', sans-serif;
}

/* 确保级联选择器下拉列表也使用正确字体 */
::v-deep .el-cascader-panel .el-cascader-node__label,
::v-deep .el-cascader__suggestion-item,
::v-deep .el-cascader-menu .el-cascader-node {
  font-family: 'Noto Sans Myanmar', 'Myanmar Fallback', 'Myanmar Text', 'Myanmar3', 'Pyidaungsu', 'Padauk', 'Microsoft Himalaya', sans-serif !important;
  text-rendering: optimizeLegibility;
}

/* 确保缅甸语文字正确渲染 */
span[title] {
  unicode-bidi: embed;
  direction: ltr;
  font-variant-ligatures: normal;
  text-rendering: optimizeLegibility;
}

/* 调试按钮样式 */
.debug-btn {
  opacity: 0.6;
  transition: opacity 0.3s;
}

.debug-btn:hover {
  opacity: 1;
}
</style>
