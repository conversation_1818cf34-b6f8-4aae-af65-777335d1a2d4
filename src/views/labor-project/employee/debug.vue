<template>
  <div class="app-container">
    <h2>员工管理 - 调试模式</h2>

    <!-- 调试信息 -->
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>调试信息</span>
      </div>
      <div>
        <p><strong>API状态:</strong> {{ apiStatus }}</p>
        <p><strong>错误信息:</strong> {{ errorMessage }}</p>
        <p><strong>数据数量:</strong> {{ list.length }}</p>
        <p><strong>请求URL:</strong> {{ requestUrl }}</p>
        <el-button type="primary" size="small" @click="testApiConnection">测试API连接</el-button>
        <el-button type="success" size="small" @click="testCreateEmployee">测试创建员工</el-button>
      </div>
    </el-card>

    <!-- 原始数据显示 -->
    <el-card v-if="rawData" style="margin-bottom: 20px;">
      <div slot="header">
        <span>原始API响应</span>
      </div>
      <pre>{{ JSON.stringify(rawData, null, 2) }}</pre>
    </el-card>

    <!-- 员工列表 -->
    <el-card>
      <div slot="header">
        <span>员工列表</span>
        <el-button type="primary" size="small" style="float: right;" @click="getList">刷新</el-button>
      </div>

      <div v-if="listLoading" style="text-align: center; padding: 20px;">
        <i class="el-icon-loading" /> 加载中...
      </div>

      <div v-else-if="list.length === 0" style="text-align: center; padding: 20px;">
        <p>暂无员工数据</p>
      </div>

      <el-table v-else :data="list" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="phone" label="电话" width="150" />
        <el-table-column prop="department" label="部门" width="150" />
        <el-table-column prop="position" label="职位" width="150" />
        <el-table-column prop="hire_date" label="入职日期" width="120">
          <template slot-scope="{row}">
            <span>{{ formatDate(row.hire_date) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="{row}">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '在职' : (row.status === 'inactive' ? '离职' : row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getEmployeeList, createEmployee } from '@/api/labor-project/employees'

export default {
  name: 'EmployeeDebug',
  data() {
    return {
      list: [],
      listLoading: false,
      apiStatus: '未知',
      errorMessage: '',
      rawData: null,
      requestUrl: '/labor-project/api/v1/employees'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.listLoading = true
      this.apiStatus = '请求中...'
      this.errorMessage = ''
      this.rawData = null

      try {
        console.log('🔍 开始获取员工列表...')
        const response = await getEmployeeList({ page: 1, limit: 10 })
        console.log('✅ API响应:', response)

        this.rawData = response
        this.apiStatus = '成功'

        // 处理不同的响应格式
        if (response && response.data && Array.isArray(response.data.employees)) {
          // 标准API响应格式：{ data: { employees: [...], pagination: {...} } }
          this.list = response.data.employees
        } else if (Array.isArray(response)) {
          this.list = response
        } else if (response && Array.isArray(response.data)) {
          this.list = response.data
        } else if (response && response.list) {
          this.list = response.list
        } else {
          this.list = []
        }

        console.log('📋 处理后的员工列表:', this.list)
      } catch (error) {
        console.error('❌ 获取员工列表失败:', error)
        this.apiStatus = '失败'
        this.errorMessage = error.message || '未知错误'
        this.rawData = { error: error.message }
        this.list = []
      } finally {
        this.listLoading = false
      }
    },

    async testApiConnection() {
      console.log('🔗 测试API连接...')
      try {
        const response = await fetch(this.requestUrl)
        console.log('🌐 Fetch响应状态:', response.status)

        if (response.ok) {
          const data = await response.json()
          console.log('📦 Fetch响应数据:', data)
          this.$message.success('API连接成功')
        } else {
          console.error('❌ HTTP错误:', response.status, response.statusText)
          this.$message.error(`API连接失败: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        console.error('❌ 网络错误:', error)
        this.$message.error('网络连接错误: ' + error.message)
      }
    },

    async testCreateEmployee() {
      console.log('➕ 测试创建员工...')
      try {
        const testData = {
          name: '测试员工' + Date.now(),
          email: 'test' + Date.now() + '@example.com',
          phone: '13800138000',
          department: '技术部',
          position: '测试工程师',
          hire_date: '2024-01-15',
          salary_type_id: 1
        }

        const response = await createEmployee(testData)
        console.log('✅ 创建员工成功:', response)
        this.$message.success(response.message || '员工创建成功')

        // 刷新列表
        this.getList()
      } catch (error) {
        console.error('❌ 创建员工失败:', error)
        this.$message.error('创建员工失败: ' + error.message)
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    // 格式化货币
    formatCurrency(amount) {
      if (!amount && amount !== 0) return ''
      return '¥' + amount.toLocaleString()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
}
</style>
