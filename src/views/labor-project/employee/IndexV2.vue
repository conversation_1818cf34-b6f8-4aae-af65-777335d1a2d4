<template>
  <div class="app-container">
    <!-- 筛选器组件 -->
    <EmployeeFilter
      :query="listQuery"
      :department-options="departmentOptions"
      :salary-type-options="salaryTypeOptions"
      :download-loading="downloadLoading"
      @filter="handleFilter"
      @create="handleCreate"
      @download="handleDownload"
      @debug="handleDebug"
    />

    <!-- 调试信息组件 -->
    <EmployeeDebugAlert
      :show="list.length === 0 && !listLoading"
      @test-api="testApiConnection"
      @debug="handleDebug"
    />

    <!-- 员工表格组件 -->
    <EmployeeTable
      :data="list"
      :loading="listLoading"
      :format-gender="formatGender"
      :format-currency="formatCurrency"
      :get-salary-type-tag-color="getSalaryTypeTagColor"
      :get-salary-type-label="getSalaryTypeLabel"
      :get-department-name="getDepartmentName"
      @edit="handleUpdate"
      @view="handleView"
      @delete="handleDelete"
    />

    <!-- 分页组件 -->
    <pagination 
      v-show="total > 0" 
      :total="total" 
      :page.sync="listQuery.page" 
      :limit.sync="listQuery.limit" 
      @pagination="getList" 
    />

    <!-- 员工表单组件 -->
    <EmployeeForm
      :visible.sync="dialogFormVisible"
      :dialog-title="dialogTitle()"
      :form-data="temp"
      :rules="rules"
      :department-options="departmentOptions"
      :position-options="positionOptions"
      :salary-type-options="salaryTypeOptions"
      ref="employeeForm"
      @submit="submitForm"
      @close="closeDialog"
      @department-change="handleDepartmentChange"
      @position-change="handlePositionChange"
      @employees-id-change="handleEmployeesIdChange"
      @employees-id-select="handleEmployeesIdSelect"
    />

    <!-- 员工详情组件 -->
    <EmployeeDetail
      :visible.sync="dialogViewVisible"
      :data="viewData"
      :format-gender="formatGender"
      :format-status="formatStatus"
      :get-salary-type-tag-color="getSalaryTypeTagColor"
      :get-salary-type-label="getSalaryTypeLabel"
    />
  </div>
</template>

<script>
import { onMounted, ref } from 'vue'
import Pagination from '@/components/Pagination'
import EmployeeFilter from './components/EmployeeFilter.vue'
import EmployeeTable from './components/EmployeeTable.vue'
import EmployeeForm from './components/EmployeeForm.vue'
import EmployeeDetail from './components/EmployeeDetail.vue'
import EmployeeDebugAlert from './components/EmployeeDebugAlert.vue'
import { useEmployee } from './composables/useEmployee'
import { useEmployeeForm } from './composables/useEmployeeForm'

export default {
  name: 'EmployeeManagementV2',
  components: {
    Pagination,
    EmployeeFilter,
    EmployeeTable,
    EmployeeForm,
    EmployeeDetail,
    EmployeeDebugAlert
  },
  setup() {
    // 使用员工相关的 composable
    const {
      list,
      total,
      listLoading,
      downloadLoading,
      listQuery,
      departmentOptions,
      positionOptions,
      salaryTypeOptions,
      getList,
      getEmployeeDetail,
      deleteEmployeeById,
      loadPositionsByDepartment,
      findDepartmentName,
      initializeData,
      formatGender,
      formatStatus,
      formatCurrency,
      getSalaryTypeTagColor,
      getSalaryTypeLabel,
      getDepartmentName
    } = useEmployee()

    // 使用表单相关的 composable
    const {
      dialogFormVisible,
      dialogStatus,
      temp,
      rules,
      dataFormRef,
      employeesIdSelectRef,
      dialogTitle,
      openCreateDialog,
      openEditDialog,
      closeDialog,
      submitForm,
      handleDepartmentChange: formHandleDepartmentChange,
      handlePositionChange: formHandlePositionChange,
      handleEmployeesIdChange,
      handleEmployeesIdSelect
    } = useEmployeeForm()

    // 查看详情相关状态
    const dialogViewVisible = ref(false)
    const viewData = ref({})
    const employeeFormRef = ref(null)

    // 处理筛选
    const handleFilter = (query) => {
      Object.assign(listQuery, query)
      listQuery.page = 1
      getList()
    }

    // 处理创建
    const handleCreate = async () => {
      await openCreateDialog()
      setTimeout(() => {
        if (employeeFormRef.value) {
          employeeFormRef.value.clearValidate()
          employeeFormRef.value.resetEmployeesIdSelect()
        }
      }, 100)
    }

    // 处理编辑
    const handleUpdate = async (row) => {
      try {
        console.log('🔍 开始编辑员工，ID:', row.id)
        const employeeData = await getEmployeeDetail(row.id)
        await openEditDialog(employeeData, loadPositionsByDepartment)
        
        setTimeout(() => {
          if (employeeFormRef.value) {
            employeeFormRef.value.clearValidate()
            employeeFormRef.value.forceReloadEmployeesIdSelect()
          }
        }, 100)
      } catch (error) {
        console.error('❌ 获取员工详情失败:', error)
        this.$message.error('获取员工详情失败: ' + error.message)
        
        // 如果API调用失败，降级使用行数据
        const employeeData = {
          id: row.id,
          name: row.name,
          email: row.email,
          phone: row.phone,
          gender: row.gender,
          department: row.department ? row.department.dept_name : (row.department || ''),
          department_id: row.department_id || (row.department ? row.department.id : null),
          position_id: row.position_id || (row.position_info ? row.position_info.id : null),
          position_info: row.position_info,
          position_name: row.position_name,
          salary_type_id: row.salary_type_id || (row.salary_type ? row.salary_type.id : 1),
          salary_type: row.salary_type,
          status: row.status,
          id_card: row.id_card || row.idCard,
          address: row.address,
          remark: row.remark,
          employees_id: row.employees_id || row.employeesId || '',
          employees_name: row.employees_name || row.employeesName || ''
        }
        
        await openEditDialog(employeeData, loadPositionsByDepartment)
        
        setTimeout(() => {
          if (employeeFormRef.value) {
            employeeFormRef.value.clearValidate()
            employeeFormRef.value.forceReloadEmployeesIdSelect()
          }
        }, 100)
      }
    }

    // 处理查看详情
    const handleView = (row) => {
      viewData.value = {
        id: row.id,
        name: row.name || '',
        email: row.email || '',
        phone: row.phone || '',
        gender: row.gender,
        department: row.department ? row.department.dept_name : (row.department || ''),
        positionName: row.position_info ? row.position_info.position_name : (row.position_name || ''),
        salaryTypeId: row.salary_type_id || (row.salary_type ? row.salary_type.id : 1),
        status: row.status,
        idCard: row.id_card || row.idCard,
        address: row.address,
        remark: row.remark,
        employeesId: row.employees_id || row.employeesId || '',
        employeesName: row.employees_name || row.employeesName || ''
      }
      dialogViewVisible.value = true
    }

    // 处理删除
    const handleDelete = async ({ row, index }) => {
      try {
        const response = await deleteEmployeeById(row.id)
        this.$notify({
          title: '成功',
          message: response.message || '员工删除成功',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        console.error('删除员工失败:', error)
        this.$message.error('删除员工失败: ' + error.message)
      }
    }

    // 处理表单提交
    const handleFormSubmit = async () => {
      try {
        const response = await submitForm()
        this.$notify({
          title: '成功',
          message: response.message || (dialogStatus.value === 'create' ? '员工创建成功' : '员工信息更新成功'),
          type: 'success',
          duration: 2000
        })
        // 刷新列表
        await getList()
      } catch (error) {
        console.error('提交表单失败:', error)
        this.$message.error('操作失败: ' + error.message)
      }
    }

    // 处理部门变化
    const handleDepartmentChange = async (value) => {
      await formHandleDepartmentChange(value, findDepartmentName, departmentOptions, loadPositionsByDepartment)
    }

    // 处理职位变化
    const handlePositionChange = (value) => {
      formHandlePositionChange(value, positionOptions)
    }

    // 处理下载
    const handleDownload = () => {
      downloadLoading.value = true
      // 模拟下载
      setTimeout(() => {
        downloadLoading.value = false
        this.$notify({
          title: '成功',
          message: '导出成功',
          type: 'success',
          duration: 2000
        })
      }, 1000)
    }

    // 处理调试
    const handleDebug = () => {
      this.$router.push('/labor-project/employee-debug')
    }

    // 测试API连接
    const testApiConnection = async () => {
      try {
        const response = await fetch('/labor-project/api/v1/employees')
        if (response.ok) {
          const data = await response.json()
          this.$message.success('API连接成功，数据已获取')
          console.log('API测试成功:', data)
        } else {
          this.$message.error(`API连接失败: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        this.$message.error('网络连接错误: ' + error.message)
        console.error('API测试失败:', error)
      }
    }

    // 组件挂载时初始化
    onMounted(async () => {
      await initializeData()
      await getList()
    })

    return {
      // 响应式数据
      list,
      total,
      listLoading,
      downloadLoading,
      listQuery,
      departmentOptions,
      positionOptions,
      salaryTypeOptions,
      dialogFormVisible,
      dialogStatus,
      temp,
      rules,
      dialogViewVisible,
      viewData,
      employeeFormRef,
      
      // 方法
      getList,
      handleFilter,
      handleCreate,
      handleUpdate,
      handleView,
      handleDelete,
      handleFormSubmit: handleFormSubmit,
      submitForm: handleFormSubmit,
      closeDialog,
      handleDepartmentChange,
      handlePositionChange,
      handleEmployeesIdChange,
      handleEmployeesIdSelect,
      handleDownload,
      handleDebug,
      testApiConnection,
      dialogTitle,
      
      // 格式化方法
      formatGender,
      formatStatus,
      formatCurrency,
      getSalaryTypeTagColor,
      getSalaryTypeLabel,
      getDepartmentName
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style> 