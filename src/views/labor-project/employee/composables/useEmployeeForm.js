import { ref, reactive, nextTick } from 'vue'
import { createEmployee, updateEmployee } from '@/api/labor-project/employees'
import { DEFAULT_EMPLOYEE_FORM, FORM_RULES, DIALOG_TITLE_MAP } from '../constants/employee'

export function useEmployeeForm() {
  // 表单相关状态
  const dialogFormVisible = ref(false)
  const dialogStatus = ref('')
  const temp = reactive({ ...DEFAULT_EMPLOYEE_FORM })
  
  // 表单验证规则
  const rules = reactive(FORM_RULES)
  
  // 表单引用
  const dataFormRef = ref(null)
  const employeesIdSelectRef = ref(null)

  /**
   * 重置表单数据
   */
  const resetTemp = () => {
    Object.assign(temp, DEFAULT_EMPLOYEE_FORM)
  }

  /**
   * 映射员工数据到表单
   */
  const mapEmployeeToForm = (employeeData) => {
    Object.assign(temp, {
      id: employeeData.id,
      name: employeeData.name || '',
      email: employeeData.email || '',
      phone: employeeData.phone || '',
      gender: employeeData.gender || '',
      department: employeeData.department ? employeeData.department.dept_name : (employeeData.department || ''),
      departmentId: employeeData.department_id || (employeeData.department ? employeeData.department.id : null),
      departmentPath: employeeData.department_id ? [employeeData.department_id] : (employeeData.department && employeeData.department.id ? [employeeData.department.id] : []),
      positionId: employeeData.position_id || null,
      positionName: employeeData.position_info ? employeeData.position_info.position_name : (employeeData.position || employeeData.position_name || ''),
      salaryTypeId: employeeData.salary_type_id || (employeeData.salary_type ? employeeData.salary_type.id : 1),
      status: employeeData.status || 'active',
      idCard: employeeData.id_card || employeeData.idCard || '',
      address: employeeData.address || '',
      remark: employeeData.remark || '',
      employeesId: employeeData.employees_id || employeeData.employeesId || '',
      employeesName: employeeData.employees_name || employeeData.employeesName || (employeeData.name || '')
    })
  }

  /**
   * 转换表单数据为API格式
   */
  const mapFormToApiData = () => {
    return {
      name: temp.name,
      email: temp.email,
      phone: temp.phone,
      gender: temp.gender,
      department: temp.department,
      department_id: temp.departmentId,
      position_id: temp.positionId,
      salary_type_id: temp.salaryTypeId || 1,
      status: temp.status,
      id_card: temp.idCard,
      address: temp.address,
      remark: temp.remark,
      employees_id: temp.employeesId
    }
  }

  /**
   * 打开创建对话框
   */
  const openCreateDialog = async () => {
    resetTemp()
    dialogStatus.value = 'create'
    dialogFormVisible.value = true
    
    await nextTick(() => {
      if (dataFormRef.value) {
        dataFormRef.value.clearValidate()
      }
      if (employeesIdSelectRef.value) {
        employeesIdSelectRef.value.reset()
      }
    })
  }

  /**
   * 打开编辑对话框
   */
  const openEditDialog = async (employeeData, loadPositionsByDepartment) => {
    mapEmployeeToForm(employeeData)
    dialogStatus.value = 'update'
    dialogFormVisible.value = true
    
    await nextTick(async () => {
      if (dataFormRef.value) {
        dataFormRef.value.clearValidate()
      }
      if (employeesIdSelectRef.value) {
        employeesIdSelectRef.value.forceReload()
      }
      
      // 如果有部门ID，加载该部门的职位选项
      if (temp.departmentId && loadPositionsByDepartment) {
        console.log('🔄 加载部门职位选项，部门ID:', temp.departmentId)
        await loadPositionsByDepartment(temp.departmentId)
      }
    })
  }

  /**
   * 关闭对话框
   */
  const closeDialog = () => {
    dialogFormVisible.value = false
    nextTick(() => {
      if (employeesIdSelectRef.value) {
        employeesIdSelectRef.value.reset()
      }
    })
  }

  /**
   * 创建员工
   */
  const createData = async () => {
    return new Promise((resolve, reject) => {
      if (!dataFormRef.value) {
        reject(new Error('表单引用不存在'))
        return
      }

      dataFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const employeeData = mapFormToApiData()
            const response = await createEmployee(employeeData)
            console.log('✅ 创建员工响应:', response)
            
            dialogFormVisible.value = false
            resolve(response)
          } catch (error) {
            console.error('❌ 创建员工失败:', error)
            reject(error)
          }
        } else {
          reject(new Error('表单验证失败'))
        }
      })
    })
  }

  /**
   * 更新员工
   */
  const updateData = async () => {
    return new Promise((resolve, reject) => {
      if (!dataFormRef.value) {
        reject(new Error('表单引用不存在'))
        return
      }

      dataFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const employeeData = mapFormToApiData()
            const response = await updateEmployee(temp.id, employeeData)
            console.log('✅ 更新员工响应:', response)
            
            dialogFormVisible.value = false
            resolve(response)
          } catch (error) {
            console.error('❌ 更新员工失败:', error)
            reject(error)
          }
        } else {
          reject(new Error('表单验证失败'))
        }
      })
    })
  }

  /**
   * 处理部门选择变化
   */
  const handleDepartmentChange = async (value, findDepartmentName, departmentOptions, loadPositionsByDepartment) => {
    if (value) {
      temp.departmentId = value
      // 根据选中的部门ID找到部门名称
      const departmentName = findDepartmentName(departmentOptions.value, value)
      temp.department = departmentName
      console.log('部门选择变化:', { id: value, name: departmentName })
      
      // 清空当前选中的职位
      temp.positionId = null
      temp.positionName = ''
      
      // 根据部门ID获取该部门的岗位
      if (loadPositionsByDepartment) {
        await loadPositionsByDepartment(value)
      }
    } else {
      temp.departmentId = null
      temp.department = ''
      temp.positionId = null
      temp.positionName = ''
    }
  }

  /**
   * 处理职位选择变化
   */
  const handlePositionChange = (value, positionOptions) => {
    console.log('🔄 职位选择变化:', value)
    if (value) {
      const selectedPosition = positionOptions.value.find(option => option.value === value)
      if (selectedPosition) {
        temp.positionName = selectedPosition.label
        console.log('👔 设置职位:', temp.positionName, '职位ID:', temp.positionId)
      }
    } else {
      temp.positionName = ''
    }
  }

  /**
   * 处理员工编号关联变化
   */
  const handleEmployeesIdChange = (value) => {
    console.log('员工编号关联变化:', value)
    temp.employeesId = value
  }

  /**
   * 处理员工编号关联选择
   */
  const handleEmployeesIdSelect = (selectedItem) => {
    console.log('选择的员工编号项:', selectedItem)
    if (selectedItem) {
      temp.employeesName = selectedItem.name
      temp.employeesId = selectedItem.id
    } else {
      temp.employeesName = ''
    }
  }

  /**
   * 提交表单
   */
  const submitForm = async () => {
    if (dialogStatus.value === 'create') {
      return await createData()
    } else {
      return await updateData()
    }
  }

  return {
    // 响应式数据
    dialogFormVisible,
    dialogStatus,
    temp,
    rules,
    
    // 引用
    dataFormRef,
    employeesIdSelectRef,
    
    // 计算属性
    dialogTitle: () => DIALOG_TITLE_MAP[dialogStatus.value] || '员工信息',
    
    // 方法
    resetTemp,
    mapEmployeeToForm,
    mapFormToApiData,
    openCreateDialog,
    openEditDialog,
    closeDialog,
    createData,
    updateData,
    submitForm,
    handleDepartmentChange,
    handlePositionChange,
    handleEmployeesIdChange,
    handleEmployeesIdSelect
  }
} 