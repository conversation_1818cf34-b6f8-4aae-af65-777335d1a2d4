import { ref, reactive } from 'vue'
import { 
  getEmployeeList, 
  getEmployeeListV2, 
  deleteEmployee, 
  getEmployeeDetail, 
  getEmployeeDetailV2 
} from '@/api/labor-project/employees'
import { getSalaryTypeList } from '@/api/labor-project/salaries'
import { getDepartmentTree } from '@/api/labor-project/departments'
import { getPositionOptions, getPositionsByDepartment } from '@/api/labor-project/positions'
import { 
  DEFAULT_SALARY_TYPE_OPTIONS, 
  GENDER_MAP, 
  STATUS_MAP, 
  SALARY_TYPE_COLOR_MAP 
} from '../constants/employee'

export function useEmployee() {
  // 响应式数据
  const list = ref([])
  const total = ref(0)
  const listLoading = ref(true)
  const downloadLoading = ref(false)
  
  // 选项数据
  const departmentOptions = ref([])
  const positionOptions = ref([])
  const salaryTypeOptions = ref([...DEFAULT_SALARY_TYPE_OPTIONS])
  
  // 查询参数
  const listQuery = reactive({
    page: 1,
    limit: 20,
    department: undefined,
    departmentId: undefined,
    status: undefined,
    salaryTypeId: undefined
  })

  /**
   * 获取员工列表
   */
  const getList = async () => {
    listLoading.value = true
    try {
      console.log('🔍 正在获取员工列表...')
      
      // 构建查询参数
      const queryParams = {
        page: listQuery.page,
        limit: listQuery.limit,
        department_id: listQuery.department || listQuery.departmentId,
        status: listQuery.status,
        salary_type_id: listQuery.salaryTypeId
      }
      
      // 移除空值参数
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === undefined || queryParams[key] === null || queryParams[key] === '') {
          delete queryParams[key]
        }
      })
      
      console.log('📤 请求参数:', queryParams)
      const response = await getEmployeeListV2(queryParams)
      console.log('✅ API响应:', response)

      if (response && response.success && response.data) {
        list.value = response.data.employees || []
        total.value = response.data.pagination ? response.data.pagination.total : list.value.length
        
        console.log('📋 处理后的员工列表:', list.value)
        console.log('📊 总数:', total.value)
      } else {
        console.warn('⚠️ 响应格式异常:', response)
        list.value = []
        total.value = 0
      }
    } catch (error) {
      console.error('❌ 获取员工列表失败:', error)
      throw error
    } finally {
      listLoading.value = false
    }
  }

  /**
   * 获取员工详情
   */
  const getEmployeeDetail = async (id) => {
    try {
      console.log('📝 获取员工详情，ID:', id)
      const response = await getEmployeeDetailV2(id)
      console.log('✅ 获取员工详情响应:', response)
      
      if (response && response.success && response.data) {
        return response.data
      } else if (response && response.data) {
        return response.data
      } else {
        throw new Error('获取员工详情失败')
      }
    } catch (error) {
      console.error('❌ 获取员工详情失败:', error)
      throw error
    }
  }

  /**
   * 删除员工
   */
  const deleteEmployeeById = async (id) => {
    try {
      const response = await deleteEmployee(id)
      console.log('✅ 删除员工响应:', response)
      await getList() // 刷新列表
      return response
    } catch (error) {
      console.error('❌ 删除员工失败:', error)
      throw error
    }
  }

  /**
   * 加载工资类型列表
   */
  const loadSalaryTypes = async () => {
    try {
      const response = await getSalaryTypeList({})
      console.log('✅ 工资类型列表响应:', response)

      if (response && response.data && Array.isArray(response.data)) {
        salaryTypeOptions.value = response.data.map(item => ({
          label: item.type_name,
          value: item.id
        }))
      }

      console.log('📋 工资类型选项:', salaryTypeOptions.value)
    } catch (error) {
      console.error('❌ 获取工资类型列表失败:', error)
      // 如果获取失败，使用默认值
      salaryTypeOptions.value = [...DEFAULT_SALARY_TYPE_OPTIONS]
    }
  }

  /**
   * 加载部门列表
   */
  const loadDepartments = async () => {
    try {
      const response = await getDepartmentTree({})
      console.log('✅ 部门列表响应:', response)

      if (response && response.data) {
        departmentOptions.value = buildDepartmentTree(response.data)
      }

      console.log('📋 部门选项:', departmentOptions.value)
    } catch (error) {
      console.error('❌ 获取部门列表失败:', error)
      // 如果获取失败，使用默认值
      departmentOptions.value = [
        {
          id: 1,
          name: '技术部',
          children: [
            { id: 11, name: '前端开发组', children: [] },
            { id: 12, name: '后端开发组', children: [] },
            { id: 13, name: '测试组', children: [] }
          ]
        },
        {
          id: 2,
          name: '销售部',
          children: [
            { id: 21, name: '华北销售组', children: [] },
            { id: 22, name: '华南销售组', children: [] }
          ]
        },
        { id: 3, name: '人事部', children: [] },
        { id: 4, name: '财务部', children: [] },
        { id: 5, name: '市场部', children: [] }
      ]
    }
  }

  /**
   * 构建部门树结构
   */
  const buildDepartmentTree = (departments) => {
    return departments.map(dept => ({
      id: dept.id,
      name: dept.dept_name || dept.name,
      children: dept.children && dept.children.length > 0 ? buildDepartmentTree(dept.children) : []
    }))
  }

  /**
   * 加载职位选项
   */
  const loadPositions = async () => {
    try {
      const response = await getPositionOptions()
      console.log('✅ 职位列表响应:', response)
      
      if (response && response.data) {
        positionOptions.value = response.data.map(position => ({
          value: position.id,
          label: position.position_name,
          department: position.department ? position.department.dept_name : '未知部门',
          positionCode: position.position_code
        }))
        console.log('📋 职位选项:', positionOptions.value)
      }
    } catch (error) {
      console.error('❌ 加载职位选项失败:', error)
      throw error
    }
  }

  /**
   * 根据部门ID加载职位选项
   */
  const loadPositionsByDepartment = async (departmentId) => {
    try {
      console.log('🔄 根据部门ID加载职位:', departmentId)
      const response = await getPositionsByDepartment(departmentId)
      console.log('✅ 部门职位列表响应:', response)
      
      if (response && response.data) {
        const positions = Array.isArray(response.data) ? response.data : []
        
        positionOptions.value = positions.map(position => ({
          value: position.id,
          label: position.position_name || position.name,
          department: position.department ? position.department.dept_name : '当前部门',
          positionCode: position.position_code || position.code
        }))
        
        console.log('📋 部门职位选项:', positionOptions.value)
      } else {
        positionOptions.value = []
      }
    } catch (error) {
      console.error('❌ 根据部门加载职位失败:', error)
      positionOptions.value = []
      throw error
    }
  }

  /**
   * 格式化性别显示
   */
  const formatGender = (gender) => {
    return GENDER_MAP[gender] || gender || '未设置'
  }

  /**
   * 格式化状态显示
   */
  const formatStatus = (status) => {
    return STATUS_MAP[status] || status || '未设置'
  }

  /**
   * 格式化货币
   */
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return '¥0'
    return '¥' + Number(amount).toLocaleString()
  }

  /**
   * 获取工资类型标签颜色
   */
  const getSalaryTypeTagColor = (salaryTypeId) => {
    const option = salaryTypeOptions.value.find(item => item.value === salaryTypeId)
    if (!option) return 'default'
    return SALARY_TYPE_COLOR_MAP[option.label] || 'default'
  }

  /**
   * 获取工资类型标签文本
   */
  const getSalaryTypeLabel = (salaryTypeId) => {
    const option = salaryTypeOptions.value.find(item => item.value === salaryTypeId)
    return option ? option.label : '未设置'
  }

  /**
   * 获取部门名称
   */
  const getDepartmentName = (row) => {
    try {
      if (row.department && typeof row.department === 'object') {
        return row.department.dept_name || row.department.name || '未分配'
      }

      if (typeof row.department === 'string' && row.department.startsWith('{')) {
        const deptObj = JSON.parse(row.department)
        return deptObj.dept_name || deptObj.name || '未分配'
      }

      return row.dept_name || row.department || row.dept_code || '未分配'
    } catch (error) {
      console.error('解析部门数据失败:', error)
      return '未分配'
    }
  }

  /**
   * 查找部门名称
   */
  const findDepartmentName = (departments, id) => {
    for (const dept of departments) {
      if (dept.id === id) {
        return dept.name
      }
      if (dept.children && dept.children.length > 0) {
        const childName = findDepartmentName(dept.children, id)
        if (childName) return childName
      }
    }
    return ''
  }

  /**
   * 初始化基础数据
   */
  const initializeData = async () => {
    await Promise.all([
      loadSalaryTypes(),
      loadDepartments(),
      loadPositions()
    ])
  }

  return {
    // 响应式数据
    list,
    total,
    listLoading,
    downloadLoading,
    listQuery,
    departmentOptions,
    positionOptions,
    salaryTypeOptions,
    
    // 方法
    getList,
    getEmployeeDetail,
    deleteEmployeeById,
    loadSalaryTypes,
    loadDepartments,
    loadPositions,
    loadPositionsByDepartment,
    findDepartmentName,
    initializeData,
    
    // 格式化方法
    formatGender,
    formatStatus,
    formatCurrency,
    getSalaryTypeTagColor,
    getSalaryTypeLabel,
    getDepartmentName
  }
} 