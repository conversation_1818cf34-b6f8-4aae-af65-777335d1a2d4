<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.employeeName" placeholder="员工姓名" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.department" placeholder="部门" clearable style="width: 200px" class="filter-item">
        <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="考勤状态" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        style="width: 300px"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        手动打卡
      </el-button>
      <el-button v-waves :loading="downloadLoading" class="filter-item" type="success" icon="el-icon-download" @click="handleDownload">
        导出考勤报表
      </el-button>
    </div>

    <!-- 考勤统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日出勤</span>
          </div>
          <div class="text item">
            <span class="number">{{ statistics.todayAttendance }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>迟到人数</span>
          </div>
          <div class="text item">
            <span class="number late">{{ statistics.lateCount }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>早退人数</span>
          </div>
          <div class="text item">
            <span class="number early">{{ statistics.earlyCount }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>旷工人数</span>
          </div>
          <div class="text item">
            <span class="number absent">{{ statistics.absentCount }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="员工编号" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employeeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employeeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.department }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考勤日期" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.attendanceDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上班时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.checkInTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下班时间" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.checkOutTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作时长" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.workHours }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考勤状态" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="打卡地点" min-width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.location }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.remark }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="info" @click="handleView(row)">
            查看
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row,$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 添加/编辑考勤记录对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 90%; margin-left:50px;">
        <el-form-item label="员工" prop="employeeId">
          <el-select v-model="temp.employeeId" placeholder="请选择员工" style="width: 100%">
            <el-option v-for="item in employeeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="考勤日期" prop="attendanceDate">
          <el-date-picker v-model="temp.attendanceDate" type="date" placeholder="请选择考勤日期" style="width: 100%" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上班时间" prop="checkInTime">
              <el-time-picker v-model="temp.checkInTime" placeholder="请选择上班时间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下班时间" prop="checkOutTime">
              <el-time-picker v-model="temp.checkOutTime" placeholder="请选择下班时间" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="考勤状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择考勤状态">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="打卡地点" prop="location">
          <el-input v-model="temp.location" placeholder="请输入打卡地点" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看考勤详情对话框 -->
    <el-dialog title="考勤详情" :visible.sync="dialogViewVisible" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工编号">{{ viewData.employeeId }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ viewData.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ viewData.department }}</el-descriptions-item>
        <el-descriptions-item label="考勤日期">{{ viewData.attendanceDate }}</el-descriptions-item>
        <el-descriptions-item label="上班时间">{{ viewData.checkInTime }}</el-descriptions-item>
        <el-descriptions-item label="下班时间">{{ viewData.checkOutTime }}</el-descriptions-item>
        <el-descriptions-item label="工作时长">{{ viewData.workHours }}</el-descriptions-item>
        <el-descriptions-item label="考勤状态">
          <el-tag :type="getStatusType(viewData.status)">
            {{ viewData.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="打卡地点" :span="2">{{ viewData.location }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'AttendanceManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        department: undefined,
        status: undefined,
        dateRange: []
      },
      statistics: {
        todayAttendance: 142,
        lateCount: 8,
        earlyCount: 3,
        absentCount: 2
      },
      departmentOptions: [
        { label: '技术部', value: '技术部' },
        { label: '销售部', value: '销售部' },
        { label: '人事部', value: '人事部' },
        { label: '财务部', value: '财务部' },
        { label: '市场部', value: '市场部' }
      ],
      statusOptions: [
        { label: '正常', value: '正常' },
        { label: '迟到', value: '迟到' },
        { label: '早退', value: '早退' },
        { label: '旷工', value: '旷工' },
        { label: '请假', value: '请假' }
      ],
      employeeOptions: [
        { label: '张三 (EMP001)', value: 'EMP001' },
        { label: '李四 (EMP002)', value: 'EMP002' },
        { label: '王五 (EMP003)', value: 'EMP003' }
      ],
      temp: {
        employeeId: '',
        attendanceDate: '',
        checkInTime: '',
        checkOutTime: '',
        status: '正常',
        location: '',
        remark: ''
      },
      dialogFormVisible: false,
      dialogViewVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑考勤记录',
        create: '新增考勤记录'
      },
      viewData: {},
      downloadLoading: false,
      rules: {
        employeeId: [{ required: true, message: '员工是必选项', trigger: 'change' }],
        attendanceDate: [{ required: true, message: '考勤日期是必选项', trigger: 'change' }],
        checkInTime: [{ required: true, message: '上班时间是必选项', trigger: 'change' }],
        checkOutTime: [{ required: true, message: '下班时间是必选项', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      // 模拟API调用
      setTimeout(() => {
        this.list = [
          {
            id: 1,
            employeeId: 'EMP001',
            employeeName: '张三',
            department: '技术部',
            attendanceDate: '2024-01-15',
            checkInTime: '09:00',
            checkOutTime: '18:00',
            workHours: '8小时',
            status: '正常',
            location: '公司大楼',
            remark: ''
          },
          {
            id: 2,
            employeeId: 'EMP002',
            employeeName: '李四',
            department: '销售部',
            attendanceDate: '2024-01-15',
            checkInTime: '09:15',
            checkOutTime: '18:00',
            workHours: '7小时45分',
            status: '迟到',
            location: '公司大楼',
            remark: '交通拥堵'
          },
          {
            id: 3,
            employeeId: 'EMP003',
            employeeName: '王五',
            department: '人事部',
            attendanceDate: '2024-01-15',
            checkInTime: '09:00',
            checkOutTime: '17:30',
            workHours: '7小时30分',
            status: '早退',
            location: '公司大楼',
            remark: '有事早退'
          }
        ]
        this.total = 3
        this.listLoading = false
      }, 1000)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row) {
      this.viewData = Object.assign({}, row)
      this.dialogViewVisible = true
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该考勤记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.list.splice(index, 1)
        this.total--
        this.$notify({
          title: '成功',
          message: '删除成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024
          this.list.unshift(this.temp)
          this.total++
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '创建成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          const index = this.list.findIndex(v => v.id === this.temp.id)
          this.list.splice(index, 1, tempData)
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    resetTemp() {
      this.temp = {
        employeeId: '',
        attendanceDate: '',
        checkInTime: '',
        checkOutTime: '',
        status: '正常',
        location: '',
        remark: ''
      }
    },
    handleDownload() {
      this.downloadLoading = true
      // 模拟下载
      setTimeout(() => {
        this.downloadLoading = false
        this.$notify({
          title: '成功',
          message: '导出成功',
          type: 'success',
          duration: 2000
        })
      }, 1000)
    },
    getStatusType(status) {
      const statusMap = {
        '正常': 'success',
        '迟到': 'warning',
        '早退': 'warning',
        '旷工': 'danger',
        '请假': 'info'
      }
      return statusMap[status]
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.box-card {
  text-align: center;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.number {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
}
.number.late {
  color: #E6A23C;
}
.number.early {
  color: #F56C6C;
}
.number.absent {
  color: #909399;
}
.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 5px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
