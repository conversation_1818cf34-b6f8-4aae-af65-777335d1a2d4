<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.dept_name" placeholder="部门名称" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        新增部门
      </el-button>
      <el-button class="filter-item" type="success" icon="el-icon-sort" @click="expandAll">
        展开/折叠
      </el-button>
      <el-button class="filter-item" type="info" icon="el-icon-download" @click="exportStructure">
        导出结构
      </el-button>
    </div>

    <!-- 部门统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>部门总数</span>
          </div>
          <div class="text item">
            <span class="number">{{ statistics.totalDepartments }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>一级部门</span>
          </div>
          <div class="text item">
            <span class="number">{{ statistics.firstLevelDepartments }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>在职员工</span>
          </div>
          <div class="text item">
            <span class="number active">{{ statistics.activeEmployees }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>部门负责人</span>
          </div>
          <div class="text item">
            <span class="number manager">{{ statistics.departmentManagers }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table
      ref="table"
      v-loading="listLoading"
      :data="list"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="部门名称" width="200">
        <template slot-scope="{row}">
          <span>{{ row.dept_name || row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门编码" width="150" align="center">
        <template slot-scope="{row}">
          <span>{{ row.dept_code || row.code }}</span>
        </template>
      </el-table-column>
      <el-table-column label="负责人" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.manager || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" width="140" align="center">
        <template slot-scope="{row}">
          <span>{{ row.phone || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="员工数量" width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employeeCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' || row.status === '启用' ? 'success' : 'danger'">
            {{ row.status === 'active' ? '启用' : row.status === 'inactive' ? '禁用' : row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createTime || row.createdAt | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="80" align="center">
        <template slot-scope="{row}">
          <span>{{ row.sort || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button type="success" size="mini" @click="handleAddChild(row)">
            添加子部门
          </el-button>
          <el-button type="info" size="mini" @click="viewEmployees(row)">
            员工
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑部门对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 90%; margin-left:50px;">
        <el-form-item label="上级部门">
          <el-cascader
            v-model="temp.parent_id"
            :options="departmentTree"
            :props="{ value: 'id', label: 'name', children: 'children', checkStrictly: true }"
            placeholder="请选择上级部门"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="部门名称" prop="dept_name">
          <el-input v-model="temp.dept_name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门编码" prop="dept_code">
          <el-input v-model="temp.dept_code" placeholder="请输入部门编码" />
        </el-form-item>
        <el-form-item label="负责人" prop="manager">
          <el-select v-model="temp.manager" placeholder="请选择负责人" style="width: 100%" filterable>
            <el-option v-for="item in employeeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="temp.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="temp.sort" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="temp.description" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" placeholder="请输入部门描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 员工列表对话框 -->
    <el-dialog title="部门员工" :visible.sync="employeeDialogVisible" width="800px">
      <el-table v-loading="employeeLoading" :data="employees" style="width: 100%">
        <el-table-column label="姓名" prop="name" width="120" />
        <el-table-column label="工号" prop="employeeId" width="120" />
        <el-table-column label="职位" prop="position" width="120" />
        <el-table-column label="联系电话" prop="phone" width="140" />
        <el-table-column label="邮箱" prop="email" width="180" />
        <el-table-column label="入职时间" prop="hireDate" width="120" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { parseTime } from '@/utils'
import {
  getDepartmentTree,
  getDepartmentList,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentEmployees,
  getAllDepartmentStats,
  exportDepartmentStructure,
  getDepartmentOptions
} from '@/api/labor-project/departments'

export default {
  name: 'DepartmentManagement',
  directives: { waves },
  filters: {
    parseTime
  },
  data() {
    return {
      list: [],
      listLoading: true,
      listQuery: {
        dept_name: undefined,
        status: undefined
      },
      statistics: {
        totalDepartments: 0,
        firstLevelDepartments: 0,
        activeEmployees: 0,
        departmentManagers: 0
      },
      statusOptions: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' }
      ],
      employeeOptions: [],
      employees: [],
      employeeLoading: false,
      employeeDialogVisible: false,
      departmentTree: [],
      temp: {
        id: undefined,
        parent_id: null,
        dept_name: '',
        dept_code: '',
        manager: '',
        phone: '',
        sort: 0,
        status: 'active',
        description: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑部门',
        create: '新增部门'
      },
      rules: {
        dept_name: [{ required: true, message: '部门名称是必填项', trigger: 'blur' }],
        dept_code: [{ required: true, message: '部门编码是必填项', trigger: 'blur' }]
      },
      expandAllFlag: false
    }
  },
  created() {
    this.getList()
    this.getStatistics()
    this.getEmployeeOptions()
  },
  methods: {
    async getList() {
      this.listLoading = true
      try {
        const response = await getDepartmentTree(this.listQuery)
        this.list = response.data || []
        this.buildDepartmentOptions()
      } catch (error) {
        console.error('获取部门列表失败:', error)
        this.$message.error('获取部门列表失败')
      } finally {
        this.listLoading = false
      }
    },

    async getStatistics() {
      try {
        const response = await getAllDepartmentStats()
        if (response.data) {
          this.statistics = response.data
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    },

    async getEmployeeOptions() {
      try {
        const response = await getDepartmentOptions()
        this.employeeOptions = response.data?.employees || []
      } catch (error) {
        console.error('获取员工选项失败:', error)
      }
    },

    buildDepartmentOptions() {
      const buildTree = (departments) => {
        return departments.map(dept => ({
          id: dept.id,
          name: dept.dept_name || dept.name,
          children: dept.children && dept.children.length > 0 ? buildTree(dept.children) : []
        }))
      }
      this.departmentTree = buildTree(this.list)
    },

    handleFilter() {
      this.getList()
    },

    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      // 处理字段名兼容性
      this.temp.dept_name = row.dept_name || row.name
      this.temp.dept_code = row.dept_code || row.code
      // 处理父部门ID
      if (row.parent_id || row.parentId) {
        this.temp.parent_id = [row.parent_id || row.parentId]
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    handleAddChild(row) {
      this.resetTemp()
      this.temp.parent_id = [row.id]
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    handleDelete(row) {
      this.$confirm('确定要删除该部门吗? 删除后子部门也将被删除!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await deleteDepartment(row.id)
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getStatistics()
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    async createData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 构建符合后端API的数据结构
            const data = {
              dept_code: this.temp.dept_code,
              dept_name: this.temp.dept_name,
              description: this.temp.description
            }

            // 只有选择了上级部门才添加parent_id字段
            if (this.temp.parent_id && this.temp.parent_id.length > 0) {
              data.parent_id = this.temp.parent_id[this.temp.parent_id.length - 1]
            }
            // 如果没有选择上级部门，不包含parent_id字段

            await createDepartment(data)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            this.getStatistics()
          } catch (error) {
            console.error('创建失败:', error)
            this.$message.error('创建失败')
          }
        }
      })
    },

    async updateData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 构建符合后端API的数据结构
            const data = {
              dept_code: this.temp.dept_code,
              dept_name: this.temp.dept_name,
              description: this.temp.description
            }

            // 只有选择了上级部门才添加parent_id字段
            if (this.temp.parent_id && this.temp.parent_id.length > 0) {
              data.parent_id = this.temp.parent_id[this.temp.parent_id.length - 1]
            }
            // 如果没有选择上级部门，不包含parent_id字段

            await updateDepartment(this.temp.id, data)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            this.getStatistics()
          } catch (error) {
            console.error('更新失败:', error)
            this.$message.error('更新失败')
          }
        }
      })
    },

    async viewEmployees(row) {
      this.employeeDialogVisible = true
      this.employeeLoading = true
      try {
        const response = await getDepartmentEmployees(row.id)
        this.employees = response.data || []
      } catch (error) {
        console.error('获取员工列表失败:', error)
        this.$message.error('获取员工列表失败')
      } finally {
        this.employeeLoading = false
      }
    },

    async exportStructure() {
      try {
        const response = await exportDepartmentStructure()
        // 处理文件下载
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.download = `部门结构_${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },

    resetTemp() {
      this.temp = {
        id: undefined,
        parent_id: null,
        dept_name: '',
        dept_code: '',
        manager: '',
        phone: '',
        sort: 0,
        status: 'active',
        description: ''
      }
    },

    expandAll() {
      this.expandAllFlag = !this.expandAllFlag
      this.toggleExpand(this.list, this.expandAllFlag)
    },

    toggleExpand(tree, expand) {
      tree.forEach(item => {
        this.$refs.table.toggleRowExpansion(item, expand)
        if (item.children && item.children.length > 0) {
          this.toggleExpand(item.children, expand)
        }
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.box-card {
  text-align: center;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.number {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
}
.number.active {
  color: #67C23A;
}
.number.manager {
  color: #E6A23C;
}
.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 5px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
