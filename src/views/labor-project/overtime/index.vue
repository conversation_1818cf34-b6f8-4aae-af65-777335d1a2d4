<template>
  <div class="app-container">
    <div class="header">
      <h1>加班管理</h1>
      <p>Overtime Management</p>
    </div>

    <div class="coming-soon">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>功能开发中</span>
        </div>
        <div class="content">
          <i class="el-icon-time" style="font-size: 64px; color: #409EFF;" />
          <h2>加班管理功能即将上线</h2>
          <p>敬请期待...</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OvertimeManagement'
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  h1 {
    color: #303133;
    margin-bottom: 10px;
  }
  p {
    color: #909399;
    font-size: 16px;
  }
}

.coming-soon {
  text-align: center;
  .content {
    padding: 40px 20px;
    h2 {
      color: #303133;
      margin: 20px 0 10px 0;
    }
    p {
      color: #909399;
      font-size: 16px;
    }
  }
}
</style>
