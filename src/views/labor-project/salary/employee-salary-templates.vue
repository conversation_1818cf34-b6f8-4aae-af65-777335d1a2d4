<template>
  <div class="app-container">
    <!-- 货币说明面板 -->
    <el-alert
      title="货币说明"
      type="info"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
      class="currency-info-panel"
    >
      <template slot="description">
        <div style="display: flex; gap: 30px; align-items: center;">
          <div style="display: flex; align-items: center;">
            <span style="color: #E6A23C; font-weight: bold; margin-right: 8px;">฿</span>
            <span>默认值使用<strong>泰币(THB)</strong></span>
          </div>
          <div style="display: flex; align-items: center;">
            <span style="color: #F56C6C; font-weight: bold; margin-right: 8px;">K</span>
            <span>子默认值使用<strong>缅币(MMK)</strong></span>
          </div>
        </div>
      </template>
    </el-alert>

    <!-- 页签切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="员工列表" name="employees">
        <div class="filter-container">
          <el-input v-model="employeeQuery.name" placeholder="员工姓名" style="width: 200px" class="filter-item" />
          <el-select v-model="employeeQuery.department" placeholder="部门" clearable style="width: 200px" class="filter-item">
            <el-option label="技术部" value="技术部" />
            <el-option label="销售部" value="销售部" />
            <el-option label="人事部" value="人事部" />
          </el-select>
          <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleEmployeeFilter">
            搜索
          </el-button>
        </div>

        <!-- 员工统计卡片 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="8">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">员工总数</div>
                <div class="card-value">{{ employeeStats.total }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">在职员工</div>
                <div class="card-value">{{ employeeStats.active }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">已设薪资模板</div>
                <div class="card-value">{{ employeeStats.withTemplate }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 员工表格 -->
        <el-table
          :key="employeeTableKey"
          v-loading="employeeLoading"
          :data="employeeList"
          border
          fit
          highlight-current-row
          style="width: 100%;"
          @row-click="handleEmployeeRowClick"
        >
          <el-table-column label="ID" width="60px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="邮箱" width="180px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.email }}</span>
            </template>
          </el-table-column>
          <el-table-column label="手机" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.phone }}</span>
            </template>
          </el-table-column>
          <el-table-column label="部门" width="100px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.department }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职位" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.position }}</span>
            </template>
          </el-table-column>
          <el-table-column label="基本工资" width="120px" align="center">
            <template slot-scope="{row}">
              <span class="salary-amount">¥{{ formatCurrency(row.base_salary) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80px" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="入职日期" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ formatDate(row.hire_date) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180px">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" @click="viewEmployeeTemplates(row)">
                查看薪资模板
              </el-button>
              <el-button type="success" size="mini" @click="createTemplateForEmployee(row)">
                创建模板
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="employeeTotal > 0"
          :total="employeeTotal"
          :page.sync="employeeQuery.page"
          :limit.sync="employeeQuery.limit"
          @pagination="getEmployeeList"
        />
      </el-tab-pane>

      <el-tab-pane label="薪资模板树" name="templates">
        <div class="filter-container">
          <el-select v-model="templateQuery.employee_id" placeholder="选择员工" clearable style="width: 200px" class="filter-item">
            <el-option v-for="item in employeeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="templateQuery.item_id" placeholder="薪资项目类型" clearable filterable style="width: 200px" class="filter-item">
            <el-option
              v-for="item in salaryItemTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 12px">{{ item.itemKey }}</span>
            </el-option>
          </el-select>
          <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleTemplateFilter">
            搜索
          </el-button>
          <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
            新增薪资模板
          </el-button>
        </div>

        <!-- 薪资模板统计 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">模板总数</div>
                <div class="card-value">{{ templateStats.total }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">涉及员工</div>
                <div class="card-value">{{ templateStats.employees }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">薪资项目类型</div>
                <div class="card-value">{{ templateStats.itemTypes }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="box-card">
              <div class="card-content">
                <div class="card-title">平均模板数/人</div>
                <div class="card-value">{{ templateStats.avgPerEmployee }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 薪资模板树状表格 -->
        <el-table
          :key="templateTableKey"
          v-loading="templateLoading"
          :data="templateTreeData"
          border
          fit
          highlight-current-row
          style="width: 100%;"
          row-key="id"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        >
          <el-table-column label="员工/模板" width="200px" align="left">
            <template slot-scope="{row}">
              <div v-if="row.type === 'employee'" class="employee-row">
                <i class="el-icon-user" />
                <span class="employee-name">{{ row.name }}</span>
                <el-tag size="mini" style="margin-left: 10px;">{{ row.department }}</el-tag>
              </div>
              <div v-else class="template-row">
                <i class="el-icon-document" />
                <span>{{ row.itemName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="薪资项目类型" width="150px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'">
                <el-tag :type="getItemTypeTagColor(row.itemType)">
                  {{ row.itemType }}
                </el-tag>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="默认值(泰币)" width="120px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'" class="default-value">
                <el-tooltip content="泰币(THB)" placement="top">
                  <span class="currency-display">{{ formatThaiCurrency(row.defaultValue) }}</span>
                </el-tooltip>
              </span>
              <span v-else-if="row.type === 'employee'" class="total-value">
                <el-tooltip content="泰币(THB)总计" placement="top">
                  <span class="currency-display">总计: {{ formatThaiCurrency(row.totalValue) }}</span>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="子默认值(缅币)" width="120px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'" class="sub-default-value">
                <el-tooltip content="缅币(MMK)" placement="top">
                  <span class="currency-display">{{ formatMyanmarCurrency(row.subDefaultValue) }}</span>
                </el-tooltip>
              </span>
              <span v-else-if="row.type === 'employee'" class="total-sub-value">
                <el-tooltip content="缅币(MMK)总计" placement="top">
                  <span class="currency-display">子项总计: {{ formatMyanmarCurrency(row.totalSubValue) }}</span>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="120px" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 'template'">{{ formatDate(row.createdAt) }}</span>
              <span v-else-if="row.type === 'employee'">{{ formatDate(row.hireDate) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200px">
            <template slot-scope="{row}">
              <div v-if="row.type === 'template'">
                <el-button type="primary" size="mini" @click="handleUpdate(row)">
                  编辑
                </el-button>
                <el-button size="mini" type="info" @click="handleView(row)">
                  查看
                </el-button>
                <el-button size="mini" type="danger" @click="handleDelete(row)">
                  删除
                </el-button>
              </div>
              <div v-else-if="row.type === 'employee'">
                <el-button type="success" size="mini" @click="createTemplateForEmployee(row)">
                  添加薪资项目
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="templateTotal > 0"
          :total="templateTotal"
          :page.sync="templateQuery.page"
          :limit.sync="templateQuery.limit"
          @pagination="getTemplateList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑薪资模板对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <!-- 调试信息面板 (开发时使用) -->
      <el-collapse v-if="false" style="margin-bottom: 20px;">
        <el-collapse-item title="调试信息" name="debug">
          <div style="font-size: 12px; color: #666;">
            <p><strong>当前temp数据:</strong> {{ JSON.stringify(temp, null, 2) }}</p>
            <p><strong>员工选项数量:</strong> {{ employeeOptions.length }}</p>
            <p><strong>员工选项:</strong> {{ JSON.stringify(employeeOptions.slice(0, 3), null, 2) }}</p>
            <p><strong>薪资项目选项数量:</strong> {{ salaryItemTypeOptions.length }}</p>
            <p><strong>对话框状态:</strong> {{ dialogStatus }}</p>
          </div>
        </el-collapse-item>
      </el-collapse>

      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="120px" style="width: 90%; margin-left:50px;">
        <el-form-item label="员工" prop="employee_id">
          <el-select
            v-model="temp.employee_id"
            placeholder="请选择员工"
            style="width: 100%"
            filterable
            clearable
            :disabled="dialogStatus === 'update' || isEmployeePreset"
            @change="handleEmployeeChange"
          >
            <el-option
              v-for="item in employeeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ item.value }}</span>
            </el-option>
          </el-select>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            <span v-if="dialogStatus === 'create' && !isEmployeePreset">当前选中的员工ID: {{ temp.employee_id }}</span>
            <span v-else-if="dialogStatus === 'create' && isEmployeePreset" class="readonly-tip">已为指定员工创建模板，员工信息不可修改</span>
            <span v-else-if="dialogStatus === 'update'" class="readonly-tip">编辑模式下员工信息不可修改</span>
          </div>
        </el-form-item>
        <el-form-item label="薪资项目类型" prop="item_id">
          <el-select
            v-model="temp.item_id"
            placeholder="请选择薪资项目类型"
            style="width: 100%"
            :disabled="dialogStatus === 'update'"
            filterable
          >
            <el-option
              v-for="item in salaryItemTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 12px">{{ item.itemKey }}</span>
            </el-option>
          </el-select>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            <span v-if="dialogStatus === 'update'" class="readonly-tip">编辑模式下薪资项目类型不可修改</span>
            <span v-else>支持搜索薪资项目类型名称</span>
          </div>
        </el-form-item>
        <el-form-item label="默认值(泰币)" prop="default_value">
          <el-input-number
            v-model="temp.default_value"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入默认值（泰币）"
            class="thai-currency-input"
          >
            <template slot="prepend">฿</template>
          </el-input-number>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            该值将作为该员工此薪资项目的默认金额（泰币฿）
          </div>
        </el-form-item>
        <el-form-item label="子默认值(缅币)" prop="sub_default_value">
          <el-input-number
            v-model="temp.sub_default_value"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入子默认值（缅币）"
            class="myanmar-currency-input"
          >
            <template slot="append">K</template>
          </el-input-number>
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            该值将作为该员工此薪资项目的子项默认金额（缅币K）
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看薪资模板详情对话框 -->
    <el-dialog title="薪资模板详情" :visible.sync="dialogViewVisible" width="600px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="ID">{{ viewData.id }}</el-descriptions-item>
        <el-descriptions-item label="员工姓名">{{ viewData.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="员工编号">{{ viewData.employeeId }}</el-descriptions-item>
        <el-descriptions-item label="薪资项目类型">
          <el-tag :type="getItemTypeTagColor(viewData.item_id)">
            {{ viewData.itemTypeName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="默认值(泰币)">
          <span class="default-value">
            <el-tooltip content="泰币(THB) - Thai Baht" placement="top">
              <span class="currency-display">{{ formatThaiCurrency(viewData.default_value) }}</span>
            </el-tooltip>
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="子默认值(缅币)">
          <span class="sub-default-value">
            <el-tooltip content="缅币(MMK) - Myanmar Kyat" placement="top">
              <span class="currency-display">{{ formatMyanmarCurrency(viewData.sub_default_value) }}</span>
            </el-tooltip>
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewData.created_at }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewData.updated_at }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import {
  getEmployeeSalaryTemplateList,
  createEmployeeSalaryTemplate,
  updateEmployeeSalaryTemplate,
  deleteEmployeeSalaryTemplate,
  getEmployeeSalaryTemplateDetail
} from '@/api/labor-project/salaries'
import { getEmployeeList } from '@/api/labor-project/employees'
import { getSalaryItemTypeList } from '@/api/labor-project/salaries'

export default {
  name: 'EmployeeSalaryTemplateManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      activeTab: 'employees', // 默认显示员工列表
      employeeTableKey: 0,
      employeeLoading: true,
      employeeList: [],
      employeeTotal: 0,
      employeeQuery: {
        page: 1,
        limit: 20,
        name: undefined,
        department: undefined
      },
      employeeStats: {
        total: 0,
        active: 0,
        withTemplate: 0
      },
      selectedEmployee: null, // 当前选中的员工

      templateTableKey: 0,
      templateLoading: true,
      templateTreeData: [],
      templateTotal: 0,
      templateQuery: {
        page: 1,
        limit: 20,
        employee_id: undefined,
        item_id: undefined
      },
      templateStats: {
        total: 0,
        employees: 0,
        itemTypes: 0,
        avgPerEmployee: 0
      },

      employeeOptions: [],
      salaryItemTypeOptions: [],
      temp: {
        id: undefined,
        employee_id: undefined,
        item_id: undefined,
        default_value: 0,
        sub_default_value: 0
      },
      dialogFormVisible: false,
      dialogViewVisible: false,
      dialogStatus: '',
      isEmployeePreset: false, // 标识是否预设了员工
      textMap: {
        update: '编辑薪资模板',
        create: '新增薪资模板'
      },
      viewData: {},
      rules: {
        employee_id: [
          { required: true, message: '员工是必选项', trigger: 'change' },
          { type: 'number', message: '请选择有效的员工', trigger: 'change' }
        ],
        item_id: [
          { required: true, message: '薪资项目类型是必选项', trigger: 'change' },
          { type: 'number', message: '请选择有效的薪资项目类型', trigger: 'change' }
        ],
        default_value: [
          { required: true, message: '默认值是必填项', trigger: 'blur' },
          { type: 'number', min: 0, message: '默认值必须是大于等于0的数字', trigger: 'blur' }
        ],
        sub_default_value: [
          { required: true, message: '子默认值是必填项', trigger: 'blur' },
          { type: 'number', min: 0, message: '子默认值必须是大于等于0的数字', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    console.log('🚀 页面初始化开始...')

    // 先加载基础数据
    this.loadEmployeeOptions()
    this.loadSalaryItemTypeOptions()

    // 然后根据当前标签页加载相应数据
    if (this.activeTab === 'employees') {
      this.getEmployeeList()
    } else if (this.activeTab === 'templates') {
      this.getTemplateList()
    }

    console.log('🚀 页面初始化完成')
  },
  methods: {
    async getEmployeeList() {
      this.employeeLoading = true
      try {
        console.log('🔍 正在获取员工列表...')
        const response = await getEmployeeList(this.employeeQuery)
        console.log('✅ 员工列表响应:', response)

        if (response && response.data) {
          this.employeeList = response.data.employees || []
          this.employeeTotal = response.data.pagination ? response.data.pagination.total : this.employeeList.length
        } else {
          this.employeeList = []
          this.employeeTotal = 0
        }

        console.log('📋 处理后的员工列表:', this.employeeList)
        console.log('📊 员工总数:', this.employeeTotal)

        this.updateEmployeeStats()
      } catch (error) {
        console.error('❌ 获取员工列表失败:', error)
        this.$message.error('获取员工列表失败: ' + error.message)
        this.employeeList = []
        this.employeeTotal = 0
      } finally {
        this.employeeLoading = false
      }
    },

    async getTemplateList() {
      this.templateLoading = true
      try {
        console.log('🔍 正在获取薪资模板列表...')
        const response = await getEmployeeSalaryTemplateList(this.templateQuery)
        console.log('✅ 薪资模板列表响应:', response)

        if (response && response.data) {
          // 处理API响应格式
          const templates = Array.isArray(response.data) ? response.data : []
          this.templateTreeData = this.processTemplateData(templates)
          this.templateTotal = response.pagination ? response.pagination.total : templates.length
        } else {
          this.templateTreeData = []
          this.templateTotal = 0
        }

        console.log('📋 处理后的薪资模板列表:', this.templateTreeData)
        console.log('📊 薪资模板总数:', this.templateTotal)

        this.updateTemplateStats()
      } catch (error) {
        console.error('❌ 获取薪资模板列表失败:', error)
        this.$message.error('获取薪资模板列表失败: ' + error.message)
        this.templateTreeData = []
        this.templateTotal = 0
      } finally {
        this.templateLoading = false
      }
    },

    // 加载员工选项
    async loadEmployeeOptions() {
      try {
        console.log('🔄 开始加载员工选项...')
        const response = await getEmployeeList({ limit: 100 }) // 加载更多员工
        console.log('✅ 员工列表响应:', response)

        if (response && response.data && response.data.employees) {
          const employees = response.data.employees

          // 修复部门显示问题
          this.employeeOptions = employees.map(emp => {
            // 确保department是字符串
            let department = '未分配部门'
            if (emp.department) {
              if (typeof emp.department === 'object') {
                department = emp.department.name || emp.department.department_name || '未分配部门'
              } else {
                department = String(emp.department)
              }
            }

            return {
              label: `${emp.name} (${department})`,
              value: emp.id, // 确保这是数字类型
              name: emp.name,
              department: department,
              employeeId: emp.id
            }
          })

          console.log('📋 处理后的员工选项:', this.employeeOptions)
        } else {
          console.warn('⚠️ 员工数据格式异常:', response)
          this.employeeOptions = []
        }
      } catch (error) {
        console.error('❌ 获取员工列表失败:', error)
        this.$message.error('获取员工列表失败，请刷新页面重试')
        this.employeeOptions = []
      }
    },

    // 加载薪资项目类型选项
    async loadSalaryItemTypeOptions() {
      try {
        // 使用分页参数获取薪资项目类型列表
        const response = await getSalaryItemTypeList({
          page: 1,
          limit: 50 // 获取更多数据，确保包含所有类型
        })
        console.log('✅ 薪资项目类型列表响应:', response)

        if (response && response.data) {
          // 新的API响应结构，数据直接在data字段中
          const itemTypes = response.data || []
          this.salaryItemTypeOptions = itemTypes.map(item => ({
            label: `${item.item_name} (${item.type})`, // 显示名称和类型
            value: item.id, // 确保这是数字类型
            type: item.type,
            itemKey: item.item_key,
            description: item.description
          }))
        }

        console.log('📋 薪资项目类型选项:', this.salaryItemTypeOptions)
      } catch (error) {
        console.error('❌ 获取薪资项目类型列表失败:', error)
        this.salaryItemTypeOptions = []
      }
    },

    handleEmployeeFilter() {
      this.employeeQuery.page = 1
      this.getEmployeeList()
    },

    handleTemplateFilter() {
      this.templateQuery.page = 1
      this.getTemplateList()
    },

    handleTabClick(tab) {
      if (tab.paneName === 'templates') {
        this.getTemplateList()
      }
    },

    handleEmployeeRowClick(row) {
      this.selectedEmployee = row
      this.activeTab = 'templates'
      this.templateQuery.employee_id = row.id
      this.getTemplateList()
    },

    viewEmployeeTemplates(employee) {
      this.selectedEmployee = employee
      this.activeTab = 'templates'
      this.templateQuery.employee_id = employee.id
      this.getTemplateList()
    },

    createTemplateForEmployee(employee) {
      console.log('🔄 为员工创建薪资模板:', employee)

      // 确保员工选项已加载
      if (this.employeeOptions.length === 0) {
        this.loadEmployeeOptions()
      }

      this.temp = {
        id: undefined,
        employee_id: employee.id || employee.employee_id, // 兼容不同的数据格式
        item_id: undefined,
        default_value: 0,
        sub_default_value: 0
      }

      console.log('🔄 设置的temp数据:', this.temp)

      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.isEmployeePreset = true // 标记为预设员工

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        // 确保选择器正确显示预选的员工
        console.log('🔄 当前选中的员工ID:', this.temp.employee_id)
      })
    },

    handleCreate() {
      console.log('🔄 打开新增薪资模板对话框')

      // 确保员工选项已加载
      if (this.employeeOptions.length === 0) {
        console.log('🔄 员工选项为空，重新加载...')
        this.loadEmployeeOptions()
      }

      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.isEmployeePreset = false // 重置为非预设员工

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        console.log('🔄 对话框已打开，员工选项数量:', this.employeeOptions.length)
      })
    },

    handleUpdate(row) {
      console.log('🔄 准备编辑薪资模板:', row)

      // 确保员工选项已加载
      if (this.employeeOptions.length === 0) {
        console.log('🔄 员工选项为空，重新加载...')
        this.loadEmployeeOptions()
      }

      // 如果是从树状结构来的数据，需要重新映射
      if (row.type === 'template') {
        this.temp = {
          id: row.id.replace('template-', ''), // 移除前缀获取真实ID
          employee_id: parseInt(row.employee_id), // 确保是数字类型
          item_id: parseInt(row.item_id),
          default_value: parseFloat(row.defaultValue),
          sub_default_value: parseFloat(row.subDefaultValue)
        }
      } else {
        // 如果是原始API数据格式
        this.temp = {
          id: row.id,
          employee_id: parseInt(row.employee_id), // 确保是数字类型
          item_id: parseInt(row.item_id),
          default_value: parseFloat(row.default_value),
          sub_default_value: parseFloat(row.sub_default_value)
        }
      }

      console.log('🔄 设置的编辑数据:', this.temp)

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.isEmployeePreset = false // 重置为非预设员工
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        console.log('🔄 当前选中的员工ID:', this.temp.employee_id)
        console.log('🔄 可用的员工选项:', this.employeeOptions.map(opt => `${opt.label}(${opt.value})`))
      })
    },

    handleView(row) {
      // 如果是从树状结构来的数据，需要重新映射
      if (row.type === 'template') {
        this.viewData = {
          id: row.id.replace('template-', ''), // 薪资模板的实际ID
          employee_id: row.employee_id,
          employeeName: this.getEmployeeName(row.employee_id),
          employeeId: row.employee_id,
          item_id: row.item_id,
          itemTypeName: row.itemName,
          default_value: row.defaultValue,
          sub_default_value: row.subDefaultValue,
          created_at: this.formatDate(row.createdAt),
          updated_at: this.formatDate(row.updatedAt)
        }
      } else if (row.type === 'employee') {
        // 如果点击的是员工行，显示员工信息
        this.viewData = {
          id: row.employee_id,
          employee_id: row.employee_id,
          employeeName: row.name,
          employeeId: row.employee_id,
          department: row.department,
          position: row.position,
          totalValue: row.totalValue,
          created_at: this.formatDate(row.hireDate),
          updated_at: this.formatDate(row.hireDate)
        }
      } else {
        // 如果是原始API数据格式
        this.viewData = {
          id: row.id,
          employee_id: row.employee_id,
          employeeName: row.employee ? row.employee.name : this.getEmployeeName(row.employee_id),
          employeeId: row.employee_id,
          item_id: row.item_id,
          itemTypeName: row.item ? row.item.item_name : this.getItemTypeName(row.item_id),
          default_value: row.default_value,
          sub_default_value: row.sub_default_value,
          created_at: this.formatDate(row.created_at),
          updated_at: this.formatDate(row.updated_at)
        }
      }

      this.dialogViewVisible = true
    },

    handleDelete(row) {
      this.$confirm('确定要删除该员工的薪资模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          // 使用员工ID来删除薪资模板，确保是数字类型
          const employeeId = row.type === 'template' ? parseInt(row.employee_id) : parseInt(row.employee_id)

          const response = await deleteEmployeeSalaryTemplate(employeeId)
          console.log('✅ 删除薪资模板响应:', response)
          this.$notify({
            title: '成功',
            message: response.message || '薪资模板删除成功',
            type: 'success',
            duration: 2000
          })
          // 刷新列表
          this.getTemplateList()
        } catch (error) {
          console.error('删除薪资模板失败:', error)
          this.$message.error('删除薪资模板失败: ' + error.message)
        }
      })
    },

    // 验证和转换数据类型
    validateAndConvertData(data) {
      return {
        employee_id: parseInt(data.employee_id),
        item_id: parseInt(data.item_id),
        default_value: parseFloat(data.default_value) || 0,
        sub_default_value: parseFloat(data.sub_default_value) || 0
      }
    },

    createData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 验证并转换数据类型
            const templateData = this.validateAndConvertData(this.temp)

            // 验证数据有效性
            if (isNaN(templateData.employee_id) || isNaN(templateData.item_id)) {
              this.$message.error('请选择有效的员工和薪资项目类型')
              return
            }

            console.log('🔄 准备创建薪资模板:', templateData)
            const response = await createEmployeeSalaryTemplate(templateData)
            console.log('✅ 创建薪资模板响应:', response)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '薪资模板创建成功',
              type: 'success',
              duration: 2000
            })

            // 刷新列表
            this.getTemplateList()
          } catch (error) {
            console.error('创建薪资模板失败:', error)
            this.$message.error('创建薪资模板失败: ' + error.message)
          }
        }
      })
    },

    updateData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            // 员工薪资模板更新信息，只包含 default_value
            const templateData = {
              default_value: parseFloat(this.temp.default_value) || 0,
              sub_default_value: parseFloat(this.temp.sub_default_value) || 0
            }

            // 验证数据有效性
            if (!this.temp.id) {
              this.$message.error('缺少薪资模板ID')
              return
            }

            console.log('🔄 准备更新薪资模板:', templateData)
            console.log('🔄 使用模板ID:', this.temp.id)
            // 使用薪资模板ID而不是员工ID
            const response = await updateEmployeeSalaryTemplate(this.temp.id, templateData)
            console.log('✅ 更新薪资模板响应:', response)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '薪资模板更新成功',
              type: 'success',
              duration: 2000
            })

            // 刷新列表
            this.getTemplateList()
          } catch (error) {
            console.error('更新薪资模板失败:', error)
            this.$message.error('更新薪资模板失败: ' + error.message)
          }
        }
      })
    },

    // 辅助方法
    resetTemp() {
      this.temp = {
        id: undefined,
        employee_id: undefined,
        item_id: undefined,
        default_value: 0, // 确保是数字类型
        sub_default_value: 0
      }
    },

    getEmployeeName(employeeId) {
      const employee = this.employeeOptions.find(emp => emp.value === employeeId)
      return employee ? employee.name : ''
    },

    getEmployeeId(employeeId) {
      return employeeId
    },

    getItemTypeName(itemId) {
      const itemType = this.salaryItemTypeOptions.find(item => item.value === itemId)
      return itemType ? itemType.label : ''
    },

    getItemTypeTagColor(itemType) {
      if (!itemType) return 'default'

      const colorMap = {
        '固定薪资类': 'success', // 绿色
        '固定薪资': 'success', // 绿色
        '浮动薪资': 'warning', // 橙色
        '扣除项目': 'danger', // 红色
        '奖金': 'primary', // 蓝色
        'other': 'info' // 其他 - 蓝色
      }
      return colorMap[itemType] || 'default'
    },

    formatCurrency(value) {
      if (!value) return '0.00'
      return Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    },

    formatThaiCurrency(value) {
      if (!value) return '฿0.00'
      return '฿' + Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    },

    formatMyanmarCurrency(value) {
      if (!value) return '0.00K'
      return Number(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,') + 'K'
    },

    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    updateEmployeeStats() {
      this.employeeStats.total = this.employeeTotal
      this.employeeStats.active = this.employeeList.filter(emp => emp.status === 'active').length
      this.employeeStats.withTemplate = this.employeeList.filter(emp => emp.id).length
    },

    updateTemplateStats() {
      this.templateStats.total = this.templateTotal

      // 计算涉及的员工数量
      const employeeSet = new Set()
      this.templateTreeData.forEach(item => {
        if (item.type === 'employee') {
          employeeSet.add(item.id)
        } else if (item.employee_id) {
          employeeSet.add(item.employee_id)
        }
      })
      this.templateStats.employees = employeeSet.size

      this.templateStats.itemTypes = this.salaryItemTypeOptions.length
      this.templateStats.avgPerEmployee = this.templateStats.employees > 0
        ? (this.templateStats.total / this.templateStats.employees).toFixed(1) : 0
    },

    processTemplateData(templates) {
      // 按员工分组构建树状结构
      const employeeMap = new Map()

      templates.forEach(template => {
        const employeeId = template.employee_id
        const employee = template.employee

        if (!employeeMap.has(employeeId)) {
          employeeMap.set(employeeId, {
            id: `employee-${employeeId}`,
            type: 'employee',
            employee_id: employeeId,
            name: employee ? employee.name : `员工 ${employeeId}`,
            department: employee ? employee.department : '',
            position: employee ? employee.position : '',
            hireDate: employee ? employee.hire_date : '',
            totalValue: 0,
            totalSubValue: 0,
            children: []
          })
        }

        const employeeNode = employeeMap.get(employeeId)
        employeeNode.children.push({
          id: `template-${template.id}`,
          type: 'template',
          employee_id: employeeId,
          item_id: template.item_id,
          itemName: template.item ? template.item.item_name : `项目 ${template.item_id}`,
          itemType: template.item ? template.item.type : 'other',
          defaultValue: template.default_value,
          subDefaultValue: template.sub_default_value,
          createdAt: template.created_at,
          updatedAt: template.updated_at
        })

        // 计算员工的总薪资
        employeeNode.totalValue += template.default_value || 0
        employeeNode.totalSubValue += template.sub_default_value || 0
      })

      return Array.from(employeeMap.values())
    },

    handleEmployeeChange(value) {
      console.log('�� 员工选择器值已改变:', value)
      const selectedEmployee = this.employeeOptions.find(emp => emp.value === value)
      if (selectedEmployee) {
        console.log('✅ 选中的员工信息:', selectedEmployee)
      } else {
        console.log('⚠️ 未找到对应的员工信息')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.box-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}

.default-value {
  font-weight: bold;
  color: #E6A23C;
}

.salary-amount {
  font-weight: bold;
  color: #E6A23C;
}

.total-value {
  font-weight: bold;
  color: #409EFF;
}

.sub-default-value {
  font-weight: bold;
  color: #F56C6C;
}

.total-sub-value {
  font-weight: bold;
  color: #F56C6C;
}

// 货币样式
.thai-currency {
  color: #E6A23C;
  font-weight: bold;
}

.myanmar-currency {
  color: #F56C6C;
  font-weight: bold;
}

// 货币说明面板样式
.currency-info-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  .el-alert__icon {
    color: #fff;
  }

  .el-alert__title {
    color: #fff;
    font-weight: bold;
  }

  .el-alert__description {
    color: #fff;
  }
}

// 货币输入框样式
.thai-currency-input {
  .el-input-group__prepend {
    background-color: #FEF7E8;
    border-color: #E6A23C;
    color: #E6A23C;
    font-weight: bold;
    font-size: 16px;
  }

  .el-input__inner {
    border-color: #E6A23C;

    &:focus {
      border-color: #E6A23C;
      box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
    }
  }
}

.myanmar-currency-input {
  .el-input-group__append {
    background-color: #FEF0F0;
    border-color: #F56C6C;
    color: #F56C6C;
    font-weight: bold;
    font-size: 16px;
  }

  .el-input__inner {
    border-color: #F56C6C;

    &:focus {
      border-color: #F56C6C;
      box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
    }
  }
}

// 工具提示样式
.el-tooltip__popper {
  max-width: 200px;
  font-size: 12px;

  &.is-dark {
    background-color: #303133;
    border-color: #303133;
  }
}

// 货币显示样式
.currency-display {
  cursor: help;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.05);
  }
}

.employee-row {
  display: flex;
  align-items: center;
  padding: 5px 0;

  .el-icon-user {
    margin-right: 8px;
    color: #409EFF;
    font-size: 16px;
  }

  .employee-name {
    font-weight: bold;
    color: #303133;
    margin-right: 10px;
  }
}

.template-row {
  display: flex;
  align-items: center;
  padding: 5px 0;
  margin-left: 20px;

  .el-icon-document {
    margin-right: 8px;
    color: #67C23A;
    font-size: 14px;
  }

  span {
    color: #606266;
  }
}

// 表格样式优化
.el-table {
  border-radius: 8px;
  overflow: hidden;

  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }

  td {
    border-bottom: 1px solid #EBEEF5;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}

// 分页样式
.el-pagination {
  text-align: center;
  margin-top: 20px;
}

// 对话框样式
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #EBEEF5;
}

.el-dialog__body {
  padding: 30px 20px;
}

.readonly-tip {
  color: #E6A23C;
  font-weight: 500;
  font-style: italic;
}

// 标签页样式
.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav-wrap {
    padding: 0 10px;
  }

  .el-tabs__item {
    font-weight: 600;
    color: #909399;

    &.is-active {
      color: #409EFF;
    }
  }
}

// 按钮样式优化
.el-button {
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 600;
    color: #606266;
  }

  .el-input, .el-select {
    .el-input__inner {
      border-radius: 4px;
      border: 1px solid #DCDFE6;
      transition: border-color 0.3s;

      &:focus {
        border-color: #409EFF;
      }
    }
  }
}

// 卡片统计样式
.el-card {
  .el-card__body {
    padding: 20px;
  }
}

// 消息提示样式
.el-message {
  border-radius: 4px;
}

// 确认框样式
.el-message-box {
  border-radius: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .filter-container {
    .filter-item {
      width: 100%;
      margin-bottom: 10px;
    }
  }

  .el-table {
    font-size: 12px;
  }

  .card-value {
    font-size: 20px;
  }
}
</style>
