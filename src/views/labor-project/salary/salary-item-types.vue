<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.item_name" placeholder="项目名称" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.item_key" placeholder="项目标识" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.type" placeholder="项目类型" clearable style="width: 150px" class="filter-item">
        <el-option label="固定薪资类" value="income" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        新增项目类型
      </el-button>
    </div>

    <!-- 调试信息 -->
    <el-alert
      v-if="list.length === 0 && !listLoading"
      title="暂无薪资项目类型数据"
      :description="`请检查后端服务和代理配置，或尝试创建新的项目类型`"
      type="warning"
      style="margin-bottom: 20px;"
      :closable="false"
    >
      <template slot="default">
        <div style="margin-top: 10px;">
          <el-button size="small" type="primary" @click="testApiConnection">测试API连接</el-button>
        </div>
      </template>
    </el-alert>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" width="80px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目标识" width="200px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.item_key }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" width="200px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.item_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目类型" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getTypeTagColor(row.type)">{{ getTypeLabel(row.type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="描述" min-width="200px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.description || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="180px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatDate(row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" width="180px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatDate(row.updated_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="info" @click="handleView(row)">
            查看
          </el-button>
          <el-button size="mini" type="success" @click="handleManageEligibilities(row)">
            关联管理
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row,$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 添加/编辑薪资项目类型对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="120px" style="width: 90%; margin-left:50px;">
        <el-form-item label="项目标识" prop="item_key">
          <el-input v-model="temp.item_key" placeholder="请输入项目标识，如：base_salary" :disabled="dialogStatus === 'update'" />
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            项目标识用于系统内部识别，创建后不可修改
          </div>
        </el-form-item>
        <el-form-item label="项目名称" prop="item_name">
          <el-input v-model="temp.item_name" placeholder="请输入项目名称，如：基本工资" />
        </el-form-item>
        <el-form-item label="项目类型" prop="type">
          <el-select v-model="temp.type" placeholder="请选择项目类型" style="width: 100%">
            <el-option label="固定薪资类" value="income" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看薪资项目类型详情对话框 -->
    <el-dialog title="薪资项目类型详情" :visible.sync="dialogViewVisible" width="600px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="ID">{{ viewData.id }}</el-descriptions-item>
        <el-descriptions-item label="项目标识">{{ viewData.item_key }}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{ viewData.item_name }}</el-descriptions-item>
        <el-descriptions-item label="项目类型">
          <el-tag :type="getTypeTagColor(viewData.type)">{{ getTypeLabel(viewData.type) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="项目描述">{{ viewData.description || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewData.created_at }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewData.updated_at }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 管理工资类型关联对话框 -->
    <el-dialog :title="`管理工资类型关联 - ${currentItem.item_name}`" :visible.sync="dialogEligibilityVisible" width="700px">
      <div class="eligibility-container">
        <div class="eligibility-header">
          <h4>为该薪资项目类型分配可用的工资类型</h4>
          <p style="color: #666; font-size: 14px; margin-top: 5px;">
            选择该薪资项目类型可以关联到哪些工资类型中
          </p>
        </div>

        <el-divider />

        <div class="eligibility-content">
          <div class="available-types">
            <h5>可用工资类型</h5>
            <el-loading v-loading="salaryTypesLoading" class="salary-types-container">
              <el-checkbox-group v-model="selectedSalaryTypes" @change="handleSalaryTypeChange">
                <div v-for="salaryType in salaryTypesList" :key="salaryType.id" class="salary-type-item">
                  <el-checkbox :label="salaryType.id" :value="salaryType.id">
                    <div class="salary-type-info">
                      <div class="salary-type-name">{{ salaryType.type_name }}</div>
                      <div class="salary-type-desc">{{ salaryType.type_key }}</div>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </el-loading>
          </div>
        </div>

        <div v-if="selectedSalaryTypes.length > 0" class="eligibility-stats">
          <el-alert
            :title="`已选择 ${selectedSalaryTypes.length} 个工资类型`"
            type="info"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEligibilityVisible = false">取消</el-button>
        <el-button type="primary" :loading="savingEligibilities" @click="saveEligibilities">
          保存关联关系
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import {
  getSalaryItemTypeList,
  createSalaryItemType,
  updateSalaryItemType,
  deleteSalaryItemType,
  getSalaryItemTypeDetail,
  getSalaryTypeList,
  getSalaryItemTypeEligibilities,
  createSalaryTypeItemEligibility
} from '@/api/labor-project/salaries'

export default {
  name: 'SalaryItemTypeManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        item_name: undefined,
        item_key: undefined,
        type: undefined
      },
      temp: {
        id: undefined,
        item_key: '',
        item_name: '',
        type: '',
        description: ''
      },
      dialogFormVisible: false,
      dialogViewVisible: false,
      dialogEligibilityVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑薪资项目类型',
        create: '新增薪资项目类型'
      },
      viewData: {},
      // 关联管理相关数据
      currentItem: {},
      salaryTypesList: [],
      selectedSalaryTypes: [],
      salaryTypesLoading: false,
      savingEligibilities: false,
      rules: {
        item_key: [
          { required: true, message: '项目标识是必填项', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '项目标识只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        item_name: [
          { required: true, message: '项目名称是必填项', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '项目类型是必填项', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.listLoading = true
      try {
        console.log('🔍 正在获取薪资项目类型列表...')
        const response = await getSalaryItemTypeList(this.listQuery)
        console.log('✅ API响应:', response)

        // 根据实际API响应格式处理
        if (response && response.data) {
          if (Array.isArray(response.data.salary_item_types)) {
            // 如果有 salary_item_types 数组
            this.list = response.data.salary_item_types
            this.total = response.data.pagination ? response.data.pagination.total : this.list.length
          } else if (Array.isArray(response.data)) {
            // 如果 data 直接是数组
            this.list = response.data
            this.total = this.list.length
          } else {
            this.list = []
            this.total = 0
          }
        } else if (Array.isArray(response)) {
          this.list = response
          this.total = response.length
        } else {
          this.list = []
          this.total = 0
        }

        console.log('📋 处理后的薪资项目类型列表:', this.list)
        console.log('📊 总数:', this.total)
      } catch (error) {
        console.error('❌ 获取薪资项目类型列表失败:', error)
        this.$message.error('获取薪资项目类型列表失败: ' + error.message)

        // 显示更友好的错误提示
        if (error.message.includes('Network Error') || error.message.includes('网络错误')) {
          this.$message.error('网络连接失败，请检查后端服务和代理配置')
        }

        this.list = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    handleView(row) {
      this.viewData = {
        id: row.id,
        item_key: row.item_key,
        item_name: row.item_name,
        type: row.type,
        description: row.description,
        created_at: this.formatDate(row.created_at),
        updated_at: this.formatDate(row.updated_at)
      }
      this.dialogViewVisible = true
    },

    handleDelete(row, index) {
      this.$confirm('确定要删除该薪资项目类型吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const response = await deleteSalaryItemType(row.id)
          console.log('✅ 删除薪资项目类型响应:', response)
          this.$notify({
            title: '成功',
            message: response.message || '薪资项目类型删除成功',
            type: 'success',
            duration: 2000
          })
          // 刷新列表
          this.getList()
        } catch (error) {
          console.error('删除薪资项目类型失败:', error)
          this.$message.error('删除薪资项目类型失败: ' + error.message)
        }
      })
    },

    async createData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            const itemTypeData = {
              item_key: this.temp.item_key,
              item_name: this.temp.item_name,
              type: this.temp.type,
              description: this.temp.description
            }

            const response = await createSalaryItemType(itemTypeData)
            console.log('✅ 创建薪资项目类型响应:', response)

            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '薪资项目类型创建成功',
              type: 'success',
              duration: 2000
            })

            // 刷新列表
            this.getList()
          } catch (error) {
            console.error('创建薪资项目类型失败:', error)
            this.$message.error('创建薪资项目类型失败: ' + error.message)
          }
        }
      })
    },

    async updateData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            const itemTypeData = {
              item_name: this.temp.item_name,
              type: this.temp.type,
              description: this.temp.description
              // item_key 不允许修改
            }

            const response = await updateSalaryItemType(this.temp.id, itemTypeData)
            console.log('✅ 更新薪资项目类型响应:', response)

            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: response.message || '薪资项目类型更新成功',
              type: 'success',
              duration: 2000
            })

            // 刷新列表
            this.getList()
          } catch (error) {
            console.error('更新薪资项目类型失败:', error)
            this.$message.error('更新薪资项目类型失败: ' + error.message)
          }
        }
      })
    },

    resetTemp() {
      this.temp = {
        id: undefined,
        item_key: '',
        item_name: '',
        type: '',
        description: ''
      }
    },

    // API连接测试
    async testApiConnection() {
      try {
        const response = await fetch('/labor-project/api/v1/salary-item-types')
        if (response.ok) {
          const data = await response.json()
          this.$message.success('API连接成功，数据已获取')
          console.log('API测试成功:', data)
        } else {
          this.$message.error(`API连接失败: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        this.$message.error('网络连接错误: ' + error.message)
        console.error('API测试失败:', error)
      }
    },

    // 格式化日期显示
    formatDate(dateString) {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    // 获取类型标签颜色
    getTypeTagColor(type) {
      const typeColorMap = {
        'income': 'success', // 收入项 - 绿色
        'deduction': 'warning', // 扣除项 - 橙色
        'other': 'info' // 其他 - 蓝色
      }
      return typeColorMap[type] || 'default'
    },

    // 获取类型标签文本
    getTypeLabel(type) {
      const typeLabelMap = {
        'income': '固定薪资类',
        'deduction': '扣除项',
        'other': '其他'
      }
      return typeLabelMap[type] || type || '-'
    },

    // 处理关联管理
    async handleManageEligibilities(row) {
      this.currentItem = { ...row }
      this.dialogEligibilityVisible = true
      this.selectedSalaryTypes = []

      // 加载工资类型列表和当前关联关系
      await Promise.all([
        this.loadSalaryTypes(),
        this.loadCurrentEligibilities(row.id)
      ])
    },

    // 加载工资类型列表
    async loadSalaryTypes() {
      this.salaryTypesLoading = true
      try {
        const response = await getSalaryTypeList({})
        console.log('✅ 工资类型列表响应:', response)

        // 处理响应数据
        if (response && response.data) {
          if (Array.isArray(response.data.salary_types)) {
            this.salaryTypesList = response.data.salary_types
          } else if (Array.isArray(response.data)) {
            this.salaryTypesList = response.data
          } else {
            this.salaryTypesList = []
          }
        } else if (Array.isArray(response)) {
          this.salaryTypesList = response
        } else {
          this.salaryTypesList = []
        }

        console.log('📋 工资类型列表:', this.salaryTypesList)
      } catch (error) {
        console.error('❌ 获取工资类型列表失败:', error)
        this.$message.error('获取工资类型列表失败: ' + error.message)
        this.salaryTypesList = []
      } finally {
        this.salaryTypesLoading = false
      }
    },

    // 加载当前薪资项目类型的关联关系
    async loadCurrentEligibilities(itemId) {
      try {
        const response = await getSalaryItemTypeEligibilities(itemId)
        console.log('✅ 当前关联关系响应:', response)

        // 处理响应数据，提取已关联的工资类型ID
        if (response && response.data) {
          if (Array.isArray(response.data.eligibilities)) {
            this.selectedSalaryTypes = response.data.eligibilities.map(item => item.salary_type_id)
          } else if (Array.isArray(response.data)) {
            this.selectedSalaryTypes = response.data.map(item => item.salary_type_id)
          } else {
            this.selectedSalaryTypes = []
          }
        } else if (Array.isArray(response)) {
          this.selectedSalaryTypes = response.map(item => item.salary_type_id)
        } else {
          this.selectedSalaryTypes = []
        }

        console.log('📋 当前已选择的工资类型:', this.selectedSalaryTypes)
      } catch (error) {
        console.error('❌ 获取当前关联关系失败:', error)
        // 如果获取失败，设置为空数组（可能是新的薪资项目类型）
        this.selectedSalaryTypes = []
      }
    },

    // 处理工资类型选择变化
    handleSalaryTypeChange(value) {
      console.log('📝 工资类型选择变化:', value)
      this.selectedSalaryTypes = value
    },

    // 保存关联关系
    async saveEligibilities() {
      this.savingEligibilities = true
      try {
        console.log('💾 保存关联关系:', {
          itemId: this.currentItem.id,
          selectedSalaryTypes: this.selectedSalaryTypes
        })

        // 为每个选中的工资类型创建关联关系
        // 后端应该处理重复的关联关系
        const createPromises = this.selectedSalaryTypes.map(salaryTypeId => {
          return createSalaryTypeItemEligibility({
            item_id: this.currentItem.id,
            salary_type_id: salaryTypeId
          })
        })

        await Promise.all(createPromises)

        console.log('✅ 关联关系保存成功')

        this.dialogEligibilityVisible = false
        this.$notify({
          title: '成功',
          message: '关联关系保存成功',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        console.error('❌ 保存关联关系失败:', error)
        this.$message.error('保存关联关系失败: ' + error.message)
      } finally {
        this.savingEligibilities = false
      }
    }
  }
}
</script>

<style scoped>
.eligibility-container {
  padding: 10px 0;
}

.eligibility-header h4 {
  margin: 0 0 5px 0;
  color: #303133;
  font-weight: 600;
}

.eligibility-content {
  margin: 20px 0;
}

.available-types h5 {
  margin: 0 0 15px 0;
  color: #606266;
  font-weight: 500;
}

.salary-types-container {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  background-color: #FAFAFA;
}

.salary-type-item {
  margin-bottom: 15px;
  padding: 12px;
  background-color: #FFFFFF;
  border: 1px solid #E4E7ED;
  border-radius: 6px;
  transition: all 0.3s;
}

.salary-type-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.salary-type-item:last-child {
  margin-bottom: 0;
}

.salary-type-info {
  margin-left: 10px;
}

.salary-type-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.salary-type-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.eligibility-stats {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .salary-types-container {
    max-height: 300px;
  }
}

/* 过滤器样式 */
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
</style>
