<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.employeeName" placeholder="员工姓名" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.department" placeholder="部门" clearable style="width: 200px" class="filter-item">
        <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.salaryMonth"
        type="month"
        placeholder="选择月份"
        class="filter-item"
        style="width: 200px"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        新增薪资
      </el-button>
      <el-button class="filter-item" type="success" icon="el-icon-money" @click="handleBatchCalculate">
        批量计算
      </el-button>
      <el-button v-waves :loading="downloadLoading" class="filter-item" type="warning" icon="el-icon-download" @click="handleDownload">
        导出工资条
      </el-button>
      <el-button class="filter-item" type="info" icon="el-icon-setting" @click="goToSalaryItemTypes">
        项目类型管理
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-user" @click="goToEmployeeSalaryTemplates">
        员工薪资模板
      </el-button>
    </div>

    <!-- 薪资统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>本月总薪资</span>
          </div>
          <div class="text item">
            <span class="number">¥{{ statistics.totalSalary }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>平均薪资</span>
          </div>
          <div class="text item">
            <span class="number">¥{{ statistics.avgSalary }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>已发放</span>
          </div>
          <div class="text item">
            <span class="number issued">{{ statistics.issuedCount }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>未发放</span>
          </div>
          <div class="text item">
            <span class="number pending">{{ statistics.pendingCount }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="员工编号" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employeeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.employeeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.department }}</span>
        </template>
      </el-table-column>
      <el-table-column label="薪资月份" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.salaryMonth }}</span>
        </template>
      </el-table-column>
      <el-table-column label="基本工资" width="120px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ row.baseSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绩效奖金" width="120px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ row.performanceBonus }}</span>
        </template>
      </el-table-column>
      <el-table-column label="加班费" width="120px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ row.overtimePay }}</span>
        </template>
      </el-table-column>
      <el-table-column label="津贴补助" width="120px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ row.allowances }}</span>
        </template>
      </el-table-column>
      <el-table-column label="社保扣除" width="120px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ row.socialInsurance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="个人所得税" width="120px" align="center">
        <template slot-scope="{row}">
          <span>¥{{ row.personalTax }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实发工资" width="120px" align="center">
        <template slot-scope="{row}">
          <span class="net-salary">¥{{ row.netSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发放状态" width="120px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200px" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="info" size="mini" @click="handleView(row)">
            查看
          </el-button>
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button v-if="row.status === '未发放'" type="success" size="mini" @click="handleIssue(row)">
            发放
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row,$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 添加/编辑薪资对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="800px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="120px" style="width: 90%; margin-left:50px;">
        <el-row>
          <el-col :span="12">
            <el-form-item label="员工" prop="employeeId">
              <el-select v-model="temp.employeeId" placeholder="请选择员工" style="width: 100%">
                <el-option v-for="item in employeeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="薪资月份" prop="salaryMonth">
              <el-date-picker v-model="temp.salaryMonth" type="month" placeholder="请选择薪资月份" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="基本工资" prop="baseSalary">
              <el-input-number v-model="temp.baseSalary" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="绩效奖金" prop="performanceBonus">
              <el-input-number v-model="temp.performanceBonus" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="加班费" prop="overtimePay">
              <el-input-number v-model="temp.overtimePay" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="津贴补助" prop="allowances">
              <el-input-number v-model="temp.allowances" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="社保扣除" prop="socialInsurance">
              <el-input-number v-model="temp.socialInsurance" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公积金扣除" prop="housingFund">
              <el-input-number v-model="temp.housingFund" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="个人所得税" prop="personalTax">
              <el-input-number v-model="temp.personalTax" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他扣除" prop="otherDeductions">
              <el-input-number v-model="temp.otherDeductions" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="实发工资">
              <el-input :value="calculateNetSalary()" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发放状态" prop="status">
              <el-select v-model="temp.status" placeholder="请选择发放状态">
                <el-option label="未发放" value="未发放" />
                <el-option label="已发放" value="已发放" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="temp.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看薪资详情对话框 -->
    <el-dialog title="薪资详情" :visible.sync="dialogViewVisible" width="700px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工编号">{{ viewData.employeeId }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ viewData.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ viewData.department }}</el-descriptions-item>
        <el-descriptions-item label="薪资月份">{{ viewData.salaryMonth }}</el-descriptions-item>
        <el-descriptions-item label="基本工资">¥{{ viewData.baseSalary }}</el-descriptions-item>
        <el-descriptions-item label="绩效奖金">¥{{ viewData.performanceBonus }}</el-descriptions-item>
        <el-descriptions-item label="加班费">¥{{ viewData.overtimePay }}</el-descriptions-item>
        <el-descriptions-item label="津贴补助">¥{{ viewData.allowances }}</el-descriptions-item>
        <el-descriptions-item label="社保扣除">¥{{ viewData.socialInsurance }}</el-descriptions-item>
        <el-descriptions-item label="公积金扣除">¥{{ viewData.housingFund }}</el-descriptions-item>
        <el-descriptions-item label="个人所得税">¥{{ viewData.personalTax }}</el-descriptions-item>
        <el-descriptions-item label="其他扣除">¥{{ viewData.otherDeductions }}</el-descriptions-item>
        <el-descriptions-item label="实发工资">
          <span class="net-salary">¥{{ viewData.netSalary }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="发放状态">
          <el-tag :type="getStatusType(viewData.status)">
            {{ viewData.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'SalaryManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        department: undefined,
        salaryMonth: undefined
      },
      statistics: {
        totalSalary: '456,789.00',
        avgSalary: '8,500.00',
        issuedCount: 45,
        pendingCount: 8
      },
      departmentOptions: [
        { label: '技术部', value: '技术部' },
        { label: '销售部', value: '销售部' },
        { label: '人事部', value: '人事部' },
        { label: '财务部', value: '财务部' },
        { label: '市场部', value: '市场部' }
      ],
      employeeOptions: [
        { label: '张三 (EMP001)', value: 'EMP001' },
        { label: '李四 (EMP002)', value: 'EMP002' },
        { label: '王五 (EMP003)', value: 'EMP003' }
      ],
      temp: {
        employeeId: '',
        salaryMonth: '',
        baseSalary: 0,
        performanceBonus: 0,
        overtimePay: 0,
        allowances: 0,
        socialInsurance: 0,
        housingFund: 0,
        personalTax: 0,
        otherDeductions: 0,
        netSalary: 0,
        status: '未发放',
        remark: ''
      },
      dialogFormVisible: false,
      dialogViewVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑薪资',
        create: '新增薪资'
      },
      viewData: {},
      downloadLoading: false,
      rules: {
        employeeId: [{ required: true, message: '员工是必选项', trigger: 'change' }],
        salaryMonth: [{ required: true, message: '薪资月份是必选项', trigger: 'change' }],
        baseSalary: [{ required: true, message: '基本工资是必填项', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      // 模拟API调用
      setTimeout(() => {
        this.list = [
          {
            id: 1,
            employeeId: 'EMP001',
            employeeName: '张三',
            department: '技术部',
            salaryMonth: '2024-01',
            baseSalary: 15000.00,
            performanceBonus: 2000.00,
            overtimePay: 500.00,
            allowances: 800.00,
            socialInsurance: 1500.00,
            housingFund: 1200.00,
            personalTax: 1200.00,
            otherDeductions: 0.00,
            netSalary: 14400.00,
            status: '已发放',
            remark: '正常发放'
          },
          {
            id: 2,
            employeeId: 'EMP002',
            employeeName: '李四',
            department: '销售部',
            salaryMonth: '2024-01',
            baseSalary: 12000.00,
            performanceBonus: 3000.00,
            overtimePay: 300.00,
            allowances: 600.00,
            socialInsurance: 1200.00,
            housingFund: 960.00,
            personalTax: 1500.00,
            otherDeductions: 0.00,
            netSalary: 12240.00,
            status: '未发放',
            remark: '待财务审核'
          },
          {
            id: 3,
            employeeId: 'EMP003',
            employeeName: '王五',
            department: '人事部',
            salaryMonth: '2024-01',
            baseSalary: 10000.00,
            performanceBonus: 1000.00,
            overtimePay: 200.00,
            allowances: 500.00,
            socialInsurance: 1000.00,
            housingFund: 800.00,
            personalTax: 800.00,
            otherDeductions: 0.00,
            netSalary: 9100.00,
            status: '已发放',
            remark: '正常发放'
          }
        ]
        this.total = 3
        this.listLoading = false
      }, 1000)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row) {
      this.viewData = Object.assign({}, row)
      this.dialogViewVisible = true
    },
    handleIssue(row) {
      this.$confirm('确定要发放该员工薪资吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.list.findIndex(v => v.id === row.id)
        this.list[index].status = '已发放'
        this.$notify({
          title: '成功',
          message: '薪资发放成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该薪资记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.list.splice(index, 1)
        this.total--
        this.$notify({
          title: '成功',
          message: '删除成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024
          this.temp.netSalary = this.calculateNetSalary()
          this.list.unshift(this.temp)
          this.total++
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '创建成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.netSalary = this.calculateNetSalary()
          const index = this.list.findIndex(v => v.id === this.temp.id)
          this.list.splice(index, 1, tempData)
          this.dialogFormVisible = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    resetTemp() {
      this.temp = {
        employeeId: '',
        salaryMonth: '',
        baseSalary: 0,
        performanceBonus: 0,
        overtimePay: 0,
        allowances: 0,
        socialInsurance: 0,
        housingFund: 0,
        personalTax: 0,
        otherDeductions: 0,
        netSalary: 0,
        status: '未发放',
        remark: ''
      }
    },
    calculateNetSalary() {
      const income = (this.temp.baseSalary || 0) + (this.temp.performanceBonus || 0) + (this.temp.overtimePay || 0) + (this.temp.allowances || 0)
      const deductions = (this.temp.socialInsurance || 0) + (this.temp.housingFund || 0) + (this.temp.personalTax || 0) + (this.temp.otherDeductions || 0)
      return (income - deductions).toFixed(2)
    },
    handleBatchCalculate() {
      this.$confirm('确定要批量计算本月薪资吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: '成功',
          message: '批量计算完成',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleDownload() {
      this.downloadLoading = true
      setTimeout(() => {
        this.downloadLoading = false
        this.$notify({
          title: '成功',
          message: '导出成功',
          type: 'success',
          duration: 2000
        })
      }, 1000)
    },
    getStatusType(status) {
      const statusMap = {
        '未发放': 'warning',
        '已发放': 'success'
      }
      return statusMap[status]
    },

    // 跳转到薪资项目类型管理页面
    goToSalaryItemTypes() {
      this.$router.push('/labor-project/salary-item-types')
    },

    // 跳转到员工薪资模板管理页面
    goToEmployeeSalaryTemplates() {
      this.$router.push('/labor-project/employee-salary-templates')
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.box-card {
  text-align: center;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.number {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
}
.number.issued {
  color: #67C23A;
}
.number.pending {
  color: #E6A23C;
}
.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 5px;
}
.net-salary {
  font-weight: bold;
  color: #E6A23C;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
