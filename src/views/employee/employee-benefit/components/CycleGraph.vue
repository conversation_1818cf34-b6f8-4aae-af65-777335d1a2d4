<template>
  <div class="cycle-graph-container">
    <el-skeleton v-if="loading" :rows="6" animated />
    <div v-else>
      <div ref="chart" style="width: 100%; height: 500px;" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import gql from 'graphql-tag'

export default {
  name: 'CycleGraph',
  data() {
    return {
      loading: true,
      chart: null,
      statistics: [],
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = `${params[0].axisValue}<br/>`
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}<br/>
                        人数: ${param.data.count}人<br/>`
            })
            return result
          }
        },
        title: {
          text: '职位统计表',
          subtext: '按分店统计',
          left: 'center'
        },
        grid: {
          top: '15%',
          bottom: '15%'
        },
        legend: {
          type: 'scroll',
          bottom: 0,
          data: []
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'center',
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar', 'stack'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: [] // 将由分店数据填充
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '人数',
            position: 'left'
          }
        ],
        series: [
          // 动态生成职位系列
        ]
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.chart) {
      this.chart.dispose()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.chart.setOption(this.option)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    processChartData(data) {
      const storeData = {}
      const positions = new Set()
      const positionStats = new Map()

      // 首先收集所有职位并初始化统计
      data.forEach(employee => {
        const posName = employee.employeePositionTable?.positionName || '其他'
        positions.add(posName)
        if (!positionStats.has(posName)) {
          positionStats.set(posName, {
            totalCount: 0,
            totalAmount: 0,
            avgAmount: 0,
            stores: new Map()
          })
        }
      })

      data.forEach(employee => {
        const storeId = employee.fenDianId
        const positionName = employee.employeePositionTable?.positionName || '其他'
        const amount = employee.social_security?.amount || 0
        const stats = positionStats.get(positionName)

        // 更新职位总统计
        stats.totalCount++
        stats.totalAmount += amount

        // 更新分店统计
        if (!stats.stores.has(storeId)) {
          stats.stores.set(storeId, { count: 0, amount: 0 })
        }
        const storeStats = stats.stores.get(storeId)
        storeStats.count++
        storeStats.amount += amount

        if (!storeData[storeId]) {
          storeData[storeId] = {}
        }

        if (!storeData[storeId][positionName]) {
          storeData[storeId][positionName] = {
            amount: 0,
            count: 0
          }
        }
        storeData[storeId][positionName].amount += amount
        storeData[storeId][positionName].count += 1
      })

      console.log('所有分店ID:', Object.keys(storeData))
      const stores = Object.keys(storeData).sort()
      const seriesData = {}

      positions.forEach(pos => {
        seriesData[pos] = []
      })

      stores.forEach(store => {
        positions.forEach(pos => {
          const storePosition = storeData[store][pos] || { amount: 0, count: 0 }
          seriesData[pos].push({
            value: storePosition.count,
            count: storePosition.count
          })
        })
      })

      // 初始化图表系列
      this.option.series = Array.from(positions).map(pos => ({
        name: pos,
        type: 'bar',
        barMaxWidth: 50,
        emphasis: {
          focus: 'series'
        },
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            return params.value + '人'
          }
        },
        data: seriesData[pos]
      }))

      // 计算平均值并保存统计结果
      this.statistics = Array.from(positionStats.entries()).map(([position, stats]) => ({
        position,
        totalCount: stats.totalCount,
        totalAmount: (stats.totalAmount / 10000).toFixed(2),
        avgAmount: (stats.totalAmount / stats.totalCount / 10000).toFixed(2),
        storeDetails: Array.from(stats.stores.entries()).map(([store, detail]) => ({
          store,
          count: detail.count,
          amount: (detail.amount / 10000).toFixed(2),
          avg: (detail.amount / detail.count / 10000).toFixed(2)
        }))
      }))

      // 在控制台输出详细统计
      console.table(this.statistics.map(stat => ({
        '职位': stat.position,
        '总人数': stat.totalCount,
        '总金额(万)': stat.totalAmount,
        '人均(万)': stat.avgAmount
      })))

      // 分店详细统计
      this.statistics.forEach(stat => {
        console.group(`${stat.position} 分店详情:`)
        console.table(stat.storeDetails.map(detail => ({
          '分店': this.getStoreName(detail.store),
          '人数': detail.count,
          '金额(万)': detail.amount,
          '人均(万)': detail.avg
        })))
        console.groupEnd()
      })

      return {
        xAxis: stores,
        series: Array.from(positions).map(pos => ({
          name: pos,
          data: seriesData[pos]
        }))
      }
    },
    updateChartData(rawData) {
      console.log('rawData', rawData)
      const { xAxis, series } = this.processChartData(rawData)

      // 根据分店ID显示不同名称
      this.option.xAxis[0].data = xAxis.map(store => this.getStoreName(store))
      series.forEach((item, index) => {
        this.option.series[index].data = item.data
      })

      // 更新 legend 数据
      this.option.legend.data = Array.from(new Set(rawData
        .map(emp => emp.employeePositionTable?.positionName || '其他')
        .filter(Boolean)))

      if (this.chart) {
        this.chart.setOption(this.option)
      }
    },
    async getList() {
      try {
        this.listLoading = true
        let allEmployees = []
        let page = 1
        const limit = 10 // 每页数据量
        let hasMore = true

        while (hasMore) {
          const { data } = await this.$apollo.query({
            query: gql`
              query FetchEmployees(
                $page: Int!, 
                $limit: Int!, 
                $workActive: Boolean
              ) {
                employees(
                  filter: {
                    salaryType: 51883
                    workActive: $workActive
                  }
                  page: $page
                  limit: $limit
                ) {
                  employees {
                    autoId
                    employeeId
                    employee_mm_name
                    salary_mm_name
                    social_security {
                      amount
                      createDatetime
                      isActive
                    }
                    employeePositionTable {
                      autoId
                      employeeId
                      positionId
                      positionName
                    }
                    workActive   
                    fenDianId
                  }
                  totalCount
                }
              }
            `,
            variables: {
              page: page,
              limit: limit,
              workActive: true
            },
            fetchPolicy: 'no-cache'
          })

          allEmployees = allEmployees.concat(data.employees.employees)
          const fetchedCount = data.employees.employees.length
          hasMore = fetchedCount === limit
          page++
        }

        this.updateChartData(allEmployees)
      } catch (error) {
        console.error(error)
        this.$notify.error({
          title: '错误',
          message: '数据加载失败'
        })
      } finally {
        this.listLoading = false
      }
    },
    getStoreName(storeId) {
      const id = String(storeId)
      const storeNames = {
        '1': '分店1',
        '2': '分店2',
        '3': '分店3',
        '4': '分店4'
      }
      return storeNames[id] || `分店${id}`
    }
  }
}
</script>

<style scoped>
.cycle-graph-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
</style>

