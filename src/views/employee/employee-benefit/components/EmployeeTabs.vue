<template>
  <div class="app-container">
    <div class="filter-container">
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="在职" name="active" />
        <el-tab-pane label="离职" name="inactive" />
      </el-tabs>

      <el-input
        v-model="listQuery.employeeId"
        placeholder="ဝန်ထမ်းအမည်"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />

      <el-select
        v-model="listQuery.benefitType"
        placeholder="ခံစားခွင့်အမျိုးအစား"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option
          v-for="item in benefitTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.fenDianId"
        placeholder="选择分店"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option
          v-for="item in fenDianOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.positionId"
        placeholder="选择职位"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option
          v-for="item in positionOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        ရှာဖွေရန်
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        အသစ်ထည့်ရန်
      </el-button>
      <el-button
        v-waves
        :loading="downloadLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="handleDownload"
      >
        ဒေါင်းလုဒ်လုပ်ရန်
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="handleSortChange"
    >
      <el-table-column
        label="ID"
        prop="id"
        align="center"
        width="80"
        sortable="custom"
      >
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="ဝန်ထမ်းအမည်"
        min-width="120"
        align="center"
      >
        <template #default="{ row }">
          <span>{{ row.employeeName }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="职位"
        width="250"
        align="center"
        prop="positionId"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-select
            v-model="row.positionId"
            placeholder="选择职位"
            clearable
            @change="handlePositionChange(row)"
          >
            <el-option
              v-for="item in positionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        label="分店"
        width="150"
        align="center"
        prop="fenDianId"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-select
            v-model="row.fenDianId"
            placeholder="选择分店"
            clearable
            @change="handleFenDianChange(row)"
          >
            <el-option
              v-for="item in fenDianOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        label="ခံစားခွင့်အမျိုးအစား"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="row.benefitType | benefitTypeFilter">
            {{ row.benefitType | benefitTypeNameFilter(benefitTypeOptions) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="ငွေပမာဏ"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <span>¥{{ (row.amount || 0) | thousandSeparator }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="စတင်သည့်ရက်စွဲ"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <span>{{ row.effectiveDate | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="အခြေအနေ"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="row.status | statusFilter">
            {{ row.status | statusNameFilter(statusOptions) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="今日工作状态"
        width="120"
        align="center"
        prop="isWorking"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag :type="row.isWorking ? 'success' : 'danger'">
            {{ row.isWorking ? '已到岗' : '未到岗' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="openUpdateEmployeeModal(row)"
          >
            {{ row.workActive ? '设为离职' : '设为在职' }}
          </el-button>
        </template>
      </el-table-column>

      <el-table-column
        label="လုပ်ဆောင်ချက်"
        align="center"
        width="230"
        class-name="small-padding fixed-width"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleCreate(row)"
          >
            {{ row.social_security === null ? 'အသစ်ထည့်ရန်' : 'ပြင်ဆင်ရန်' }}
          </el-button>
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="ဝန်ထမ်းအမည်" prop="employeeName">
          <el-input v-model="temp.employeeName" />
        </el-form-item>

        <el-form-item label="ခံစားခွင့်အမျိုးအစား" prop="benefitType">
          <el-select v-model="temp.benefitType" class="filter-item" placeholder="请选择">
            <el-option
              v-for="item in benefitTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="ငွေပမာဏ" prop="amount">
          <el-input-number v-model="temp.amount" :min="0" :precision="2" />
        </el-form-item>

        <el-form-item label="စတင်သည့်ရက်စွဲ" prop="effectiveDate">
          <el-date-picker
            v-model="temp.effectiveDate"
            type="date"
            value-format="timestamp"
            placeholder="请选择日期"
          />
        </el-form-item>

        <el-form-item
          label="အခြေအနေ"
          prop="status"
        >
          <el-select v-model="temp.status" class="filter-item" placeholder="请选择">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogFormVisible = false">
          ပယ်ဖျက်ရန်
        </el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          အတည်ပြုရန်
        </el-button>
      </template>
    </el-dialog>

    <!-- 添加密码验证对话框 -->
    <el-dialog
      title="密码验证"
      :visible.sync="passwordDialogVisible"
      width="30%"
    >
      <el-form>
        <el-form-item label="请输入密码" required>
          <el-input
            v-model="password"
            type="password"
            placeholder="请输入密码"
            @keyup.enter.native="verifyPassword"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="verifyPassword">确认</el-button>
      </span>
    </el-dialog>

    <!-- 更新员工状态模态框 -->
    <el-dialog
      title="更新员工状态"
      :visible.sync="updateEmployeeModalVisible"
      width="30%"
    >
      <el-form v-if="currentEmployee">
        <el-form-item label="员工姓名">
          <el-input v-model="currentEmployee.employeeName" disabled />
        </el-form-item>
        <el-form-item label="当前状态">
          <el-tag :type="currentEmployee.workActive ? 'success' : 'danger'">
            {{ currentEmployee.workActive ? '在职' : '离职' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新状态">
          <el-switch
            v-model="currentEmployee.workActive"
            active-text="在职"
            inactive-text="离职"
          />
        </el-form-item>
        <el-form-item label="分店">
          <el-select
            v-model="currentEmployee.fenDianId"
            placeholder="选择分店"
            clearable
          >
            <el-option
              v-for="item in fenDianOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateEmployeeModalVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateEmployee">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime, thousandSeparator } from '@/utils'
import gql from 'graphql-tag'

const benefitTypeOptions = [
  { value: 'insurance', label: 'လူမှုဖူလုံရေး' },
  { value: 'housing', label: 'အိမ်ရာရန်ပုံငွေ' },
  { value: 'meal', label: 'အစားအသောက်ထောက်ပံ့ကြေး' },
  { value: 'transport', label: 'သယ်ယူပို့ဆောင်ရေးထောက်ပံ့ကြေး' }
]

const statusOptions = [
  { value: true, label: 'အလုပ်လုပ်နေသည်' }, // 正在工作
  { value: false, label: 'အလုပ်ထွက်သွားသည်' } // 离职
]

const workActiveOptions = [
  { value: true, label: 'အလုပ်လုပ်နေသည်' },
  { value: false, label: 'အလုပ်ထွက်သွားသည်' }
]

export default {
  name: 'EmployeeBenefit',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        true: 'success',
        false: 'danger'
      }
      return statusMap[status] || 'info'
    },
    benefitTypeFilter(type) {
      const typeMap = {
        insurance: '',
        housing: 'info',
        meal: 'warning',
        transport: 'success'
      }
      return typeMap[type] || 'info'
    },
    statusNameFilter(value, options) {
      return options.find(item => item.value === value)?.label || '未知状态'
    },
    benefitTypeNameFilter(value, options) {
      return options.find(item => item.value === value)?.label || '未知类型'
    },
    thousandSeparator(value) {
      return thousandSeparator(value)
    }
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        benefitType: undefined,
        workActive: true,
        fenDianId: undefined,
        positionId: undefined,
        sortBy: null,
        sortOrder: null,
        employeeId: undefined
      },
      benefitTypeOptions,
      statusOptions,
      workActiveOptions,
      temp: {
        id: undefined,
        employeeName: '',
        benefitType: '',
        amount: 0,
        effectiveDate: new Date(),
        status: true
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑员工福利',
        create: '新增员工福利'
      },
      rules: {
        employeeName: [{ required: true, message: '员工姓名不能为空', trigger: 'blur' }],
        benefitType: [{ required: true, message: '请选择福利类型', trigger: 'change' }],
        amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }]
      },
      downloadLoading: false,
      passwordDialogVisible: false,
      password: '',
      pendingAction: null,
      pendingRow: null,
      activeName: 'active', // 默认选中全部
      fenDianOptions: [
        { value: 1, label: '分店A' },
        { value: 2, label: '分店B' },
        { value: 3, label: '分店C' },
        { value: 4, label: '分店D' }
      ],
      positionOptions: [], // 新增职位选项
      updateEmployeeModalVisible: false,
      currentEmployee: {
        employeeName: '',
        workActive: false,
        isActive: false
      },
      pendingFenDianUpdate: null // 存储待更新的分店信息
    }
  },
  created() {
    this.getList()
    this.getPositionOptions() // 获取职位选项
  },
  methods: {
    async getList() {
      try {
        this.listLoading = true
        const { data } = await this.$apollo.query({
          query: gql`
              query FetchEmployees(
                $page: Int!, 
                $limit: Int!, 
                $workActive: Boolean,
                $sortBy: String,
                $sortOrder: String,
                $employeeId: Int,
                $fenDianId: Int,
                $positionId: Int
              ) {
                employees(
                  filter: {
                    salaryType: 51883
                    workActive: $workActive
                    employeeId: $employeeId
                    fenDianId: $fenDianId
                    positionId: $positionId
                  }
                  page: $page
                  limit: $limit
                  sortBy: $sortBy
                  sortOrder: $sortOrder
                ) {
                  employees {
                    workActive
                    autoId
                    employeeId
                    employee_mm_name
                    salary_mm_name
                    social_security {
                      amount
                      createDatetime
                      isActive
                    }
                    employeePositionTable {
                      autoId
                      employeeId
                      positionId
                      positionName
                    }
                    workActive   
                    fenDianId
                    isWorking
                  }
                  totalCount
                }
              }
            `,
          variables: {
            page: Number(this.listQuery.page),
            limit: Number(this.listQuery.limit),
            workActive: this.listQuery.workActive,
            sortBy: this.listQuery.sortBy,
            sortOrder: this.listQuery.sortOrder,
            employeeId: parseInt(this.listQuery.employeeId),
            fenDianId: parseInt(this.listQuery.fenDianId),
            positionId: parseInt(this.listQuery.positionId)
          },
          fetchPolicy: 'no-cache'
        })
        this.list = data.employees.employees.map(employee => ({
          id: employee.employeeId,
          employeeName: employee.employee_mm_name,
          benefitType: 'insurance',
          amount: employee.social_security?.amount || 0,
          effectiveDate: employee.social_security?.createDatetime,
          status: employee.workActive,
          social_security: employee.social_security || null,
          fenDianId: employee.fenDianId,
          positionId: employee.employeePositionTable?.positionId || null,
          workActive: employee.workActive || false,
          isActive: employee.social_security?.isActive || false,
          isWorking: employee.isWorking || false
        }))
        this.total = data.employees.totalCount
      } catch (error) {
        console.error(error)
        this.$notify.error({
          title: '错误',
          message: '数据加载失败'
        })
      } finally {
        this.listLoading = false
      }
    },
    handleTabClick(tab) {
      this.activeName = tab.name
      console.log('当前过滤条件:', {
        tab: tab.name,
        workActive: this.listQuery.workActive,
        employeeName: this.listQuery.employeeName
      })
      this.listQuery.workActive = tab.name === 'active' ? true
        : tab.name === 'inactive' ? false
          : undefined
      this.listQuery = { ...this.listQuery }
      this.handleFilter()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.listQuery.workActive = this.activeName === 'active' ? true
        : this.activeName === 'inactive' ? false
          : undefined
      // this.listQuery = {...this.listQuery}
      // this.$emit('filter', this.listQuery)
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        employeeName: '',
        benefitType: '',
        amount: 0,
        effectiveDate: new Date(),
        status: true
      }
    },
    handleCreate(row) {
      console.log(row)
      if (row.social_security === null) {
        // 如果没有社保记录，执行创建操作
        this.resetTemp()
        this.temp.id = row.id
        this.temp.employeeName = row.employeeName
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      } else {
        // 如果已有社保记录，执行更新操作
        this.temp = Object.assign({}, {
          id: row.id,
          employeeName: row.employeeName,
          benefitType: 'insurance',
          amount: row.social_security?.amount || 0,
          effectiveDate: row.social_security?.createDatetime || new Date(),
          status: row.workActive
        })
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
    async createData() {
      try {
        await this.$apollo.mutate({
          mutation: gql`
              mutation CreateSocialSecurity($socialSecurityData: SocialSecurityInput!) {
                createSocialSecurity(socialSecurityData: $socialSecurityData) {
                  autoId
                  employeeId
                  amount
                  createDatetime
                  goodsId
                  isActive
                }
              }
            `,
          variables: {
            socialSecurityData: {
              employeeId: Number(this.temp.id),
              amount: this.temp.amount,
              goodsId: 1
            }
          },
          client: 'client1'
        })

        this.$notify({
          title: 'အောင်မြင်သည်',
          message: 'ဖန်တီးခြင်းအောင်မြင်ပါသည်',
          type: 'success',
          duration: 2000
        })
        this.getList()
        this.dialogFormVisible = false
      } catch (error) {
        console.error('Mutation error:', error)
        this.$notify.error({
          title: 'အမှား',
          message: error.message || 'ဖန်တီးခြင်းမအောင်မြင်ပါ'
        })
      }
    },
    handleUpdate(row) {
      if (row.status === false) {
        // 如果是离职员工，显示密码验证
        this.pendingAction = 'update'
        this.pendingRow = row
        this.passwordDialogVisible = true
      } else {
        // 在职员工直接操作
        this.temp = Object.assign({}, row)
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
    async updateData() {
      try {
        await this.$apollo.mutate({
          mutation: gql`
              mutation UpdateSocialSecurity(
                $employeeId: Int!
                $socialSecurityData: SocialSecurityUpdateInput!
              ) {
                updateSocialSecurity(
                  employeeId: $employeeId
                  socialSecurityData: $socialSecurityData
                ) {
                  autoId
                  employeeId
                  amount
                  createDatetime
                  goodsId
                  isActive
                }
              }
            `,
          variables: {
            employeeId: this.temp.id,
            socialSecurityData: {
              amount: this.temp.amount,
              isActive: this.temp.status
            }
          },
          client: 'client1'
        })

        this.$notify({
          title: 'အောင်မြင်သည်',
          message: 'ပြင်ဆင်ခြင်းအောင်မြင်ပါသည်',
          type: 'success',
          duration: 2000
        })
        this.getList()
        this.dialogFormVisible = false
      } catch (error) {
        console.error('Mutation error:', error)
        this.$notify.error({
          title: 'အမှား',
          message: error.message || 'ပြင်ဆင်ခြင်းမအောင်မြင်ပါ'
        })
      }
    },
    handleDelete(row) {
      if (row.status === false) {
        // 如果是离职员工，显示密码验证
        this.pendingAction = 'delete'
        this.pendingRow = row
        this.passwordDialogVisible = true
      } else {
        // 在职员工直接操作
        this.executeDelete(row)
      }
    },
    handleDownload() {
      this.downloadLoading = true
      import('@/vendor/Export2Excel').then(excel => {
        const tHeader = ['员工姓名', '福利类型', '金额', '生效日期', '状态']
        const filterVal = ['employeeName', 'benefitType', 'amount', 'effectiveDate', 'status']
        const data = this.formatJson(filterVal)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '员工福利列表'
        })
        this.downloadLoading = false
      })
    },
    formatJson(filterVal) {
      return this.list.map(v =>
        filterVal.map(j => {
          if (j === 'effectiveDate') {
            return parseTime(v[j], '{y}-{m}-{d}')
          }
          if (j === 'benefitType') {
            return this.benefitTypeOptions.find(item => item.value === v[j])?.label || ''
          }
          if (j === 'status') {
            return this.statusOptions.find(item => item.value === v[j])?.label || ''
          }
          return v[j]
        })
      )
    },
    async updateEmployee(row) {
      try {
        await this.$apollo.mutate({
          mutation: gql`
              mutation upsertEmployee($employeeId: Int!, $employeeData: EmployeeUpdateInput!) {
                upsertEmployee(employeeId: $employeeId, employeeData: $employeeData) {
                  autoId
                  employeeId
                  workActive
                  fenDianId
                }
              }
            `,
          variables: { employeeId: row.id, employeeData: { workActive: this.currentEmployee.workActive, fenDianId: this.currentEmployee.fenDianId }},
          client: 'client1'
        })
        this.$notify({
          title: '成功',
          message: '员工状态更新成功',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        console.error('Mutation error:', error)
        this.$notify.error({
          title: '错误',
          message: '员工状态更新失败'
        })
      }
    },
    async getPositionOptions() {
      try {
        const { data } = await this.$apollo.query({
          query: gql`
              query MyQuery {
                positionTable {
                  positionId
                  positionName
                }
              }
            `,
          fetchPolicy: 'no-cache'
        })
        this.positionOptions = data.positionTable.map(item => ({
          value: item.positionId,
          label: item.positionName
        }))
      } catch (error) {
        console.error('获取职位数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: '获取职位数据失败'
        })
      }
    },
    async handlePositionChange(row) {
      this.pendingAction = 'updatePosition'
      this.pendingRow = row
      this.passwordDialogVisible = true
    },
    handleSortChange({ prop, order }) {
      this.listQuery.sortBy = prop
      this.listQuery.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.getList()
    },
    openUpdateEmployeeModal(row) {
      this.currentEmployee = {
        ...row,
        workActive: row.workActive || false,
        isActive: row.isActive || false
      }
      this.updateEmployeeModalVisible = true
    },
    async confirmUpdateEmployee() {
      try {
        await this.updateEmployee(this.currentEmployee)
        this.$notify({
          title: '成功',
          message: '员工状态更新成功',
          type: 'success',
          duration: 2000
        })
        this.getList()
      } catch (error) {
        console.error('Mutation error:', error)
        this.$notify.error({
          title: '错误',
          message: '员工状态更新失败'
        })
      } finally {
        this.updateEmployeeModalVisible = false
      }
    },
    handleFenDianChange(row) {
      this.pendingFenDianUpdate = row // 存储待更新的分店信息
      this.passwordDialogVisible = true // 显示密码验证对话框
    },
    async verifyPassword() {
      const correctPassword = '123456' // 设置正确的密码
      if (this.password === correctPassword) {
        this.updateFenDian(this.pendingFenDianUpdate) // 密码正确，执行分店更新
        this.passwordDialogVisible = false // 关闭密码验证对话框
        this.password = '' // 清空密码输入
      } else {
        this.$notify.error({
          title: '错误',
          message: '密码错误'
        })
      }
    },
    async updateFenDian(row) {
      try {
        await this.$apollo.mutate({
          mutation: gql`
            mutation upsertEmployee($employeeId: Int!, $employeeData: EmployeeUpdateInput!) {
              upsertEmployee(employeeId: $employeeId, employeeData: $employeeData) {
                employeeId
                fenDianId
              }
            }
          `,
          variables: {
            employeeId: row.id,
            employeeData: { fenDianId: row.fenDianId }
          }
        })
        this.$notify({
          title: '成功',
          message: '分店更新成功',
          type: 'success',
          duration: 2000
        })
        this.getList() // 重新获取列表以反映更新
      } catch (error) {
        console.error('更新分店失败:', error)
        this.$notify.error({
          title: '错误',
          message: '分店更新失败'
        })
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
  .filter-container {
    padding-bottom: 20px;
    .filter-item {
      margin-right: 10px;
    }
  }
  </style>
