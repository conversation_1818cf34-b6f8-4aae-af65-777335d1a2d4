<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="员工管理" name="employee">
        <employee-tabs
          :list-query="listQuery"
          @filter="handleFilter"
          @refresh="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="福利管理" name="benefit">
        <benefit-tabs
          :list-query="listQuery"
          @filter="handleFilter"
          @refresh="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="新增员工" name="newEmployee">
        <new-employee />
      </el-tab-pane>
      <!-- <el-tab-pane label="Cycle Graph" name="cycleGraph">
        <cycle-graph />
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import BenefitTabs from './components/BenefitTabs.vue'
import EmployeeTabs from './components/EmployeeTabs.vue'
// import CycleGraph from './components/CycleGraph.vue'
import NewEmployee from './components/NewEmployee.vue'
export default {
  name: 'EmployeeIndex',
  components: {
    BenefitTabs,
    EmployeeTabs,
    // CycleGraph,
    NewEmployee
  },
  data() {
    return {
      activeTab: 'employee', // 当前激活的标签页
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
    }
  },
  methods: {
    handleFilter(query) {
      this.listQuery = { ...query }
      this.getList()
    },
    // 新增标签切换处理
    handleTabClick(tab) {
      // 切换标签时重置查询条件
      this.listQuery = {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
      this.getList()
    },
    async getList() {
      // 根据当前激活的标签调用不同的API
      if (this.activeTab === 'benefit') {
        // 调用福利管理接口
      } else {
        // 调用员工管理接口
      }
    }
  }
}
</script>
