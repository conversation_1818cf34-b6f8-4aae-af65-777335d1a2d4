<template>
  <div class="duty-info-container">
    <div class="page-header">
      <h2>职责商品信息管理</h2>
      <el-button type="primary" @click="openAddDialog">添加商品信息</el-button>
    </div>

    <!-- 商品信息查询组件 -->
    <goods-info-query ref="goodsInfoQuery" @update-list="updateGoodsList" />

    <!-- 商品信息表格组件 -->
    <goods-info-table
      :loading="loading"
      :goods-info-list="goodsInfoList"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @edit="handleEdit"
      @delete="handleDelete"
    />

    <!-- 添加/编辑商品信息表单组件 -->
    <goods-info-form
      :visible="dialogVisible"
      :title="dialogTitle"
      :form="form"
      :is-edit="isEdit"
      @close="dialogVisible = false"
      @submit="submitForm"
    />
  </div>
</template>

<script>
import { companyClient } from '@/apolloClient'
import gql from 'graphql-tag'
import GoodsInfoTable from './components/GoodsInfoTable.vue'
import GoodsInfoForm from './components/GoodsInfoForm.vue'
import GoodsInfoQuery from './components/GoodsInfoQuery.vue'

export default {
  name: 'DutyInfo',
  components: {
    GoodsInfoTable,
    GoodsInfoForm,
    GoodsInfoQuery
  },
  data() {
    return {
      goodsInfoList: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      isEdit: false,
      form: {
        autoId: null,
        goodsId: '',
        goodsIdName: '',
        av: 0,
        bank: 0,
        commercial: 0,
        custom: 0,
        income: 0,
        po: 0
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑商品信息' : '添加商品信息'
    }
  },
  methods: {
    // 更新商品列表数据（由查询组件触发）
    updateGoodsList(data) {
      this.goodsInfoList = data
      this.total = data.length
    },

    // 打开添加对话框
    openAddDialog() {
      this.isEdit = false
      this.resetForm()
      this.dialogVisible = true
    },

    // 处理编辑
    handleEdit(row) {
      this.isEdit = true
      this.form = {
        autoId: row.autoId,
        goodsId: row.goodsId,
        goodsIdName: row.goodsId.toString(),
        av: row.av,
        bank: row.bank,
        commercial: row.commercial,
        custom: row.custom,
        income: row.income,
        po: row.po
      }
      this.dialogVisible = true
    },

    // 处理删除
    handleDelete(row) {
      this.$confirm('确认删除该商品信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现删除逻辑
        companyClient.mutate({
          mutation: gql`
            mutation DeleteDutyGoodsInfo($autoId: Int!) {
              dutyHead {
                deleteDutyGoodsInfo(autoId: $autoId)
              }
            }
          `,
          variables: {
            autoId: row.autoId
          }
        })
          .then(response => {
            this.$message.success('删除成功')
            // 删除后刷新数据
            if (this.$refs.goodsInfoQuery) {
              this.$refs.goodsInfoQuery.handleQueryAll()
            }
          })
          .catch(error => {
            console.error('删除商品信息失败:', error)
            this.$message.error('删除商品信息失败')
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 提交表单
    submitForm(formData) {
      if (this.isEdit) {
        this.updateGoodsInfo(formData)
      } else {
        this.addGoodsInfo(formData)
      }
    },

    // 添加商品信息
    addGoodsInfo(formData) {
      companyClient.mutate({
        mutation: gql`
          mutation InsertDutyGoodsInfo(
            $goodsId: Int!
            $av: Float!
            $bank: Float!
            $commercial: Float!
            $custom: Float!
            $income: Float!
            $po: Float!
          ) {
            dutyHead {
              insertDutyGoodsInfo(
                goodsId: $goodsId
                av: $av
                bank: $bank
                commercial: $commercial
                custom: $custom
                income: $income
                po: $po
              ) {
                autoId
                goodsId
                av
                bank
                commercial
                custom
                income
                po
                createDatetime
              }
            }
          }
        `,
        variables: {
          goodsId: parseInt(formData.goodsId),
          av: parseFloat(formData.av),
          bank: parseFloat(formData.bank),
          commercial: parseFloat(formData.commercial),
          custom: parseFloat(formData.custom),
          income: parseFloat(formData.income),
          po: parseFloat(formData.po)
        }
      })
        .then(response => {
          this.$message.success('添加成功')
          this.dialogVisible = false
          // 添加成功后刷新数据
          if (this.$refs.goodsInfoQuery) {
            this.$refs.goodsInfoQuery.handleQueryAll()
          }
        })
        .catch(error => {
          console.error('添加商品信息失败:', error)
          this.$message.error('添加商品信息失败')
        })
    },

    // 更新商品信息
    updateGoodsInfo(formData) {
      companyClient.mutate({
        mutation: gql`
          mutation UpdateDutyGoodsInfo(
            $autoId: Int!
            $goodsId: Int!
            $av: Float!
            $bank: Float!
            $commercial: Float!
            $custom: Float!
            $income: Float!
            $po: Float!
          ) {
            dutyHead {
              updateDutyGoodsInfo(
                autoId: $autoId
                goodsId: $goodsId
                av: $av
                bank: $bank
                commercial: $commercial
                custom: $custom
                income: $income
                po: $po
              ) {
                autoId
                goodsId
                av
                bank
                commercial
                custom
                income
                po
                createDatetime
              }
            }
          }
        `,
        variables: {
          autoId: formData.autoId,
          goodsId: parseInt(formData.goodsId),
          av: parseFloat(formData.av),
          bank: parseFloat(formData.bank),
          commercial: parseFloat(formData.commercial),
          custom: parseFloat(formData.custom),
          income: parseFloat(formData.income),
          po: parseFloat(formData.po)
        }
      })
        .then(response => {
          this.$message.success('更新成功')
          this.dialogVisible = false
          // 更新成功后刷新数据
          if (this.$refs.goodsInfoQuery) {
            this.$refs.goodsInfoQuery.handleQueryAll()
          }
        })
        .catch(error => {
          console.error('更新商品信息失败:', error)
          this.$message.error('更新商品信息失败')
        })
    },

    // 重置表单
    resetForm() {
      this.form = {
        autoId: null,
        goodsId: '',
        goodsIdName: '',
        av: 0,
        bank: 0,
        commercial: 0,
        custom: 0,
        income: 0,
        po: 0
      }
    },

    // 处理分页大小变化
    handleSizeChange(size) {
      this.pageSize = size
      if (this.$refs.goodsInfoQuery) {
        this.$refs.goodsInfoQuery.handleSizeChange(size)
      }
    },

    // 处理页码变化
    handleCurrentChange(page) {
      this.currentPage = page
      if (this.$refs.goodsInfoQuery) {
        this.$refs.goodsInfoQuery.handleCurrentChange(page)
      }
    }
  }
}
</script>

<style scoped>
.duty-info-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
