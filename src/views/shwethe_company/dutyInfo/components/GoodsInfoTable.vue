<template>
  <div>
    <!-- 商品信息列表 -->
    <el-table
      v-loading="loading"
      :data="goodsInfoList"
      border
      style="width: 100%"
    >
      <el-table-column prop="autoId" label="ID" width="80" />
      <el-table-column prop="goodsId" label="商品ID" width="100" />
      <el-table-column prop="av" label="AV" width="100" />
      <el-table-column prop="bank" label="银行" width="100" />
      <el-table-column prop="commercial" label="商业" width="100" />
      <el-table-column prop="custom" label="海关" width="100" />
      <el-table-column prop="income" label="收入" width="100" />
      <el-table-column prop="po" label="PO" width="100" />
      <el-table-column prop="createDatetime" label="创建时间" width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createDatetime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="$emit('edit', scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="$emit('delete', scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'GoodsInfoTable',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    goodsInfoList: {
      type: Array,
      default: () => []
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    }
  },
  methods: {
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 