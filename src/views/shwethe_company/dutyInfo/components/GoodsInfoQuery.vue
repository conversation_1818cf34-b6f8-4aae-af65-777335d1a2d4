<template>
  <div class="goods-info-query">
    <div class="query-header">
      <h3>商品信息查询</h3>
    </div>
    
    <el-card class="query-card">
      <el-form :inline="true" :model="queryForm" class="query-form">
        <el-form-item label="商品ID">
          <el-input v-model="queryForm.goodsId" placeholder="请输入商品ID" class="custom-input"></el-input>
        </el-form-item>
        <el-form-item class="button-group">
          <el-button type="primary" @click="handleQuery" :loading="loading" icon="el-icon-search">查询</el-button>
          <el-button @click="resetQuery" icon="el-icon-refresh">重置</el-button>
          <el-button type="success" @click="handleQueryAll" :loading="loadingAll" icon="el-icon-menu">查询全部</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <transition name="fade">
      <el-card v-if="queryResult && !showAllData" class="query-result-card">
        <div slot="header" class="card-header">
          <span>商品信息详情</span>
        </div>
        <el-descriptions border :column="2" class="custom-descriptions">
          <el-descriptions-item label="ID">
            <el-tag size="medium">{{ queryResult.autoId }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="商品ID">
            <el-tag size="medium" type="success">{{ queryResult.goodsId }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="AV">{{ queryResult.av }}</el-descriptions-item>
          <el-descriptions-item label="银行">{{ queryResult.bank }}</el-descriptions-item>
          <el-descriptions-item label="商业">{{ queryResult.commercial }}</el-descriptions-item>
          <el-descriptions-item label="海关">{{ queryResult.custom }}</el-descriptions-item>
          <el-descriptions-item label="收入">{{ queryResult.income }}</el-descriptions-item>
          <el-descriptions-item label="PO">{{ queryResult.po }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">
            <i class="el-icon-time"></i> {{ formatDate(queryResult.createDatetime) }}
          </el-descriptions-item>
          <el-descriptions-item label="商品名称信息" :span="2" v-if="queryResult.goodsIdInfo">
            <div class="product-detail-display">
              <div class="product-detail-header">
                <span class="product-detail-idname">{{ queryResult.goodsIdInfo.idname }}</span>
                <span class="product-detail-id">ID: {{ queryResult.goodsIdInfo.id }}</span>
              </div>
              <div class="product-detail-body">
                <div class="product-detail-names">
                  <div v-if="queryResult.goodsIdInfo.mmName" class="product-detail-name mm-name">{{ queryResult.goodsIdInfo.mmName }}</div>
                  <div v-if="queryResult.goodsIdInfo.dName" class="product-detail-name d-name">{{ queryResult.goodsIdInfo.dName }}</div>
                  <div v-if="queryResult.goodsIdInfo.thName" class="product-detail-name th-name">{{ queryResult.goodsIdInfo.thName }}</div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </transition>
    
    <!-- 全部商品信息表格 -->
    <transition name="fade">
      <el-card v-if="showAllData" class="all-data-card">
        <div slot="header" class="card-header">
          <span>全部商品信息</span>
          <el-input
            placeholder="搜索商品..."
            v-model="searchText"
            class="search-input"
            prefix-icon="el-icon-search"
            clearable
          ></el-input>
        </div>
        <el-table
          v-loading="loadingAll"
          :data="filteredGoodsInfo"
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          :row-class-name="tableRowClassName"
          highlight-current-row
        >
          <el-table-column prop="autoId" label="ID" width="80">
            <template slot-scope="scope">
              <el-tag size="small">{{ scope.row.autoId }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="goodsId" label="商品ID" width="100">
            <template slot-scope="scope">
              <el-tag size="small" type="success">{{ scope.row.goodsId }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="av" label="AV" width="100" />
          <el-table-column prop="bank" label="银行" width="100" />
          <el-table-column prop="commercial" label="商业" width="100" />
          <el-table-column prop="custom" label="海关" width="100" />
          <el-table-column prop="income" label="收入" width="100" />
          <el-table-column prop="po" label="PO" width="100" />
          <el-table-column prop="createDatetime" label="创建时间" width="180">
            <template slot-scope="scope">
              <i class="el-icon-time"></i> {{ formatDate(scope.row.createDatetime) }}
            </template>
          </el-table-column>
          <el-table-column label="商品名称信息" min-width="250">
            <template slot-scope="scope">
              <div v-if="scope.row.goodsIdInfo" class="product-display">
                <div class="product-display-id">{{ scope.row.goodsIdInfo.idname }}</div>
                <div class="product-display-names">
                  <div v-if="scope.row.goodsIdInfo.mmName" class="product-display-name mm-name">{{ scope.row.goodsIdInfo.mmName }}</div>
                  <div v-if="scope.row.goodsIdInfo.dName" class="product-display-name d-name">{{ scope.row.goodsIdInfo.dName }}</div>
                </div>
              </div>
              <el-tag v-else type="info" size="small">无商品名称信息</el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </transition>
    
    <transition name="fade">
      <div v-if="showNoData" class="no-data">
        <el-empty description="未找到相关商品信息">
          <el-button type="primary" @click="handleQueryAll">查询全部商品</el-button>
        </el-empty>
      </div>
    </transition>
  </div>
</template>

<script>
import { companyClient } from '@/apolloClient'
import gql from 'graphql-tag'

export default {
  name: 'GoodsInfoQuery',
  data() {
    return {
      queryForm: {
        goodsId: ''
      },
      queryResult: null,
      loading: false,
      hasQueried: false,
      searchText: '',
      
      // 全部数据相关
      allGoodsInfo: [],
      loadingAll: false,
      showAllData: false,
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    showNoData() {
      return this.hasQueried && !this.queryResult && !this.showAllData
    },
    filteredGoodsInfo() {
      if (!this.searchText) return this.allGoodsInfo
      
      const searchLower = this.searchText.toLowerCase()
      return this.allGoodsInfo.filter(item => {
        return (
          (item.goodsId && item.goodsId.toString().includes(searchLower)) ||
          (item.goodsIdInfo && item.goodsIdInfo.dName && item.goodsIdInfo.dName.toLowerCase().includes(searchLower)) ||
          (item.goodsIdInfo && item.goodsIdInfo.mmName && item.goodsIdInfo.mmName.toLowerCase().includes(searchLower)) ||
          (item.goodsIdInfo && item.goodsIdInfo.thName && item.goodsIdInfo.thName.toLowerCase().includes(searchLower)) ||
          (item.goodsIdInfo && item.goodsIdInfo.idname && item.goodsIdInfo.idname.toLowerCase().includes(searchLower))
        )
      })
    }
  },
  mounted() {
    this.handleQueryAll()
  },
  methods: {
    handleQuery() {
      if (!this.queryForm.goodsId) {
        this.$message.warning('请输入商品ID')
        return
      }
      
      this.loading = true
      this.hasQueried = true
      this.showAllData = false
      
      companyClient.query({
        query: gql`
          query GetDutyGoodsInfo($goodsId: Int!) {
            statistics {
              getDutyGoodsInfo(goodsId: $goodsId) {
                autoId
                av
                bank
                commercial
                createDatetime
                custom
                goodsId
                income
                po
                goodsIdInfo {
                  dName
                  id
                  idname
                  mmName
                  thName
                }
              }
            }
          }
        `,
        variables: {
          goodsId: parseInt(this.queryForm.goodsId)
        },
        fetchPolicy: 'network-only'
      })
        .then(response => {
          this.queryResult = response.data.statistics.getDutyGoodsInfo
          if (!this.queryResult) {
            this.$message.info('未找到该商品信息')
          }
          this.loading = false
        })
        .catch(error => {
          console.error('查询商品信息失败:', error)
          this.$message.error('查询商品信息失败')
          this.loading = false
          this.queryResult = null
        })
    },
    
    // 查询所有商品信息
    handleQueryAll() {
      this.loadingAll = true
      this.showAllData = true
      this.queryResult = null
      this.searchText = ''
      
      this.fetchAllGoodsInfo()
    },
    
    // 获取所有商品信息
    fetchAllGoodsInfo() {
      companyClient.query({
        query: gql`
          query GetAllDutyGoodsInfo($page: Int!, $limit: Int!) {
            statistics {
              getAllDutyGoodsInfo(page: $page, limit: $limit) {
                autoId
                av
                bank
                commercial
                createDatetime
                custom
                goodsId
                income
                po
                goodsIdInfo {
                  dName
                  id
                  idname
                  mmName
                  thName
                }
              }
            }
          }
        `,
        variables: {
          page: this.currentPage,
          limit: this.pageSize
        },
        fetchPolicy: 'network-only'
      })
        .then(response => {
          this.allGoodsInfo = response.data.statistics.getAllDutyGoodsInfo || []
          // 由于可能没有分页信息，我们设置一个默认值
          this.total = this.allGoodsInfo.length
          this.loadingAll = false
          
          // 向父组件发送数据更新事件
          this.$emit('update-list', this.allGoodsInfo)
        })
        .catch(error => {
          console.error('获取所有商品信息失败:', error)
          this.$message.error('获取所有商品信息失败')
          this.loadingAll = false
        })
    },
    
    resetQuery() {
      this.queryForm.goodsId = ''
      this.queryResult = null
      this.hasQueried = false
      this.showAllData = false
      this.searchText = ''
    },
    
    // 处理分页大小变化
    handleSizeChange(size) {
      this.pageSize = size
      this.fetchAllGoodsInfo()
    },

    // 处理页码变化
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchAllGoodsInfo()
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    tableRowClassName({row, rowIndex}) {
      if (rowIndex % 2 === 0) {
        return 'even-row'
      }
      return 'odd-row'
    }
  }
}
</script>

<style scoped>
.goods-info-query {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.query-header {
  margin-bottom: 20px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 10px;
}

.query-header h3 {
  font-size: 20px;
  color: #303133;
  font-weight: 600;
  margin: 0;
}

.query-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.query-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.custom-input {
  width: 220px;
}

.button-group {
  margin-left: 10px;
}

.query-result-card, .all-data-card {
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-input {
  width: 220px;
}

.custom-descriptions {
  margin-top: 10px;
}

.all-data-table {
  margin-top: 20px;
}

.all-data-table h4 {
  margin-bottom: 15px;
  font-weight: 500;
  color: #303133;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.no-data {
  margin-top: 40px;
  text-align: center;
}

/* 商品信息卡片样式 - 完全重新设计 */
.product-display {
  display: flex;
  flex-direction: column;
  padding: 5px 0;
}

.product-display-id {
  font-weight: bold;
  color: #409EFF;
  font-size: 13px;
  margin-bottom: 4px;
}

.product-display-names {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-display-name {
  font-size: 12px;
  line-height: 1.4;
}

.mm-name {
  color: #606266;
}

.d-name {
  color: #909399;
}

.th-name {
  color: #C0C4CC;
}

/* 详情页商品信息样式 - 完全重新设计 */
.product-detail-display {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.product-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.product-detail-idname {
  font-weight: bold;
  color: #409EFF;
  font-size: 14px;
}

.product-detail-id {
  color: #909399;
  font-size: 13px;
}

.product-detail-body {
  padding: 12px 15px;
  background-color: #FFFFFF;
}

.product-detail-names {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-detail-name {
  font-size: 13px;
  line-height: 1.5;
  padding-bottom: 8px;
  border-bottom: 1px dashed #EBEEF5;
}

.product-detail-name:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

/* 表格行样式 */
/deep/ .even-row {
  background-color: #fafafa;
}

/deep/ .odd-row {
  background-color: #ffffff;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style> 