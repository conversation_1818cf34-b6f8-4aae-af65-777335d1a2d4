<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="50%"
    @close="$emit('close')"
  >
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="商品ID" prop="goodsIdName">
        <el-autocomplete
          v-model="formData.goodsIdName"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入商品ID或名称搜索"
          style="width: 100%"
          @select="handleSelect"
        >
          <template slot-scope="{ item }">
            <div class="product-item">
              <div class="product-id">{{ item.idname }}</div>
              <div class="product-name">
                <div>{{ item.mmName || '' }}</div>
                <div>{{ item.dName || '' }}</div>
              </div>
            </div>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="AV" prop="av">
        <el-input-number v-model="formData.av" :precision="2" :step="0.1" :min="0" style="width: 100%" />
      </el-form-item>
      <el-form-item label="银行" prop="bank">
        <el-input-number v-model="formData.bank" :precision="2" :step="0.1" :min="0" style="width: 100%" />
      </el-form-item>
      <el-form-item label="商业" prop="commercial">
        <el-input-number v-model="formData.commercial" :precision="2" :step="0.1" :min="0" style="width: 100%" />
      </el-form-item>
      <el-form-item label="海关" prop="custom">
        <el-input-number v-model="formData.custom" :precision="2" :step="0.1" :min="0" style="width: 100%" />
      </el-form-item>
      <el-form-item label="收入" prop="income">
        <el-input-number v-model="formData.income" :precision="2" :step="0.1" :min="0" style="width: 100%" />
      </el-form-item>
      <el-form-item label="PO" prop="po">
        <el-input-number v-model="formData.po" :precision="2" :step="0.1" :min="0" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { companyClient } from '@/apolloClient'
import gql from 'graphql-tag'

export default {
  name: 'GoodsInfoForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '添加商品信息'
    },
    form: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        autoId: null,
        goodsId: '',
        goodsIdName: '',
        av: 0,
        bank: 0,
        commercial: 0,
        custom: 0,
        income: 0,
        po: 0
      },
      rules: {
        goodsIdName: [
          { required: true, message: '请选择商品', trigger: 'blur' }
        ],
        av: [
          { required: true, message: '请输入AV值', trigger: 'blur' }
        ],
        bank: [
          { required: true, message: '请输入银行值', trigger: 'blur' }
        ],
        commercial: [
          { required: true, message: '请输入商业值', trigger: 'blur' }
        ],
        custom: [
          { required: true, message: '请输入海关值', trigger: 'blur' }
        ],
        income: [
          { required: true, message: '请输入收入值', trigger: 'blur' }
        ],
        po: [
          { required: true, message: '请输入PO值', trigger: 'blur' }
        ]
      },
      searchTimeout: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    form: {
      handler(newVal) {
        this.formData = { ...newVal }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.formData)
        }
      })
    },

    // 搜索商品
    querySearchAsync(queryString, callback) {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout)
      }
      
      if (queryString.length < 1) {
        callback([])
        return
      }
      
      this.searchTimeout = setTimeout(() => {
        companyClient.query({
          query: gql`
            query searchProducts($keyword: String!, $limit: Int!, $skip: Int!) {
              statistics {
                searchProducts(keyword: $keyword, limit: $limit, skip: $skip) {
                  dName
                  id
                  idname
                  mmName
                  thName
                }
              }
            }
          `,
          variables: {
            keyword: queryString,
            limit: 10,
            skip: 0
          }
        }).then(response => {
          const results = response.data.statistics.searchProducts || []
          callback(results)
        }).catch(error => {
          console.error('搜索商品出错:', error)
          callback([])
          this.$message.error('搜索商品失败!')
        })
      }, 300)
    },

    // 选择商品
    handleSelect(item) {
      this.formData.goodsId = String(item.id)
      this.formData.goodsIdName = item.idname
    }
  }
}
</script>

<style scoped>
.product-item {
  display: flex;
  padding: 10px 0;
  align-items: flex-start;
  width: 100%;
  border-bottom: 1px solid #eee;
}

.product-id {
  font-weight: bold;
  color: #409EFF;
  min-width: 120px;
  margin-right: 20px;
  white-space: nowrap;
}

.product-name {
  color: #606266;
  font-size: 12px;
  flex-grow: 1;
  text-align: left;
  word-break: break-word;
  line-height: 1.6;
  max-width: 400px;
}

/* 增加下拉菜单宽度 */
::v-deep .el-autocomplete-suggestion {
  min-width: 600px !important;
  max-width: 800px !important;
}

/* 调整下拉菜单项的样式 */
::v-deep .el-autocomplete-suggestion__list {
  padding: 10px;
}

/* 增加下拉菜单的最大高度 */
::v-deep .el-autocomplete-suggestion__wrap {
  max-height: 500px;
}
</style> 