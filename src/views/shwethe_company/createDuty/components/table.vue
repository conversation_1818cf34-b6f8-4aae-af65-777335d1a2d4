<template>
  <div class="test">
    <div class="table-header">
      <h3>公司职责列表</h3>
      <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="dutyHeads"
      style="width: 100%"
    >

      <el-table-column label="ID" prop="autoId" />
      <el-table-column label="公司ID" prop="companyId" />
      <el-table-column label="创建时间" prop="createDatetime">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createDatetime) }}
        </template>
      </el-table-column>
      <el-table-column label="年度" prop="yearRange" />

      <el-table-column align="right">
        <template slot="header" slot-scope="scope">
          <el-input
            v-model="search"
            size="mini"
            placeholder="输入关键字搜索"
          />
        </template>
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleView(scope.$index, scope.row)">查看</el-button>
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">添加文档</el-button>
          <el-button size="mini" type="success" @click="handleGoods(scope.$index, scope.row)">商品</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      :current-page="pagination.currentPage"
      :page-sizes="[5, 10, 20, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.totalItems"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog
      :visible.sync="showModal"
      :title="`添加文档到职责 #${editedItem.autoId}`"
      width="50%"
    >
      <div class="form_container">
        <el-form ref="documentForm" :model="documentForm">
          <el-form-item label="文档ID" prop="documentId">
            <div class="form_add">
              <el-input v-model.number="documentForm.documentId" type="number" placeholder="请输入文档ID" />
            </div>
          </el-form-item>
          <el-form-item label="过期日期" prop="expiry_date">
            <div class="form_add">
              <el-date-picker
                v-model="documentForm.expiry_date"
                type="date"
                placeholder="选择过期日期"
                value-format="yyyy-MM-dd"
              />
            </div>
          </el-form-item>
          <el-form-item label="文档费用" prop="document_fee">
            <div class="form_add">
              <el-input
                v-model="documentForm.document_fee"
                type="number"
                placeholder="请输入文档费用"
                :min="0"
                :step="0.01"
              />
            </div>
          </el-form-item>
          <el-button type="success" style="margin-top: 12px" @click="addDocument">
            添加</el-button>
        </el-form>

        <div>
          <h4>文档列表</h4>
          <el-table
            v-loading="documentsLoading"
            :data="dutyDocuments"
            style="width: 100%"
          >
            <el-table-column
              prop="documentId"
              label="文档ID"
            />
            <el-table-column
              prop="dutyHeadId"
              label="职责ID"
            />

            <el-table-column label="过期日期" prop="expiryDate" />
            <el-table-column label="文档费用" prop="documentFee" />
            <el-table-column
              label="操作"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="viewDocument(scope.row)"
                >查看</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click="deleteDocument(scope.row)"
                >删除</el-button>
                <el-button
                  size="mini"
                  type="success"
                  @click="handleEditDocument(scope.row)"
                >编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="showModal = false">关闭</el-button>
          <el-button type="primary" @click="refreshDocuments">刷新文档列表</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="showViewModal"
      :title="`职责详情 #${viewItem.autoId}`"
      width="50%"
    >
      <div class="form_container">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="ID">{{ viewItem.autoId }}</el-descriptions-item>
          <el-descriptions-item label="公司ID">{{ viewItem.companyId }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(viewItem.createDatetime) }}</el-descriptions-item>
          <el-descriptions-item label="年度">{{ viewItem.yearRange }}</el-descriptions-item>
          <el-descriptions-item label="API ID">{{ viewItem.apiId || '无' }}</el-descriptions-item>
          <el-descriptions-item label="API 表">{{ viewItem.apiTable || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div class="document-section">
          <h4>关联文档</h4>
          <el-table
            v-loading="documentsLoading"
            :data="dutyDocuments"
            style="width: 100%"
          >
            <el-table-column
              prop="documentId"
              label="文档ID"
            />
            <el-table-column
              prop="dutyHeadId"
              label="职责ID"
            />
          </el-table>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="showViewModal = false">关闭</el-button>
          <el-button type="primary" @click="refreshViewDocuments">刷新文档列表</el-button>
        </div>
      </div>
    </el-dialog>

    <modal-goods
      :visible="showDocumentModal"
      :duty-head-id="currentDocument.dutyHeadId || 0"
      :document-id="currentDocument.documentId"
      title-prefix="文档详情"
      mode="document"
      @update:visible="val => showDocumentModal = val"
    />

    <modal-goods
      :visible="showGoodsModal"
      :duty-head-id="currentDutyHeadId"
      mode="goods"
      @update:visible="val => showGoodsModal = val"
    />
  </div>
</template>

<script>
import ModalGoods from './modal_goods.vue'

export default {
  components: {
    ModalGoods
  },

  data() {
    return {
      search: '',
      showModal: false,
      showViewModal: false,
      showDocumentModal: false,
      editedItem: {},
      viewItem: {},
      currentDocument: {},
      documentForm: {
        documentId: null,
        expiry_date: '',
        document_fee: ''
      },
      showGoodsModal: false,
      currentDutyHeadId: null,
      relatedGoods: [],
      relatedGoodsLoading: false,
      goodsCurrentPage: 1,
      goodsPageSize: 10,
      goodsTotal: 0
    }
  },

  computed: {
    dutyHeads() {
      const searchQuery = this.search.toLowerCase()
      const data = this.$store.state.shwetheCompany.dutyHeads
      if (!searchQuery) return data

      return data.filter(item => {
        return Object.keys(item).some(key => {
          const value = item[key]
          if (value === null || value === undefined) return false
          return String(value).toLowerCase().includes(searchQuery)
        })
      })
    },
    loading() {
      return this.$store.state.shwetheCompany.loading
    },
    pagination() {
      return this.$store.state.shwetheCompany.pagination
    },
    dutyDocuments() {
      return this.$store.state.shwetheCompany.dutyDocuments
    },
    documentsLoading() {
      return this.$store.state.shwetheCompany.documentsLoading
    }
  },

  created() {
    console.log('表格组件创建')
    this.fetchData()
  },

  mounted() {
    console.log('表格组件挂载')
    console.log('Vuex 状态:', this.$store.state)
    console.log('shwetheCompany 模块:', this.$store.state.shwetheCompany)
  },

  methods: {
    fetchData() {
      console.log('调用 fetchData 方法')
      this.$store.dispatch('shwetheCompany/fetchDutyHeads', {
        page: 1,
        pageSize: 10
      }).then(() => {
        console.log('数据获取完成')
      }).catch(error => {
        console.error('获取数据时出错:', error)
      })
    },

    refreshData() {
      console.log('调用 refreshData 方法')
      this.$store.dispatch('shwetheCompany/fetchDutyHeads', {
        page: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        forceRefresh: true
      }).then(() => {
        this.$message({
          type: 'success',
          message: '数据刷新成功!'
        })
      }).catch(error => {
        this.$message({
          type: 'error',
          message: '数据刷新失败!'
        })
        console.error('刷新数据时出错:', error)
      })
    },

    handleSizeChange(size) {
      this.$store.dispatch('shwetheCompany/fetchDutyHeads', {
        page: this.pagination.currentPage,
        pageSize: size
      })
    },

    handleCurrentChange(page) {
      this.$store.dispatch('shwetheCompany/fetchDutyHeads', {
        page: page,
        pageSize: this.pagination.pageSize
      })
    },

    handleView(index, row) {
      this.viewItem = { ...row }
      this.showViewModal = true

      this.fetchViewDocuments()
    },

    fetchViewDocuments() {
      this.$store.dispatch('shwetheCompany/fetchDutyDocuments', this.viewItem.autoId)
        .catch(error => {
          this.$message({
            type: 'error',
            message: '获取文档列表失败!'
          })
        })
    },

    refreshViewDocuments() {
      this.fetchViewDocuments()
      this.$message({
        type: 'info',
        message: '正在刷新文档列表...'
      })
    },

    handleEdit(index, row) {
      this.editedItem = { ...row }
      this.documentForm.documentId = null
      this.showModal = true

      this.fetchDocuments()
    },

    handleDelete(index, row) {
      this.$confirm('确认删除此记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('shwetheCompany/deleteDutyHead', row.autoId).then(result => {
          if (result.success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: '删除失败!'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    fetchDocuments() {
      this.$store.dispatch('shwetheCompany/fetchDutyDocuments', this.editedItem.autoId)
        .catch(error => {
          this.$message({
            type: 'error',
            message: '获取文档列表失败!'
          })
        })
    },

    refreshDocuments() {
      this.fetchDocuments()
      this.$message({
        type: 'info',
        message: '正在刷新文档列表...'
      })
    },

    addDocument() {
      console.log('添加文档')
      console.log(this.documentForm.documentId)
      console.log(this.editedItem.autoId)
      console.log(this.documentForm)
      if (!this.documentForm.documentId) {
        this.$message({
          type: 'warning',
          message: '请输入文档ID'
        })
        return
      }

      if (!this.documentForm.document_fee || isNaN(this.documentForm.document_fee)) {
        this.$message({
          type: 'warning',
          message: '请输入有效的文档费用'
        })
        return
      }

      this.$store.dispatch('shwetheCompany/insertDutyDocument', {
        documentId: String(this.documentForm.documentId),
        dutyHeadId: parseInt(this.editedItem.autoId),
        expiryDate: this.documentForm.expiry_date,
        documentFee: parseFloat(this.documentForm.document_fee) || 0
      }).then(result => {
        if (result.success) {
          this.$message({
            type: 'success',
            message: '文档添加成功!'
          })

          this.documentForm.documentId = null
          this.documentForm.expiry_date = ''
          this.documentForm.document_fee = ''

          this.fetchDocuments()
        } else {
          this.$message({
            type: 'error',
            message: '文档添加失败!'
          })
        }
      })
    },

    deleteDocument(document) {
      this.$confirm('确认删除此文档?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '文档删除成功!'
        })
        this.fetchDocuments()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    viewDocument(document) {
      this.$store.commit('shwetheCompany/SET_DUTY_DOCUMENT_AUTO_ID', document.autoId)
      this.currentDocument = {
        ...document,
        dutyHeadId: document.dutyHeadId || 0,
        dutyDocumentAutoId: document.autoId || 0
      }
      this.showDocumentModal = true
    },

    fetchRelatedGoods() {
      this.relatedGoodsLoading = true

      setTimeout(() => {
        this.relatedGoods = [
          {
            autoId: 1,
            goodsId: 'G001',
            qty: 2.5,
            dutyDocumentId: this.currentDocument.documentId,
            dutyHeadId: this.currentDocument.dutyHeadId,
            createDatetime: new Date().toISOString()
          },
          {
            autoId: 2,
            goodsId: 'G002',
            qty: 1.0,
            dutyDocumentId: this.currentDocument.documentId,
            dutyHeadId: this.currentDocument.dutyHeadId,
            createDatetime: new Date().toISOString()
          }
        ]
        this.goodsTotal = 2
        this.relatedGoodsLoading = false
      }, 500)
    },

    handleGoodsSizeChange(size) {
      this.goodsPageSize = size
      this.fetchRelatedGoods()
    },

    handleGoodsCurrentChange(page) {
      this.goodsCurrentPage = page
      this.fetchRelatedGoods()
    },

    viewGoodsDetail(goods) {
      this.$message({
        type: 'info',
        message: `查看商品详情: ${goods.goodsId}`
      })
    },

    downloadDocument() {
      this.$message({
        type: 'info',
        message: `正在下载文档 ID: ${this.currentDocument.documentId}`
      })

      // 这里应该添加实际的下载逻辑
      // 例如：window.open(`/api/documents/download/${this.currentDocument.documentId}`)
    },

    previewDocument() {
      this.$message({
        type: 'info',
        message: `正在预览文档 ID: ${this.currentDocument.documentId}`
      })

      // 这里应该添加实际的预览逻辑
      // 例如：打开一个包含PDF预览器的新窗口
    },

    handleGoods(index, row) {
      this.currentDutyHeadId = row.autoId
      this.showGoodsModal = true
    },

    handleEditDocument(document) {
      // this.editedItem = { ...document }
      this.documentForm = {
        documentId: document.documentId,
        expiry_date: document.expiryDate,
        document_fee: document.documentFee
      }
      this.showModal = true
    }
  }
}
</script>

<style scoped>
.test {
  margin: 20px;
}

.dialog-footer {
  text-align: right;
  margin-top: 15px;
}

.el-button {
  margin-left: 10px;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}

.form_container {
  margin: 30px;
}

.el-select {
  width: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form_add {
  display: flex;
  flex-direction: row;
}

.document-section {
  margin-top: 20px;
}

.document-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.goods-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section, .table-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-section h4, .table-section h4 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

@media (min-width: 992px) {
  .goods-container {
    flex-direction: row;
  }

  .form-section {
    flex: 1;
    margin-right: 20px;
  }

  .table-section {
    flex: 2;
  }
}
</style>
