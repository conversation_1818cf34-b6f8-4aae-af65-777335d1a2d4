<template>
  <el-dialog
    :visible.sync="visible"
    :title="dialogTitle"
    width="80%"
    @close="handleClose"
  >
    <div class="goods-container">
      <!-- 添加商品表单 -->
      <div class="form-section">
        <h4>添加商品</h4>
        <el-form ref="goodsForm" :model="goodsForm" :rules="rules" label-width="100px">
          <el-form-item label="职责ID" prop="dutyHeadId">
            <el-input v-model="goodsForm.dutyHeadId" :disabled="true" />
          </el-form-item>
          <el-form-item label="商品ID" prop="goodsId">
            <el-autocomplete
              v-model="goodsForm.goodsIdName"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入商品ID或名称搜索"
              style="width: 100%"
              @select="handleSelect"
            >
              <template slot-scope="{ item }">
                <div class="product-item">
                  <div class="product-id">{{ item.idname }}</div>
                  <div class="product-name">
                    <div>{{ item.mmName || '' }}</div>
                    <div>{{ item.dName || '' }}</div>
                  </div>
                </div>
              </template>
            </el-autocomplete>
          </el-form-item>
          <el-form-item label="数量" prop="qty">
            <el-input-number v-model="goodsForm.qty" :precision="2" :step="0.1" :min="0.1" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">添加商品</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 商品列表 -->
      <div class="table-section">
        <h4>商品列表</h4>
        <el-table
          v-loading="loading"
          :data="goodsList"
          style="width: 100%"
        >
          <el-table-column
            prop="autoId"
            label="ID"
            width="80"
          />
          <el-table-column
            prop="goodsId"
            label="商品ID"
            width="100"
          />
          <el-table-column
            label="商品名称"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.goodsIdInfo">
                <div style="color: #606266; font-size: 12px;">{{ scope.row.goodsIdInfo.mmName }}</div>
                <div style="color: #909399; font-size: 12px;">{{ scope.row.goodsIdInfo.dName }}</div>
              </div>
              <div v-else>{{ scope.row.goodsId }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="qty"
            label="数量"
            width="100"
          />
          <el-table-column
            prop="dutyDocumentId"
            label="文档ID"
            width="100"
          >
            <template slot-scope="scope">
              {{ scope.row.dutyDocumentId || '无' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="createDatetime"
            label="创建时间"
            width="180"
          >
            <template slot-scope="scope">
              {{ formatDate(scope.row.createDatetime) }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="180"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEdit(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 编辑商品对话框 -->
    <el-dialog
      width="70%"
      title="编辑商品"
      :visible.sync="editDialogVisible"
      append-to-body
    >
      <el-form ref="editForm" :model="editForm" :rules="rules" label-width="300px">
        <el-form-item label="职责ID" prop="dutyHeadId">
          <el-input v-model="editForm.dutyHeadId" :disabled="true" />
        </el-form-item>
        <el-form-item label="商品ID" prop="goodsIdName">
          <el-autocomplete
            v-model="editForm.goodsIdName"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入商品ID或名称搜索"
            style="width: 100%"
            @select="handleEditSelect"
          >
            <template slot-scope="{ item }">
              <div class="product-item">
                <div class="product-id">{{ item.idname }}</div>
                <div class="product-name">
                  <div>{{ item.mmName || '' }}</div>
                  <div>{{ item.dName || '' }}</div>
                </div>
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="数量" prop="qty">
          <el-input-number v-model="editForm.qty" :precision="2" :step="0.1" :min="0.1" />
        </el-form-item>

        <el-form-item label="template_duty_goods_info">
          <el-input v-model="editForm.templateDutyGoodsInfo" />
          <el-button type="primary" @click="updateTemplateDutyGoodsInfo">update</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateGoods">保存</el-button>
          <el-button @click="editDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { mapActions } from 'vuex'
import gql from 'graphql-tag'
import { companyClient } from '@/apolloClient'
import { UPDATE_DUTY_GOODS_INFO } from '@/graphql/mutations'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dutyHeadId: {
      type: [Number, String],
      required: false,
      default: 0
    },
    documentId: {
      type: [Number, String],
      default: null
    },
    titlePrefix: {
      type: String,
      default: '添加商品到职责'
    },
    mode: {
      type: String,
      default: 'goods' // 'goods' 或 'document'
    },
    dutyDocumentAutoId: {
      type: [Number, String],
      default: null
    }
  },

  data() {
    return {
      goodsForm: {
        dutyDocumentId: '',
        dutyHeadId: '',
        goodsId: '',
        qty: 1,
        dutyDocumentId: '',
        goodsIdName: ''
      },
      rules: {
        goodsIdName: [
          { required: true, message: '请输入商品ID', trigger: 'blur' }
        ],
        qty: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          { type: 'number', min: 0.1, message: '数量必须大于0', trigger: 'blur' }
        ]
      },
      editForm: {
        autoId: null,
        dutyHeadId: '',
        goodsId: '',
        qty: 1,
        dutyDocumentId: '',
        goodsIdName: ''
      },
      editDialogVisible: false,
      goodsList: [],
      documents: [],
      loading: false,
      documentsLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchTimeout: null,
      searchResults: [],
      searchLoading: false
    }
  },

  computed: {
    dialogTitle() {
      if (this.mode === 'document' && this.documentId) {
        return `${this.titlePrefix} #${this.documentId}`
      }
      return `${this.titlePrefix} #${this.dutyHeadId || ''}`
    },

    shouldUseDocumentId() {
      return this.mode === 'document' && this.documentId
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.fetchData()
        this.fetchDocuments()
        this.goodsForm.dutyHeadId = this.dutyHeadId
      }
    },
    dutyHeadId(val) {
      if (val) {
        this.goodsForm.dutyHeadId = val
        this.editForm.dutyHeadId = val
      }
    }
  },

  created() {
    if (this.visible) {
      this.fetchData()
      this.fetchDocuments()
      this.goodsForm.dutyHeadId = this.dutyHeadId
    }
  },

  methods: {
    ...mapActions('shwetheCompany', [
      'fetchDutyDocuments',
      'insertDutySub',
      'updateDutySub',
      'deleteDutySub'
    ]),

    fetchData() {
      this.loading = true

      if (this.shouldUseDocumentId) {
        this.$store.dispatch('shwetheCompany/fetchDutySubsByDocumentId', {
          dutyDocumentId: parseInt(this.$store.state.shwetheCompany.dutyDocumentAutoId)
        }).then(response => {
          if (response.success) {
            this.goodsList = response.data
            this.total = response.data.length

            // 如果获取到了商品数据，从第一个商品中获取 dutyHeadId
            if (this.goodsList && this.goodsList.length > 0) {
              this.goodsForm.dutyHeadId = this.goodsList[0].dutyHeadId
            }
          } else {
            this.$message({
              type: 'error',
              message: '获取商品列表失败!'
            })
          }
          this.loading = false
        }).catch(error => {
          console.error('获取商品列表出错:', error)
          this.loading = false
          this.$message({
            type: 'error',
            message: '获取商品列表失败!'
          })
        })
      } else if (this.dutyHeadId) {
        this.$store.dispatch('shwetheCompany/fetchDutySubsByHeadId', {
          dutyHeadId: parseInt(this.dutyHeadId)
        }).then(response => {
          if (response.success) {
            this.goodsList = response.data
            this.total = response.data.length
          } else {
            this.$message({
              type: 'error',
              message: '获取商品列表失败!'
            })
          }
          this.loading = false
        }).catch(error => {
          console.error('获取商品列表出错:', error)
          this.loading = false
          this.$message({
            type: 'error',
            message: '获取商品列表失败!'
          })
        })
      } else {
        this.loading = false
        this.goodsList = []
        this.total = 0
      }
    },

    fetchDocuments() {
      console.log(this.$store.state.shwetheCompany.dutyDocumentAutoId)
      console.log(this.dutyDocumentAutoId)
      this.documentsLoading = true

      // 调用获取文档列表的API
      this.fetchDutyDocuments(this.dutyHeadId)
        .then(documents => {
          this.documents = documents
          this.documentsLoading = false
        })
        .catch(error => {
          console.error('获取文档列表出错:', error)
          this.documentsLoading = false
          this.$message({
            type: 'error',
            message: '获取文档列表失败!'
          })
        })
    },

    resetForm() {
      this.$refs.goodsForm.resetFields()
      this.goodsForm = {
        dutyHeadId: this.dutyHeadId,
        goodsId: '',
        qty: 1,
        dutyDocumentId: '',
        goodsIdName: ''
      }
    },

    submitForm() {
      this.$refs.goodsForm.validate((valid) => {
        console.log(this.goodsForm)
        if (valid) {
          const payload = {
            dutyHeadId: parseInt(this.goodsForm.dutyHeadId || 0),
            goodsId: String(this.goodsForm.goodsId),
            qty: parseFloat(this.goodsForm.qty),
            dutyDocumentId: String(this.$store.state.shwetheCompany.dutyDocumentAutoId) || ''
          }

          // 调用添加商品的API
          this.insertDutySub(payload)
            .then(result => {
              if (result.success) {
                this.$message({
                  type: 'success',
                  message: '商品添加成功!'
                })
                this.resetForm()
                this.fetchData()
              } else {
                this.$message({
                  type: 'error',
                  message: '商品添加失败!'
                })
              }
            })
            .catch(error => {
              console.error('添加商品出错:', error)
              this.$message({
                type: 'error',
                message: '商品添加失败!'
              })
            })
        }
      })
    },

    handleEdit(row) {
      companyClient.query({
        query: gql`
          query getProductById($id: Int!) {
            statistics {
              getProductById(id: $id) {
                dName
                id
                idname
                mmName
                thName
              }
            }
          }
        `,
        variables: {
          id: parseInt(row.goodsId)
        }
      }).then(response => {
        const product = response.data.statistics.getProductById
        this.editForm = {
          autoId: row.autoId,
          dutyHeadId: row.dutyHeadId,
          goodsId: row.goodsId,
          goodsIdName: product ? product.idname : row.goodsId,
          qty: row.qty,
          dutyDocumentId: row.dutyDocumentId || ''
        }
        this.editDialogVisible = true
      }).catch(error => {
        console.error('获取商品详情出错:', error)
        // 如果获取失败，仍然显示编辑对话框，但不设置 goodsIdName
        this.editForm = {
          autoId: row.autoId,
          dutyHeadId: row.dutyHeadId,
          goodsId: row.goodsId,
          goodsIdName: row.goodsId,
          qty: row.qty,
          dutyDocumentId: row.dutyDocumentId || ''
        }
        this.editDialogVisible = true
      })
    },

    updateGoods() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          const payload = {
            autoId: this.editForm.autoId,
            dutyHeadId: parseInt(this.editForm.dutyHeadId),
            goodsId: String(this.editForm.goodsId),
            qty: parseFloat(this.editForm.qty),
            dutyDocumentId: this.editForm.dutyDocumentId || ''
          }

          // 调用更新商品的API
          this.updateDutySub(payload)
            .then(result => {
              if (result.success) {
                this.$message({
                  type: 'success',
                  message: '商品更新成功!'
                })
                this.editDialogVisible = false
                this.fetchData()
              } else {
                this.$message({
                  type: 'error',
                  message: '商品更新失败!'
                })
              }
            })
            .catch(error => {
              console.error('更新商品出错:', error)
              this.$message({
                type: 'error',
                message: '商品更新失败!'
              })
            })
        }
      })
    },

    handleDelete(row) {
      this.$confirm('确认删除此商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除商品的API
        this.deleteDutySub(row.autoId)
          .then(result => {
            if (result.success) {
              this.$message({
                type: 'success',
                message: '商品删除成功!'
              })
              this.fetchData()
            } else {
              this.$message({
                type: 'error',
                message: '商品删除失败!'
              })
            }
          })
          .catch(error => {
            console.error('删除商品出错:', error)
            this.$message({
              type: 'error',
              message: '商品删除失败!'
            })
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    handleSizeChange(size) {
      this.pageSize = size
      this.fetchData()
    },

    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchData()
    },

    handleClose() {
      this.$emit('update:visible', false)
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    async updateTemplateDutyGoodsInfo() {
      try {
        const response = await companyClient.mutate({
          mutation: UPDATE_DUTY_GOODS_INFO,
          variables: {
            autoId: this.editForm.autoId
          }
        })

        // 检查响应是否存在且包含预期的数据结构
        const result = response.data?.dutyHead?.updateDataSubForDutyGoodsInfo
        if (result !== undefined) {
          this.$message({
            type: 'success',
            message: '更新成功'
          })
          this.$emit('update:visible', false)
          this.fetchData() // 刷新数据
        } else {
          this.$message({
            type: 'error',
            message: '更新失败'
          })
        }
      } catch (error) {
        console.error('更新出错:', error)
        this.$message({
          type: 'error',
          message: '更新出错，请稍后重试'
        })
      }
    },

    querySearchAsync(queryString, callback) {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout)
      }

      if (queryString.length < 1) {
        callback([])
        return
      }

      this.searchLoading = true
      this.searchTimeout = setTimeout(() => {
        companyClient.query({
          query: gql`
            query searchProducts($keyword: String!, $limit: Int!, $skip: Int!) {
              statistics {
                searchProducts(keyword: $keyword, limit: $limit, skip: $skip) {
                  dName
                  id
                  idname
                  mmName
                  thName
                }
              }
            }
          `,
          variables: {
            keyword: queryString,
            limit: 10,
            skip: 0
          }
        }).then(response => {
          this.searchLoading = false
          const results = response.data.statistics.searchProducts || []
          callback(results)
        }).catch(error => {
          console.error('搜索商品出错:', error)
          this.searchLoading = false
          callback([])
          this.$message({
            type: 'error',
            message: '搜索商品失败!'
          })
        })
      }, 300)
    },

    handleSelect(item) {
      console.log(item)
      this.goodsForm.goodsId = String(item.id)

      console.log(this.goodsForm.goodsId)

      this.goodsForm.goodsIdName = item.idname
    },

    handleEditSelect(item) {
      this.editForm.goodsId = String(item.id)
      this.editForm.goodsIdName = item.idname
    }
  }
}
</script>

<style scoped>
.goods-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section, .table-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-section h4, .table-section h4 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

@media (min-width: 992px) {
  .goods-container {
    flex-direction: row;
  }

  .form-section {
    flex: 1;
    margin-right: 20px;
  }

  .table-section {
    flex: 2;
  }
}

.product-item {
  display: flex;
  padding: 10px 0;
  align-items: flex-start;
  width: 100%;
  border-bottom: 1px solid #eee;
}

.product-id {
  font-weight: bold;
  color: #409EFF;
  min-width: 120px;
  margin-right: 20px;
  white-space: nowrap;
}

.product-name {
  color: #606266;
  font-size: 12px;
  flex-grow: 1;
  text-align: left;
  word-break: break-word;
  line-height: 1.6;
  max-width: 400px;
}

/* 增加下拉菜单宽度 */
::v-deep .el-autocomplete-suggestion {
  min-width: 600px !important;
  max-width: 800px !important;
}

/* 调整下拉菜单项的样式 */
::v-deep .el-autocomplete-suggestion__list {
  padding: 10px;
}

/* 增加下拉菜单的最大高度 */
::v-deep .el-autocomplete-suggestion__wrap {
  max-height: 500px;
}

/* Additional UI improvements */
::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-button {
  padding: 10px 20px;
  border-radius: 4px;
}

::v-deep .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
}

::v-deep .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

::v-deep .el-table {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

::v-deep .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

::v-deep .el-input__inner,
::v-deep .el-autocomplete,
::v-deep .el-input-number {
  width: 100%;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}
</style>
