<template>
  <div class="test">
    <el-table
      :data="tableData.filter(data => !search || data.car_mmname.toLowerCase().includes(search.toLowerCase()) || data.product_mmname.toLowerCase().includes(search.toLowerCase()))"
      style="width: 100%"
    >

      <el-table-column label="Car ID" prop="car_id" />
      <el-table-column label="Car Name" prop="car_mmname" />
      <el-table-column label="Product Name" prop="product_mmname" />
      <el-table-column label="Product Quantity" prop="product_qty" />

      <el-table-column align="right">
        <template slot="header" slot-scope="scope">
          <el-input
            v-model="search"
            size="mini"
            placeholder="Type to search"
          />
        </template>
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">Edit</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">Delete</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="showModal"
      title="Edit Item"
      width="50%"
    >
      <el-form ref="editForm" :model="editedItem">
        <el-form-item label="Car ID" prop="car_id">
          <el-input v-model="editedItem.car_id" />
        </el-form-item>
        <el-form-item label="Car Name" prop="car_mmname">
          <el-input v-model="editedItem.car_mmname" />
        </el-form-item>
        <el-form-item label="Product Name" prop="product_mmname">
          <el-input v-model="editedItem.product_mmname" />
        </el-form-item>
        <el-form-item label="Product Quantity" prop="product_qty">
          <el-input v-model="editedItem.product_qty" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showModal = false">Cancel</el-button>
        <el-button type="primary" @click="saveEdit">Save</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      search: '',
      showModal: false,
      editedItem: {}
    }
  },

  mounted() {
    fetch('http://localhost:3000/juty')
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        return response.json()
      })
      .then((data) => {
        this.tableData = data
        console.log(tableData)
      })
      .catch((error) => {
        console.error('There was a problem with the fetch operation:', error)
      })
  },

  methods: {
    handleEdit(index, row) {
      this.editedItem = { ...row }
      this.showModal = true
    },

    handleDelete(index, row) {
      this.tableData = this.tableData.filter(item => item.id !== row.id)
    },

    saveEdit() {
      const index = this.tableData.findIndex(item => item.id === this.editedItem.id)
      if (index !== -1) {
        this.$set(this.tableData, index, { ...this.editedItem })
      }
      this.showModal = false
    }
  }
}
</script>

<style scoped>
.test {
  margin: 20px;
}

.dialog-footer {
  text-align: right;
}

.el-button {
  margin-left: 10px;
}
</style>
