<template>
  <div class="container">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      status-icon
      :rules="rules"
      label-width="120px"
      class="demo-ruleForm"
    >
      <el-form-item label="公司" prop="status">
        <el-select v-model="ruleForm.status" placeholder="请选择公司">
          <el-option label="company1" value="1" />
          <el-option label="company2" value="2" />
          <el-option label="company3" value="3" />
          <el-option label="company4" value="4" />
          <el-option label="company5" value="5" />
          <el-option label="SHWE EAIN" value="19" />
          <el-option label="ANANDA SHWE OE MINING " value="20" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    var checkStatus = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择公司'))
      }
      callback()
    }

    return {
      ruleForm: {
        status: ''
      },
      rules: {
        status: [{ validator: checkStatus, trigger: 'change' }]
      }
    }
  },
  methods: {
    fetchData() {
      console.log('调用 fetchData 方法')
      this.$store.dispatch('shwetheCompany/fetchDutyHeads', {
        page: 1,
        pageSize: 10,
        forceRefresh: true
      }).then(() => {
        console.log('数据获取完成')
      }).catch(error => {
        console.error('获取数据时出错:', error)
      })
    },
    submitForm(formName) {
      console.log(this.ruleForm)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$store.dispatch('shwetheCompany/createDutyHead', this.ruleForm.status)
            .then(result => {
              if (result.success) {
                const autoId = result.data.autoId
                const yearRange = result.data.yearRange
                this.$message({
                  type: 'success',
                  message: `表单提交成功！ID: ${autoId}, 年度: ${yearRange}`
                })

                // 手动触发刷新，强制回到第一页并刷新
                this.fetchData()

                this.$emit('closeModal')
                this.$emit('refresh-table')
              } else {
                this.$message({
                  type: 'error',
                  message: '提交失败!'
                })
              }
            })
        } else {
          console.log('表单验证失败！')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 50px auto;
  padding: 30px;
  background: #fff;
  border-radius: 12px;
}

.demo-ruleForm {
  padding-top: 20px;
  display: flex;
  flex-direction: column;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item label {
  font-weight: bold;
  color: #555;
}

.el-input,
.el-select {
  border-radius: 8px;
  border: none;
  transition: all 0.3s ease-in-out;
}

.el-input:hover,
.el-input:focus,
.el-select:hover,
.el-select:focus {
  border-color: #007bff;
  box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.3);
}

.el-button {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  transition: background 0.3s ease-in-out;
}

.el-button--primary {
  border: none;
}

.el-select {
  width: 100%;
}

.reset-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ccc;
}

.reset-btn:hover {
  background-color: #ddd;
}
</style>
