<template>
  <div>
    <el-button type="text" @click="open">+ Add Item</el-button>
  </div>
</template>

<script>
export default {
  methods: {
    open() {
      // Open prompt for name
      this.$prompt('Please input your name', 'Name Input', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel'
      }).then(({ value: name }) => {
        // Open prompt for qty
        this.$prompt('Please input the quantity', 'Quantity Input', {
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          inputPattern: /^[1-9]\d*$/,
          inputErrorMessage: 'Quantity must be a positive integer'
        }).then(({ value: qty }) => {
          // Open prompt for price
          this.$prompt('Please input the price', 'Price Input', {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            inputPattern: /^[0-9]+(\.[0-9]{1,2})?$/,
            inputErrorMessage: 'Invalid Price Format'
          }).then(({ value: price }) => {
            // Open select dropdown for category
            this.$prompt('Please select a category', 'Select Category', {
              confirmButtonText: 'OK',
              cancelButtonText: 'Cancel',
              inputType: 'select',
              inputValue: '',
              inputOptions: [
                { label: 'Option 1', value: 'option1' },
                { label: 'Option 2', value: 'option2' },
                { label: 'Option 3', value: 'option3' }
              ]
            }).then(({ value: category }) => {
              this.$message({
                type: 'success',
                message: `Item added successfully! Name: ${name}, Qty: ${qty}, Price: ${price}, Category: ${category}`
              })
            }).catch(() => {
              this.$message({
                type: 'info',
                message: 'Category selection canceled'
              })
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: 'Price input canceled'
            })
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: 'Quantity input canceled'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: 'Name input canceled'
        })
      })
    }
  }
}
</script>

  <style scoped>
    /* You can style your button or components here */
  </style>
