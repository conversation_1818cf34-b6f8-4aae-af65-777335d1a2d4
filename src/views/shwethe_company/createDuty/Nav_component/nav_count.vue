<template>
  <div class="nav_container">
    <el-card class="box-card">
      <div v-for="o in 1" :key="o" class="text item">
        {{ 'List item ' + o }}
      </div>
    </el-card>
    <el-card class="box-card">
      <div v-for="o in 1" :key="o" class="text item">
        {{ 'List item ' + o }}
      </div>
    </el-card>
    <el-card class="box-card">
      <div v-for="o in 1" :key="o" class="text item">
        {{ 'List item ' + o }}
      </div>
    </el-card>
    <el-card class="box-card">
      <div v-for="o in 1" :key="o" class="text item">
        {{ 'List item ' + o }}
      </div>
    </el-card>
    <el-card class="box-card">
      <div v-for="o in 1" :key="o" class="text item">
        {{ 'List item ' + o }}
      </div>
    </el-card>
    <el-card class="box-card">
      <div v-for="o in 1" :key="o" class="text item">
        {{ 'List item ' + o }}
      </div>
    </el-card>
  </div>
</template>

  <style>
    .text {
      font-size: 14px;
    }

    .item {
      padding: 0px 0;
    }

    .box-card {
      width: 250px;
      margin: 0px 0px 0px 15px;
    }

    .nav_container {
      display: flex;

    }
    @media (max-width: 1200px) {
    .box-card {
      width: 45%;
    }
  }

  @media (max-width: 768px) {
    .box-card {
      width: 100%;
    }
  }
  </style>
