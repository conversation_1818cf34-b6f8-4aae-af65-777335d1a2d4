<template>
  <div>
    <div class="header_con">
      <div class="text_con">
        <h3 class="text">View All</h3>
        <p class="text">Manage add new and access.</p>
      </div>
      <el-button class="add_item" type="success" @click="showModal = true">+ Add Item</el-button>
    </div>
    <div class="header_con">
      <NavCount />
      <!-- <div>
        <el-button class="add_item" @click="showModal = true" type="success">+ Add Item</el-button>
      </div> -->
    </div>

    <div v-if="showModal" class="modal-overlay" @click="showModal = false">
      <div class="modal-content" @click.stop>
        <Form :show-modal="showModal" @closeModal="closeModal" />
      </div>
    </div>

    <Table />
    <ViewAll />
    <UserView />
  </div>
</template>

<script>
import Table from './components/table.vue'
import Form from './components/from.vue'
import ViewAll from './components/view_all.vue'
import UserView from './components/user_view_table.vue'
import NavCount from './Nav_component/nav_count.vue'

export default {
  name: 'WlDataUserd',
  components: {
    Table, Form, ViewAll, UserView, NavCount
  },
  data() {
    return {
      showModal: false,
      newItem: {
        name: '',
        description: ''
      }
    }
  },
  methods: {
    closeModal() {
      this.showModal = false
    },
    addItem() {
      console.log('Item added:', this.newItem)

      this.newItem.name = ''
      this.newItem.description = ''
      this.showModal = false
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  width: 800px;
  /* box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); */
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  max-width: 800px;
  margin: 50px auto;
  padding: 30px;
  background: #fff;
  border-radius: 12px;
  /* box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); */
}

.demo-ruleForm {
  padding-top: 20px;
  display: flex;
  flex-direction: column;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item label {
  font-weight: bold;
  color: #555;
}

.el-input,
.el-select {
  border-radius: 8px;
  border: 1px solid #ccc;
  padding: 10px;
  transition: all 0.3s ease-in-out;
}

.el-input:hover,
.el-input:focus,
.el-select:hover,
.el-select:focus {
  border-color: #007bff;
  box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.3);
}

.el-button {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  transition: background 0.3s ease-in-out;
}

.el-button--primary {
  background-color: #007bff;
  border: none;
  color: white;
}

.el-button--primary:hover {
  background-color: #0056b3;
}

.reset-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ccc;
}

.reset-btn:hover {
  background-color: #ddd;
}

.el-table {
  width: 100%;
}

.el-table-column {
  padding: 10px;
}

.el-button {
  margin: 0 5px;
}

.el-input {
  width: 100%;
}

.el-input[readonly] {
  background-color: #f7f7f7;
}

.el-button--mini {
  padding: 5px 10px;
}

.el-table th {
  font-weight: bold;
}

.el-table__body {
  max-height: 400px;
  overflow-y: auto;
}

.add_item {
  cursor: pointer;
  width: 120px;
  margin: 10px;
}
.header_con {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  white-space: nowrap;
}
.text_con {
  margin-left: 20px;
}
.text {
  margin: 0;
  padding: 0;
}
h3 {
  font-size: 20px;
}
p{
  color: grey;
}
</style>

