<template>
  <div class="app-container">
    <TableComponent
      :data="tableData"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
      @view-document="handleViewDocument"
      @update-item="handleUpdateItem"
    />
  </div>
</template>

<script>
import TableComponent from './components/table.vue'
import { companyClient } from '@/apolloClient'
import { GET_DUTY_HEAD_ALL } from '@/graphql/queries'

export default {
  name: 'DutyDaily',
  components: { TableComponent },
  data() {
    return {
      tableData: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        const response = await companyClient.query({
          query: GET_DUTY_HEAD_ALL,
          fetchPolicy: 'network-only',
          variables: {
            page: this.currentPage,
            pageSize: this.pageSize
          }
        })

        const result = response.data.statistics.getDutyHeadAll
        this.tableData = result.items.map(item => ({
          ...item,
          companyName: this.getCompanyName(item.companyId),
          documentCount: item.dutyDocuments.length,
          totalQty: item.dutyDocuments.reduce((sum, doc) =>
            sum + doc.dutySub.reduce((subSum, sub) => subSum + sub.qty, 0), 0),
          totalValue: item.dutyDocuments.reduce((sum, doc) =>
            sum + doc.dutySub.reduce((subSum, sub) => {
              // 确保从dutyGoodsInfo中获取av值
              const av = sub.dutyGoodsInfo ? sub.dutyGoodsInfo.av || 0 : 0
              // 使用商品数量、av值和头部的税率计算
              return subSum + (sub.qty * av * (item.rate || 1) / 100)
            }, 0), 0)
        }))
        this.total = result.pageInfo.totalItems
      } catch (error) {
        this.$message({
          type: 'error',
          message: '获取数据失败，请稍后重试'
        })
      } finally {
        this.loading = false
      }
    },
    getCompanyName(companyId) {
      const companies = {
        '1': 'Company 1',
        '2': 'Company 2',
        '3': 'Company 3',
        '4': 'Company 4',
        '5': 'Company 5',
        '19': 'SHWE EAIN'
      }
      return companies[companyId] || 'Unknown Company'
    },
    handleEdit(row) {
      this.$refs.tableComponent.handleEdit(row)
    },
    handleDelete(row) {
      // 处理删除逻辑
      console.log('Delete:', row)
    },
    handleViewDocument(document) {
      console.log('View document:', document)
      // 这里可以添加查看文档详情的逻辑
    },
    handleUpdateItem(updatedItem) {
      // 这里应该调用API来更新数据
      const index = this.tableData.findIndex(item => item.autoId === updatedItem.autoId)
      if (index !== -1) {
        this.tableData.splice(index, 1, updatedItem)
      }
    }
  }
}
</script>
