<template>
  <div>
    <el-table
      v-loading="loading"
      :data="data"
      border
      style="width: 100%; min-width: 1600px"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="autoId" label="ID" width="100" />
      <el-table-column prop="companyName" label="公司名称" width="200" />
      <el-table-column prop="yearRange" label="年度范围" width="150" />
      <el-table-column label="创建时间" width="200">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createDatetime) }}
        </template>
      </el-table-column>
      <el-table-column prop="rate" label="税率" width="120" align="right">
        <template slot-scope="scope">
          {{ scope.row.rate ? scope.row.rate.toFixed(2) + '%' : 'N/A' }}
        </template>
      </el-table-column>
      <el-table-column prop="ratePo" label="PO税率" width="120" align="right">
        <template slot-scope="scope">
          {{ scope.row.ratePo ? scope.row.ratePo.toFixed(4) + '%' : 'N/A' }}
        </template>
      </el-table-column>
      <el-table-column prop="documentCount" label="文档数量" width="120" />
      <el-table-column prop="totalQty" label="总数量" width="120" />

      <el-table-column type="expand">
        <template slot-scope="props">
          <el-table
            :data="props.row.dutyDocuments"
            border
            style="width: 100%; min-width: 1400px"
          >
            <el-table-column prop="documentId" label="文档ID" width="150" />
            <el-table-column label="过期日期" width="180">
              <template slot-scope="scope">
                {{ formatDate(scope.row.expiryDate) }}
              </template>
            </el-table-column>
            <el-table-column label="文档费用" width="120" align="right">
              <template slot-scope="scope">
                {{ scope.row.documentFee ? scope.row.documentFee.toFixed(2) : 'N/A' }}
              </template>
            </el-table-column>
            <el-table-column type="expand">
              <template slot-scope="scope">
                <el-table
                  :data="scope.row.dutySub"
                  border
                  style="width: 100%; min-width: 1200px"
                >
                  <el-table-column prop="goodsId" label="商品ID" width="150" />
                  <el-table-column prop="qty" label="数量" width="120" />
                  <el-table-column label="创建时间" width="180">
                    <template slot-scope="scope">
                      {{ formatDate(scope.row.createDatetime) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="商品信息" width="250">
                    <template slot-scope="scope">
                      <div v-if="scope.row.goodsIdInfo">
                        {{ scope.row.goodsIdInfo.dName }} ({{ scope.row.goodsIdInfo.idname }})
                      </div>
                      <div v-else>
                        N/A
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="商品详情" width="400">
                    <template slot-scope="scope">
                      <div v-if="scope.row.dutyGoodsInfo">
                        <div>AV: {{ scope.row.dutyGoodsInfo.av }}</div>
                        <div>Bank: {{ scope.row.dutyGoodsInfo.bank }}</div>
                        <div>Commercial: {{ scope.row.dutyGoodsInfo.commercial }}</div>
                        <div>Custom: {{ scope.row.dutyGoodsInfo.custom }}</div>
                        <div>Income: {{ scope.row.dutyGoodsInfo.income }}</div>
                        <div>PO: {{ scope.row.dutyGoodsInfo.po }}</div>
                      </div>
                      <div v-else>
                        N/A
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column label="子项数量" width="120">
              <template slot-scope="scope">
                {{ scope.row.dutySub.length }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="$emit('view-document', scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="$emit('edit', scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="$emit('delete', scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑对话框 -->
    <el-dialog
      :visible.sync="editDialogVisible"
      :title="`编辑职责 #${currentItem.autoId}`"
      width="50%"
    >
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="公司名称">
          <el-input v-model="editForm.companyName" disabled />
        </el-form-item>
        <el-form-item label="年度范围">
          <el-input v-model="editForm.yearRange" />
        </el-form-item>
        <el-form-item label="税率">
          <el-input-number v-model="editForm.rate" :precision="2" :step="0.01" />
        </el-form-item>
        <el-form-item label="PO税率">
          <el-input-number v-model="editForm.ratePo" :precision="4" :step="0.0001" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TableComponent',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editDialogVisible: false,
      currentItem: {},
      editForm: {
        companyName: '',
        yearRange: '',
        rate: 0,
        ratePo: 0
      }
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return 'N/A'
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }
      return new Date(date).toLocaleString('en-US', options)
    },
    tableRowClassName({ row }) {
      return row.dutyDocuments.length > 0 ? 'has-documents' : ''
    },
    handleEdit(row) {
      this.currentItem = { ...row }
      this.editForm = {
        companyName: row.companyName,
        yearRange: row.yearRange,
        rate: row.rate,
        ratePo: row.ratePo
      }
      this.editDialogVisible = true
    },
    handleEditSubmit() {
      // 这里应该调用API来保存编辑后的数据
      this.$emit('update-item', {
        ...this.currentItem,
        ...this.editForm
      })
      this.editDialogVisible = false
      this.$message({
        type: 'success',
        message: '更新成功'
      })
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

::v-deep .el-table__expanded-cell {
  padding: 20px 50px;
}

::v-deep .has-documents .el-table__expand-icon {
  color: #409EFF;
}

::v-deep .el-table__expanded-cell .el-table__expanded-cell {
  padding: 10px 30px;
}

::v-deep .el-table__body-wrapper {
  overflow-x: auto;
}

::v-deep .el-table__row {
  height: 60px;
}

::v-deep .el-table__expanded-cell .el-table {
  margin: 10px 0;
}
</style>
