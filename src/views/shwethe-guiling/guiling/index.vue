<template>
  <div class="app-container">
    <!-- Tabs for Form Insert -->
    <el-tabs v-model="activeTab" type="card">

      <!-- Form Insert Tab -->
      <el-tab-pane label="Form Insert" name="insert">
        <div class="flexible-grid">
          <el-card class="grid-card" shadow="hover">
            <div class="grid-card-header">
              <div class="record-id">Form Insert</div>
            </div>
            <div class="grid-card-content">
              <el-form ref="form" :model="form" :rules="rules">
                <div class="grid-section">
                  <div class="grid-row">
                    <!-- <div class="grid-cell">
                  <div class="cell-label">Shu Riqi Datetime</div>
                  <div class="cell-value">
                    <el-date-picker v-model="form.shu_riqi_datetime" type="datetime" placeholder="Select date" size="mini" style="width:100%;" value-format="yyyy-MM-dd HH:mm:ss" />
                  </div>
                </div> -->
                    <div class="grid-cell">
                      <div class="cell-label">Bu Bian</div>
                      <div class="cell-value">
                        <el-form-item prop="bu_bian">
                          <el-input v-model.number="form.bu_bian" placeholder="Bu Bian" size="mini" />
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Riqi Datetime</div>
                      <div class="cell-value">
                        <el-date-picker v-model="form.riqi_datetime" type="datetime" placeholder="Select date" size="mini" style="width:100%;" value-format="yyyy-MM-dd HH:mm:ss" />
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Bi Zhi</div>
                      <div class="cell-value">
                        <el-form-item prop="bi_zhi">
                          <el-select v-model="form.bi_zhi" placeholder="Bi Zhi" clearable filterable size="mini" style="width:100%;" @change="onBiZhiChange">
                            <el-option v-for="opt in biZhiOptions" :key="opt.id" :label="opt.name" :value="opt.id" />
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Ke Bian</div>
                      <div class="cell-value">
                        <el-form-item>
                          <el-input
                            v-model="searchTextK"
                            placeholder="Type to search Ke Bian"
                            size="mini"
                            style="width:100%;"
                            @keyup.enter.native="searchKeBian"
                            @input="onKeBianSearchChange"
                          >
                            <i v-if="searchKeBianLoading" slot="suffix" class="el-icon-loading" />
                          </el-input>
                          <div v-if="selectedKeBian" style="margin-top:4px; font-size:12px; color:#409EFF;">
                            Selected: {{ formatSelectedItem(selectedKeBian) }}
                          </div>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Che Liang</div>
                      <div class="cell-value">
                        <el-input
                          v-model="searchTextC"
                          placeholder="Type to search Che Liang"
                          size="mini"
                          style="width:100%;"
                          @keyup.enter.native="searchCheLiang"
                          @input="onCheLiangSearchChange"
                        >
                          <i v-if="searchCheLiangLoading" slot="suffix" class="el-icon-loading" />
                        </el-input>
                        <div v-if="selectedCheLiang" style="margin-top:4px; font-size:12px; color:#409EFF;">
                          Selected: {{ formatSelectedItem(selectedCheLiang) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="grid-section">
                  <div class="grid-row">
                    <div class="grid-cell">
                      <div class="cell-label">Jia Yi Fang A</div>
                      <div class="cell-value">
                        <el-form-item prop="jia_yi_fang_a">
                          <el-input
                            v-model="searchTextA"
                            placeholder="Type to search Jia Yi A"
                            size="mini"
                            style="width:100%;"
                            @keyup.enter.native="searchJiaYi('A')"
                            @input="onJiaYiASearchChange"
                          >
                            <i v-if="searchJiaYiALoading" slot="suffix" class="el-icon-loading" />
                          </el-input>
                          <div v-if="selectedJiaYiA" style="margin-top:4px; font-size:12px; color:#409EFF;">
                            Selected: {{ formatSelectedItem(selectedJiaYiA) }}
                          </div>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Lei A</div>
                      <div class="cell-value">
                        <el-form-item prop="lei_a">
                          <el-select v-model="form.lei_a" placeholder="Lei A" clearable filterable size="mini" style="width:100%;" @change="onLeiAChange">
                            <el-option v-for="item in leiOptions" :key="item.id" :label="item.name" :value="item.id" />
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Product</div>
                      <div class="cell-value">
                        <el-form-item prop="product_id">
                          <el-input
                            v-model="searchText"
                            placeholder="Type to search products"
                            size="mini"
                            style="width:100%;"
                            :loading="searchProductLoading"
                            @keyup.enter.native="searchProduct"
                            @input="onProductSearchChange"
                          >
                            <i v-if="searchProductLoading" slot="suffix" class="el-icon-loading" />
                          </el-input>
                          <div v-if="selectedProduct" style="margin-top:4px; font-size:12px; color:#409EFF;">
                            Selected: {{ formatSelectedProduct(selectedProduct) }}
                          </div>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Product Price A</div>
                      <div class="cell-value">
                        <el-form-item prop="product_price_a">
                          <el-input v-model.number="form.product_price_a" placeholder="Price A" size="mini" @input="onProductPriceAChange" />
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="grid-section">
                  <div class="grid-row">
                    <div class="grid-cell">
                      <div class="cell-label">Jia Yi Fang B</div>
                      <div class="cell-value">
                        <el-form-item prop="jia_yi_fang_b">
                          <el-input
                            v-model="searchTextB"
                            placeholder="Type to search Jia Yi B"
                            size="mini"
                            style="width:100%;"
                            @keyup.enter.native="searchJiaYi('B')"
                            @input="onJiaYiBSearchChange"
                          >
                            <i v-if="searchJiaYiBLoading" slot="suffix" class="el-icon-loading" />
                          </el-input>
                          <div v-if="selectedJiaYiB" style="margin-top:4px; font-size:12px; color:#409EFF;">
                            Selected: {{ formatSelectedItem(selectedJiaYiB) }}
                          </div>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Lei B</div>
                      <div class="cell-value">
                        <el-form-item prop="lei_b">
                          <el-select v-model="form.lei_b" placeholder="Lei B" clearable filterable size="mini" style="width:100%;" @change="onLeiBChange">
                            <el-option v-for="item in leiOptions" :key="item.id" :label="item.name" :value="item.id" />
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Product Qty</div>
                      <div class="cell-value">
                        <el-form-item prop="product_qty">
                          <el-input v-model.number="form.product_qty" placeholder="Qty" size="mini" />
                        </el-form-item>
                      </div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Product Price B</div>
                      <div class="cell-value">
                        <el-form-item prop="product_price_b">
                          <el-input v-model.number="form.product_price_b" placeholder="Price B" size="mini" disabled @input="onProductPriceBChange" />
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="grid-card-footer">
                  <el-button type="primary" size="mini" @click="submitForm">Submit</el-button>
                  <el-button size="mini" @click="resetForm">Reset</el-button>
                </div>
              </el-form>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

    </el-tabs>
  </div>
</template>

<script>
import request from '@/utils/request'

function decodeToken(token) {
  try {
    const base64 = token.split('.')[1].replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    }).join(''))
    return JSON.parse(jsonPayload)
  } catch (e) {
    return {}
  }
}

export default {
  name: 'Guiling',
  data() {
    return {
      form: {
        product_id: null,
        product_qty: null,
        product_price_a: null,
        product_price_b: null,
        ke_bian: '',
        jia_yi_fang_a: '',
        jia_yi_fang_b: '',
        lei_a: null,
        lei_b: null,
        bu_bian: null,
        shu_riqi_datetime: '',
        riqi_datetime: '',
        che_liang: '',
        bi_zhi: '',
        details_json: {}
      },
      rules: {
        product_id: [
          { required: true, message: 'Product ID is required', trigger: 'change' }
        ],
        product_qty: [
          { type: 'number', required: true, message: 'Product quantity is required', trigger: 'blur' }
        ],
        product_price_a: [
          { type: 'number', required: true, message: 'Product price A is required', trigger: 'blur' }
        ],
        product_price_b: [
          { type: 'number', required: true, message: 'Product price B is required', trigger: 'blur' }
        ],

        jia_yi_fang_a: [
          { required: true, message: 'Jia Yi Fang A is required', trigger: 'blur' }
        ],
        jia_yi_fang_b: [
          { required: true, message: 'Jia Yi Fang B is required', trigger: 'blur' }
        ],
        lei_a: [
          { required: true, message: 'Lei A is required', trigger: 'change' },
          { type: 'number', message: 'Lei A must be a number', trigger: 'change' }
        ],
        lei_b: [
          { required: true, message: 'Lei B is required', trigger: 'change' },
          { type: 'number', message: 'Lei B must be a number', trigger: 'change' }
        ],
        bu_bian: [
          { type: 'number', required: true, message: 'Bu Bian is required', trigger: 'blur' }
        ],
        shu_riqi_datetime: [
          { required: true, message: 'Shu Riqi Datetime is required', trigger: 'change' }
        ],
        riqi_datetime: [
          { required: true, message: 'Riqi Datetime is required', trigger: 'change' }
        ],

        bi_zhi: [
          { required: true, message: 'Bi Zhi is required', trigger: 'change' }
        ]
      },
      searchText: '',
      products: [],
      searchTextA: '',
      searchTextB: '',
      jiaYiResultsA: [],
      jiaYiResultsB: [],
      jiaYiColumnKeysA: [],
      jiaYiColumnKeysB: [],
      searchTextK: '',
      keBianResults: [],
      keBianColumnKeys: [],
      searchTextC: '',
      cheLiangResults: [],
      cheLiangColumnKeys: [],
      leiOptions: [],
      biZhiOptions: [],
      selectedProduct: null,
      selectedKeBian: null,
      selectedCheLiang: null,
      selectedJiaYiA: null,
      selectedJiaYiB: null,
      searchLoading: false,
      searchProductLoading: false,
      searchKeBianLoading: false,
      searchCheLiangLoading: false,
      searchJiaYiALoading: false,
      searchJiaYiBLoading: false,
      activeTab: 'insert'
    }
  },
  created() {
    // initialize riqi_datetime and shu_riqi_datetime to current datetime
    this.form.riqi_datetime = this.getNowDateTimeString()
    this.form.shu_riqi_datetime = this.getNowDateTimeString()
    // fetch lei and bi zhi options
    this.fetchLeiOptions()
    this.fetchBiZhiOptions()
  },
  methods: {
    fetchLeiOptions() {
      request({
        url: '/api/v1/code',
        method: 'get',
        params: { type_item: 'lei' }
      }, 'shwethe_guiling').then(res => {
        this.leiOptions = Array.isArray(res) ? res : (res.data || [])
      }).catch(err => {
        console.error('Failed to load Lei options:', err)
        this.$message.error('Failed to load Lei options')
      })
    },
    fetchBiZhiOptions() {
      request({
        url: '/api/v1/code',
        method: 'get',
        params: { type_item: 'currency' }
      }, 'shwethe_guiling').then(res => {
        this.biZhiOptions = Array.isArray(res) ? res : (res.data || [])
      }).catch(err => {
        console.error('Failed to load Bi Zhi options:', err)
        this.$message.error('Failed to load Bi Zhi options')
      })
    },
    searchProduct() {
      if (!this.searchText) {
        this.$message.warning('Enter text to search')
        return
      }
      this.searchProductLoading = true
      request({
        url: '/api/v1/product_info',
        method: 'get',
        params: { text: this.searchText }
      }, 'shwethe_guiling').then(res => {
        this.products = Array.isArray(res) ? res : (res.data || [])
        if (this.products.length === 0) {
          this.$message.info('No products found')
        } else if (this.products.length === 1) {
          // Auto-select if only one result
          this.selectProduct(this.products[0])
        } else {
          // Show first result as selected for now, user can search again for different result
          this.selectProduct(this.products[0])
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.searchProductLoading = false
      })
    },
    selectProduct(row) {
      this.selectedProduct = row
      this.form.product_id = Number(row.ProductId) || 0
      this.form.details_json = {
        ...this.form.details_json,
        product: row
      }
      this.$nextTick(() => {
        this.$refs.form.validateField('product_id')
      })
      this.$message.success(`Selected ${row.ProductIdname}`)
      this.products = []
    },
    getIdFromRow(row) {
      for (const key in row) {
        if (/id$/i.test(key) || /Id/i.test(key)) return row[key]
      }
      return Object.values(row)[0]
    },
    searchJiaYi(type) {
      const text = type === 'A' ? this.searchTextA : this.searchTextB
      if (!text) {
        this.$message.warning('Enter text to search')
        return
      }
      if (type === 'A') {
        this.searchJiaYiALoading = true
      } else {
        this.searchJiaYiBLoading = true
      }
      request({
        url: '/api/v1/jia_yi_info',
        method: 'get',
        params: { q: text }
      }, 'shwethe_guiling').then(res => {
        const list = Array.isArray(res) ? res : (res.data || [])
        if (type === 'A') {
          this.jiaYiResultsA = list
          this.jiaYiColumnKeysA = list.length ? Object.keys(list[0]) : []
        } else {
          this.jiaYiResultsB = list
          this.jiaYiColumnKeysB = list.length ? Object.keys(list[0]) : []
        }
        if (list.length === 0) {
          this.$message.info('No results found')
        } else if (list.length === 1) {
          // Auto-select if only one result
          this.selectJiaYi(type, list[0])
        } else {
          // Show first result as selected for now
          this.selectJiaYi(type, list[0])
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        if (type === 'A') {
          this.searchJiaYiALoading = false
        } else {
          this.searchJiaYiBLoading = false
        }
      })
    },
    selectJiaYi(type, row) {
      const id = Number(this.getIdFromRow(row)) || 0
      if (type === 'A') {
        this.selectedJiaYiA = row
        this.form.jia_yi_fang_a = id
        this.form.details_json = {
          ...this.form.details_json,
          jia_yi_fang_a: row
        }
        this.jiaYiResultsA = []
      } else {
        this.selectedJiaYiB = row
        this.form.jia_yi_fang_b = id
        this.form.details_json = {
          ...this.form.details_json,
          jia_yi_fang_b: row
        }
        this.jiaYiResultsB = []
      }
      this.$nextTick(() => {
        this.$refs.form.validateField(type === 'A' ? 'jia_yi_fang_a' : 'jia_yi_fang_b')
      })
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const token = localStorage.getItem('auth-token') || ''
          const payload = decodeToken(token)
          const uId = payload.user_id || payload.sub || 0

          const postData = {
            ...this.form,
            type_edit: 'insert',
            u_id: uId
          }

          // ensure numeric fields are numbers
          const numericKeys = ['product_id', 'product_qty', 'product_price_a', 'product_price_b', 'ke_bian', 'jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'lei_b', 'bu_bian', 'bi_zhi', 'che_liang', 'u_id']
          numericKeys.forEach(k => {
            postData[k] = Number(postData[k]) || 0
          })

          console.log('POST payload', postData)

          request({
            url: '/api/v1/form_insert',
            method: 'post',
            data: postData,
            headers: {
              accept: 'application/json',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`
            }
          }, 'shwethe_guiling').then(() => {
            this.$message.success('Submitted successfully!')
            console.log('API success')
            // Clear form to default state after successful submission
            this.clearFormToDefault()
          }).catch(err => {
            console.error(err)
            if (err.response) {
              console.error('Server response', err.response.data)
              this.$message.error(err.response.data.message || 'Submit failed')
            } else {
              this.$message.error('Submit failed')
            }
          })
        } else {
          this.$message.error('Please fix validation errors.')
        }
      })
    },
    resetForm() {
      this.$refs.form.resetFields()
    },
    clearFormToDefault() {
      // Reset form to initial default state
      this.form = {
        product_id: null,
        product_qty: null,
        product_price_a: null,
        product_price_b: null,
        ke_bian: '',
        jia_yi_fang_a: '',
        jia_yi_fang_b: '',
        lei_a: null,
        lei_b: null,
        bu_bian: null,
        shu_riqi_datetime: this.getNowDateTimeString(),
        riqi_datetime: this.getNowDateTimeString(),
        che_liang: '',
        bi_zhi: '',
        details_json: {}
      }

      // Clear all search-related data
      this.searchText = ''
      this.products = []
      this.selectedProduct = null
      this.searchKeBianText = ''
      this.selectedKeBian = null
      this.searchCheLiangText = ''
      this.selectedCheLiang = null
      this.searchJiaYiAText = ''
      this.selectedJiaYiA = null
      this.searchJiaYiBText = ''
      this.selectedJiaYiB = null

      // Reset form validation
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    getNowDateTimeString() {
      const d = new Date()
      const pad = n => (n < 10 ? '0' + n : n)
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
    },
    searchKeBian() {
      if (!this.searchTextK) {
        this.$message.warning('Enter text to search')
        return
      }
      this.searchKeBianLoading = true
      request({
        url: '/api/v1/jia_yi_info',
        method: 'get',
        params: { q: this.searchTextK }
      }, 'shwethe_guiling').then(res => {
        const list = Array.isArray(res) ? res : (res.data || [])
        this.keBianResults = list
        this.keBianColumnKeys = list.length ? Object.keys(list[0]) : []
        if (list.length === 0) {
          this.$message.info('No results found')
        } else if (list.length === 1) {
          // Auto-select if only one result
          this.selectKeBian(list[0])
        } else {
          // Show first result as selected for now
          this.selectKeBian(list[0])
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.searchKeBianLoading = false
      })
    },
    selectKeBian(row) {
      const id = Number(this.getIdFromRow(row)) || 0
      this.selectedKeBian = row
      this.form.ke_bian = id
      this.form.details_json = {
        ...this.form.details_json,
        ke_bian: row
      }
      this.keBianResults = []
      this.$nextTick(() => {
        this.$refs.form.validateField('ke_bian')
      })
    },
    searchCheLiang() {
      if (!this.searchTextC) {
        this.$message.warning('Enter text to search')
        return
      }
      this.searchCheLiangLoading = true
      request({
        url: '/api/v1/jia_yi_info',
        method: 'get',
        params: { q: this.searchTextC }
      }, 'shwethe_guiling').then(res => {
        const list = Array.isArray(res) ? res : (res.data || [])
        this.cheLiangResults = list
        this.cheLiangColumnKeys = list.length ? Object.keys(list[0]) : []
        if (list.length === 0) {
          this.$message.info('No results found')
        } else if (list.length === 1) {
          // Auto-select if only one result
          this.selectCheLiang(list[0])
        } else {
          // Show first result as selected for now
          this.selectCheLiang(list[0])
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.searchCheLiangLoading = false
      })
    },
    selectCheLiang(row) {
      const id = Number(this.getIdFromRow(row)) || 0
      this.selectedCheLiang = row
      this.form.che_liang = id
      this.form.details_json = {
        ...this.form.details_json,
        che_liang: row
      }
      this.cheLiangResults = []
    },
    onLeiAChange(value) {
      const selectedLei = this.leiOptions.find(item => item.id === value)
      if (selectedLei) {
        this.form.details_json = {
          ...this.form.details_json,
          lei_a: selectedLei
        }
      }
    },
    onLeiBChange(value) {
      const selectedLei = this.leiOptions.find(item => item.id === value)
      if (selectedLei) {
        this.form.details_json = {
          ...this.form.details_json,
          lei_b: selectedLei
        }
      }
    },
    onBiZhiChange(value) {
      const selected = this.biZhiOptions.find(item => item.id === value)
      if (selected) {
        this.form.details_json = {
          ...this.form.details_json,
          bi_zhi: selected
        }
      }
    },
    formatSelectedItem(item) {
      if (!item) return ''
      // Try to format as ID | Code | Name
      const values = []

      // Look for ID field
      for (const key in item) {
        if (/id$/i.test(key) || /Id$/i.test(key)) {
          values.push(item[key])
          break
        }
      }

      // Look for code/name fields
      for (const key in item) {
        if (key.toLowerCase().includes('name') && !key.toLowerCase().includes('id')) {
          values.push(item[key])
        }
      }

      // If we don't have enough values, add more from the object
      if (values.length < 3) {
        for (const key in item) {
          if (!values.includes(item[key]) && item[key] && typeof item[key] === 'string') {
            values.push(item[key])
            if (values.length >= 3) break
          }
        }
      }

      return values.slice(0, 3).join(' | ')
    },
    formatSelectedProduct(item) {
      if (!item) return ''
      // Format as ID | Code | Name for product
      const values = []

      if (item.ProductId) values.push(item.ProductId)
      if (item.ProductIdname) values.push(item.ProductIdname)
      if (item.ProductMmName) values.push(item.ProductMmName)

      return values.join(' | ')
    },
    onProductPriceAChange(value) {
      // Sync Product Price B with Product Price A
      this.form.product_price_b = Number(value) || null
    },
    onProductPriceBChange(value) {
      // Sync Product Price A with Product Price B
      this.form.product_price_a = Number(value) || null
    },
    onProductSearchChange() {
      // Clear selected product when search text changes
      this.selectedProduct = null
      this.form.product_id = null
      this.$nextTick(() => {
        this.$refs.form.validateField('product_id')
      })
    },
    onKeBianSearchChange() {
      // Clear selected Ke Bian when search text changes
      this.selectedKeBian = null
      this.form.ke_bian = null
      // Note: Ke Bian doesn't have validation as per requirements
    },
    onCheLiangSearchChange() {
      // Clear selected Che Liang when search text changes
      this.selectedCheLiang = null
      this.form.che_liang = null
      // Note: Che Liang doesn't have validation as per requirements
    },
    onJiaYiASearchChange() {
      // Clear selected Jia Yi A when search text changes
      this.selectedJiaYiA = null
      this.form.jia_yi_fang_a = null
      this.$nextTick(() => {
        this.$refs.form.validateField('jia_yi_fang_a')
      })
    },
    onJiaYiBSearchChange() {
      // Clear selected Jia Yi B when search text changes
      this.selectedJiaYiB = null
      this.form.jia_yi_fang_b = null
      this.$nextTick(() => {
        this.$refs.form.validateField('jia_yi_fang_b')
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.flexible-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 1000px;
  margin: 0 auto;
}

.grid-card {
  width: 100%;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
}

.grid-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
}

.grid-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f5f7fa;
}

.record-id {
  font-weight: 600;
  font-size: 15px;
  color: #303133;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-code {
  font-size: 14px;
  color: #409EFF;
  font-weight: 500;
}

.grid-card-content {
  padding: 12px;
}

.grid-section {
  margin-bottom: 16px;
}

.grid-section .section-header {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
  margin: 12px 0 8px 0;
  display: flex;
  align-items: center;
  padding-bottom: 4px;
  border-bottom: 1px solid #ebeef5;
}

.grid-section .section-header i {
  margin-right: 6px;
  color: #409EFF;
}

.grid-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.grid-cell {
  flex: 1;
  min-width: 150px;
  background-color: #f9f9f9;
  padding: 6px 10px;
  border-radius: 4px;
}

.cell-label {
  font-weight: 500;
  color: #909399;
  margin-bottom: 4px;
  font-size: 11px;
}

.cell-value {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
}

.grid-card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed #f0f0f0;
}

@media (max-width: 768px) {
  .grid-row {
    flex-direction: column;
  }

  .grid-cell {
    flex: 1 0 100%;
  }
}

/* Form styling improvements */
.el-form-item {
  margin-bottom: 0 !important;
}

.el-form-item__error {
  position: static !important;
  margin-top: 4px;
  font-size: 11px;
}

.el-input--mini .el-input__inner {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.2s ease;
  font-size: 12px;
  padding: 0 8px;
  background-color: #ffffff;
}

.el-input--mini .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

.el-select {
  width: 100%;
}

.el-date-editor.el-input {
  width: 100%;
}

.el-button--mini {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 7px 15px;
}

.el-button--primary.el-button--mini {
  background: #409EFF;
  border-color: #409EFF;
}

.el-button--primary.el-button--mini:hover {
  background: #66b3ff;
  border-color: #66b3ff;
}

/* Selected item styling */
.cell-value div[style*="color:#409EFF"] {
  background: #ecf5ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
  font-size: 11px;
  font-weight: 500;
  margin-top: 4px;
}

/* Tab styling */
.el-tabs--card > .el-tabs__header {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
  background: #f5f7fa;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-left: 1px solid #e4e7ed;
  border-right: none;
  background: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  transition: all 0.3s ease;
}

.el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
  border-left: none;
  border-radius: 8px 0 0 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__item:last-child {
  border-radius: 0 8px 0 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background: #ffffff;
  color: #409EFF;
  border-bottom-color: #ffffff;
  font-weight: 600;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.el-tabs--card > .el-tabs__header .el-tabs__item:hover:not(.is-active) {
  background: #ecf5ff;
  color: #409EFF;
}

.el-tabs__content {
  padding: 0;
}

.el-tab-pane {
  background: transparent;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .flexible-grid {
    grid-template-columns: 1fr;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 15px;
  }

  .flexible-grid {
    gap: 15px;
  }

  .grid-row {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  .grid-card-header {
    padding: 15px 15px 0 15px;
  }

  .grid-card-content {
    padding: 0 15px 15px 15px;
  }
}

@media (max-width: 480px) {
  .grid-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .grid-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .record-id {
    font-size: 16px;
  }

  .grid-card-footer {
    flex-direction: column;
    gap: 8px;
  }

  .grid-card-footer .el-button {
    width: 100%;
  }
}
</style>
