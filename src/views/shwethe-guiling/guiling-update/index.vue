<template>
  <div class="app-container">
    <!-- Navigation Header -->
    <!-- <div class="page-header">
      <el-button 
        type="info" 
        icon="el-icon-link" 
        size="small" 
        @click="openDAG"
        class="dag-button"
      >
        View DAG Workflow
      </el-button>
    </div> -->

    <div style="display:flex; align-items:center; margin-bottom:10px;">
      <el-button type="primary" icon="el-icon-refresh" :loading="loading || bigTableLoading" size="mini" @click="refreshCurrentTab">Refresh</el-button>
    </div>

    <!-- Tabs for Today and Search -->
    <el-tabs v-model="activeTab" type="card">

      <!-- Today Tab -->
      <el-tab-pane label="Today" name="today">
        <!-- Grid View -->
        <div v-loading="loading" class="flexible-grid">
          <el-card v-for="item in rows" :key="item.auto_id" class="grid-card" shadow="hover">
            <div class="grid-card-header">
              <div class="record-id">#{{ item.record_id }}</div>
              <div class="product-code">{{ item.type_edit }}</div>
            </div>

            <div class="grid-card-content">
              <div class="grid-section">
                <div class="grid-row">
                  <div class="grid-cell">
                    <div class="cell-label">shu_riqi_datetime</div>
                    <div class="cell-value">{{ item.shu_riqi_datetime || 'N/A' }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">bu_bian</div>
                    <div class="cell-value">{{ item.bu_bian || 'N/A' }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">riqi_datetime</div>
                    <div class="cell-value">{{ item.riqi_datetime || 'N/A' }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Bi Zhi</div>
                    <div class="cell-value">{{ getBiZhiName(item) }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Ke Bian</div>
                    <div class="cell-value">{{ formatDetail(item.details_ke_bian) || 'N/A' }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Che Liang</div>
                    <div class="cell-value">{{ formatDetail(item.details_che_liang) }}</div>
                  </div>
                </div>
              </div>

              <div class="grid-section">
                <div class="grid-row">
                  <div class="grid-cell">
                    <div class="cell-label">Jia Yi A</div>
                    <div class="cell-value">{{ formatDetail(item.details_jia_yi_fang_a) }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Lei A</div>
                    <div class="cell-value">{{ getLeiName(item, 'lei_a') }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Product</div>
                    <div class="cell-value">{{ getProductDescription(item) }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">product_price_a</div>
                    <div class="cell-value">{{ item.product_price_a }}</div>
                  </div>
                </div>
              </div>
              <div class="grid-section">
                <div class="grid-row">
                  <div class="grid-cell">
                    <div class="cell-label">Jia Yi B</div>
                    <div class="cell-value">{{ formatDetail(item.details_jia_yi_fang_b) }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Lei B</div>
                    <div class="cell-value">{{ getLeiName(item, 'lei_b') }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">product_qty</div>
                    <div class="cell-value">{{ item.product_qty }}</div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">product_price_b</div>
                    <div class="cell-value">{{ item.product_price_b }}</div>
                  </div>
                </div>
              </div>
              <!-- <div class="grid-card-footer">
                <el-button
                  v-if="isToday(item.shu_riqi_datetime)"
                  type="text"
                  size="mini"
                  icon="el-icon-edit"
                  @click.stop="handleRowClick(item)"
                >Edit</el-button>
              </div> -->
            </div>
          </el-card>
        </div>

        <!-- Pagination for Today Tab -->
        <div style="margin-top: 20px;">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            @pagination="fetchData"
          />
        </div>
      </el-tab-pane>

      <!-- Search Tab -->
      <el-tab-pane label="Search" name="search">
        <!-- Inline template card handles inputs -->
        <div v-loading="searchLoading" class="flexible-grid">
          <!-- Blank template card (for new insert) -->
          <el-card class="grid-card" shadow="hover">
            <div class="grid-card-header">
              <!-- <div class="record-id">#</div> -->
              <!-- <el-button type="text" style="margin-left:auto;" @click="dialogVisible = true">insert</el-button> -->
            </div>
            <div class="grid-card-content">
              <div class="grid-section">
                <div class="grid-row">
                  <div class="grid-cell">
                    <div class="cell-label">Shu Riqi Datetime</div>
                    <div class="cell-value">
                      <el-date-picker v-model="searchCriteria.shu_riqi_datetime" type="datetime" placeholder="Select date" size="mini" style="width:100%;" value-format="yyyy-MM-dd HH:mm:ss" />
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Bu Bian</div>
                    <div class="cell-value">
                      <el-input v-model.number="searchCriteria.bu_bian" size="mini" placeholder="Bu Bian" />
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Riqi Datetime</div>
                    <div class="cell-value">
                      <el-date-picker v-model="searchCriteria.riqi_datetime" type="datetime" placeholder="Select date" size="mini" style="width:100%;" value-format="yyyy-MM-dd HH:mm:ss" />
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Bi Zhi</div>
                    <div class="cell-value">
                      <el-select v-model="searchCriteria.bi_zhi" placeholder="Bi Zhi" clearable filterable size="mini" style="width:100%;">
                        <el-option v-for="opt in biZhiOptions" :key="opt.id" :label="opt.name" :value="opt.id" />
                      </el-select>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Ke Bian</div>
                    <div class="cell-value">
                      <el-autocomplete
                        v-model="searchKeBianText"
                        :fetch-suggestions="queryJiaYiSearch"
                        placeholder="Type to search Ke Bian"
                        :trigger-on-focus="true"
                        size="mini"
                        style="width:100%;"
                        :popper-class="'product-search-dropdown expanded-dropdown'"
                        @select="item => handleJiaYiSelect(item, 'ke_bian')"
                        @input="handleSearchKeBianInput"
                      >
                        <template slot-scope="{item}">
                          <div class="product-suggestion-item">
                            <div class="product-code">{{ item.jia_yi_id_name }}</div>
                            <div class="product-name">{{ item.jia_yi_mm_name }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <div v-if="selectedKeBian" style="margin-top:4px; font-size:12px; color:#409EFF;">
                        Selected: {{ selectedKeBian.jia_yi_id_name }} - {{ selectedKeBian.jia_yi_mm_name }}
                      </div>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Che Liang</div>
                    <div class="cell-value">
                      <el-autocomplete
                        v-model="searchCheLiangText"
                        :fetch-suggestions="queryJiaYiSearch"
                        placeholder="Type to search Che Liang"
                        :trigger-on-focus="true"
                        size="mini"
                        style="width:100%;"
                        :popper-class="'product-search-dropdown expanded-dropdown'"
                        @select="item => handleJiaYiSelect(item, 'che_liang')"
                        @input="handleSearchCheLiangInput"
                      >
                        <template slot-scope="{item}">
                          <div class="product-suggestion-item">
                            <div class="product-code">{{ item.jia_yi_id_name }}</div>
                            <div class="product-name">{{ item.jia_yi_mm_name }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <div v-if="selectedCheLiang" style="margin-top:4px; font-size:12px; color:#409EFF;">
                        Selected: {{ selectedCheLiang.jia_yi_id_name }} - {{ selectedCheLiang.jia_yi_mm_name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="grid-section">
                <div class="grid-row">
                  <div class="grid-cell">
                    <div class="cell-label">Jia Yi Fang A</div>
                    <div class="cell-value">
                      <el-autocomplete
                        v-model="searchJiaYiAText"
                        :fetch-suggestions="queryJiaYiSearch"
                        placeholder="Type to search Jia Yi A"
                        :trigger-on-focus="true"
                        size="mini"
                        style="width:100%;"
                        :popper-class="'product-search-dropdown expanded-dropdown'"
                        @select="item => handleJiaYiSelect(item, 'jia_yi_fang_a')"
                        @input="handleSearchJiaYiAInput"
                      >
                        <template slot-scope="{item}">
                          <div class="product-suggestion-item">
                            <div class="product-code">{{ item.jia_yi_id_name }}</div>
                            <div class="product-name">{{ item.jia_yi_mm_name }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <div v-if="selectedJiaYiA" style="margin-top:4px; font-size:12px; color:#409EFF;">
                        Selected: {{ selectedJiaYiA.jia_yi_id_name }} - {{ selectedJiaYiA.jia_yi_mm_name }}
                      </div>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Lei A</div>
                    <div class="cell-value">
                      <el-select v-model="searchCriteria.lei_a" placeholder="Lei A" clearable filterable size="mini" style="width:100%;">
                        <el-option v-for="item in leiOptions" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Product ID</div>
                    <div class="cell-value">
                      <el-autocomplete
                        v-model="searchProductText"
                        :fetch-suggestions="queryProductSearch"
                        placeholder="Type to search products"
                        :trigger-on-focus="true"
                        size="mini"
                        style="width:100%;"
                        :popper-class="'product-search-dropdown expanded-dropdown'"
                        @select="handleProductSelect"
                        @input="handleSearchProductInput"
                      >
                        <template slot-scope="{item}">
                          <div class="product-suggestion-item">
                            <div class="product-code">{{ item.product_id_name || item.ProductIdname }}</div>
                            <div class="product-name">{{ item.product_mm_name || item.ProductMmName }}</div>
                            <div class="product-desc">{{ item.product_d_name || item.ProductDName }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <div v-if="selectedProduct" style="margin-top:4px; font-size:12px; color:#409EFF;">
                        Selected: {{ selectedProduct.product_id_name || selectedProduct.ProductIdname }}
                      </div>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Product Price A</div>
                    <div class="cell-value">
                      <el-input v-model.number="searchCriteria.product_price_a" size="mini" placeholder="Price A" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="grid-section">
                <div class="grid-row">
                  <div class="grid-cell">
                    <div class="cell-label">Jia Yi Fang B</div>
                    <div class="cell-value">
                      <el-autocomplete
                        v-model="searchJiaYiBText"
                        :fetch-suggestions="queryJiaYiSearch"
                        placeholder="Type to search Jia Yi B"
                        :trigger-on-focus="true"
                        size="mini"
                        style="width:100%;"
                        :popper-class="'product-search-dropdown expanded-dropdown'"
                        @select="item => handleJiaYiSelect(item, 'jia_yi_fang_b')"
                        @input="handleSearchJiaYiBInput"
                      >
                        <template slot-scope="{item}">
                          <div class="product-suggestion-item">
                            <div class="product-code">{{ item.jia_yi_id_name }}</div>
                            <div class="product-name">{{ item.jia_yi_mm_name }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <div v-if="selectedJiaYiB" style="margin-top:4px; font-size:12px; color:#409EFF;">
                        Selected: {{ selectedJiaYiB.jia_yi_id_name }} - {{ selectedJiaYiB.jia_yi_mm_name }}
                      </div>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Lei B</div>
                    <div class="cell-value">
                      <el-select v-model="searchCriteria.lei_b" placeholder="Lei B" clearable filterable size="mini" style="width:100%;">
                        <el-option v-for="item in leiOptions" :key="item.id" :label="item.name" :value="item.id" />
                      </el-select>
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Product Qty</div>
                    <div class="cell-value">
                      <el-input v-model.number="searchCriteria.product_qty" size="mini" placeholder="Qty" />
                    </div>
                  </div>
                  <div class="grid-cell">
                    <div class="cell-label">Product Price B</div>
                    <div class="cell-value">
                      <el-input v-model.number="searchCriteria.product_price_b" size="mini" placeholder="Price B" />
                    </div>
                  </div>

                </div>
              </div>
              <div class="grid-section">
                <div class="grid-row" />
              </div>
              <div class="grid-card-footer">
                <el-button type="primary" size="mini" icon="el-icon-search" @click="searchRecords">Search</el-button>
                <el-button size="mini" @click="resetSearch">Reset</el-button>
              </div>
            </div>
          </el-card>

          <!-- Search results -->
          <template v-for="item in searchRows">
            <el-card :key="item.auto_id" class="grid-card" shadow="hover">
              <div class="grid-card-header">
                <div class="record-id">#{{ item.record_id }}</div>
                <div class="product-code">{{ item.type_edit }}</div>
              </div>
              <div class="grid-card-content">
                <!-- replicate same sections as Today card for brevity using existing helper methods -->
                <div class="grid-section">
                  <div class="grid-row">
                    <div class="grid-cell">
                      <div class="cell-label">shu_riqi_datetime</div>
                      <div class="cell-value">{{ item.shu_riqi_datetime || 'N/A' }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">bu_bian</div>
                      <div class="cell-value">{{ item.bu_bian || 'N/A' }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">riqi_datetime</div>
                      <div class="cell-value">{{ item.riqi_datetime || 'N/A' }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Bi Zhi</div>
                      <div class="cell-value">{{ getBiZhiName(item) }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Ke Bian</div>
                      <div class="cell-value">{{ formatDetail(item.details_ke_bian) || 'N/A' }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Che Liang</div>
                      <div class="cell-value">{{ formatDetail(item.details_che_liang) }}</div>
                    </div>
                  </div>
                </div>
                <div class="grid-section">
                  <div class="grid-row">
                    <div class="grid-cell">
                      <div class="cell-label">Jia Yi A</div>
                      <div class="cell-value">{{ formatDetail(item.details_jia_yi_fang_a) }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Lei A</div>
                      <div class="cell-value">{{ getLeiName(item, 'lei_a') }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Product</div>
                      <div class="cell-value">{{ getProductDescription(item) }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">product_price_a</div>
                      <div class="cell-value">{{ item.product_price_a }}</div>
                    </div>
                  </div>
                </div>
                <div class="grid-section">
                  <div class="grid-row">
                    <div class="grid-cell">
                      <div class="cell-label">Jia Yi B</div>
                      <div class="cell-value">{{ formatDetail(item.details_jia_yi_fang_b) }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">Lei B</div>
                      <div class="cell-value">{{ getLeiName(item, 'lei_b') }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">product_qty</div>
                      <div class="cell-value">{{ item.product_qty }}</div>
                    </div>
                    <div class="grid-cell">
                      <div class="cell-label">product_price_b</div>
                      <div class="cell-value">{{ item.product_price_b }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </template>
        </div>
      </el-tab-pane>

      <!-- Big Table Sync Tab -->
      <el-tab-pane label="Big Table Sync" name="bigtable">
        <div v-loading="bigTableLoading" class="flexible-grid">
          <!-- Empty state when no data -->
          <div v-if="bigTableData.length === 0 && !bigTableLoading" class="empty-state">
            <i class="el-icon-document"></i>
            <p>No data available for Big Table Sync</p>
          </div>

          <!-- Big Table Data Cards -->
          <el-card
            v-for="item in bigTableData"
            :key="item.auto_id"
            class="grid-card big-table-card"
            :class="{ 'processing': processingCardId === item.auto_id }"
            shadow="hover"
            @click.native="handleBigTableCardClick(item)"
            v-loading="processingCardId === item.auto_id"
            element-loading-text="Sending to Big Table..."
            element-loading-spinner="el-icon-loading"
          >
            <div class="grid-card-header">
              <div class="record-id">#{{ item.auto_id }}</div>
              <div class="sync-status" :class="{ 'processing': processingCardId === item.auto_id }">
                {{ processingCardId === item.auto_id ? 'Sending...' : 'Ready to Sync' }}
              </div>
            </div>
            <div class="grid-card-content">
              <div class="big-table-date">
                <i class="el-icon-date"></i>
                <span>{{ item.riqi_datetime_formatted }}</span>
              </div>
              <div class="big-table-timestamp">
                <span class="timestamp-label">Timestamp:</span>
                <span class="timestamp-value">{{ item.riqi_datetime }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

    </el-tabs>

    <!-- Update Form Dialog -->
    <el-dialog title="Update Form" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false" class="update-dialog">
      <el-form ref="form" v-loading="formLoading" :model="form" :rules="rules" label-width="160px">
        <el-form-item label="Product" prop="product_id">
          <div style="display:flex; align-items:center;">
            <el-input v-model="searchText" placeholder="Enter product code or name" style="flex:1" />
            <el-button type="primary" icon="el-icon-search" style="margin-left:8px" @click="searchProduct">Search</el-button>
          </div>
          <div v-if="products.length" style="margin-top:10px;">
            <el-table :data="products" size="mini" border style="width:100%" @row-click="selectProduct">
              <el-table-column prop="ProductId" label="ID" width="80" />
              <el-table-column prop="ProductIdname" label="Code" width="120" />
              <el-table-column prop="ProductMmName" label="Name" />
              <el-table-column prop="ProductDName" label="Desc" />
              <el-table-column prop="ProductPrice" label="Price" width="80" />
            </el-table>
            <p style="margin:4px 0;">Click a row to select product.</p>
          </div>
          <div v-if="form.product_id" style="margin-top:4px;color:#409EFF;">Selected Product ID: {{ form.product_id }}</div>
        </el-form-item>

        <el-form-item label="Che Liang" prop="che_liang">
          <div style="display:flex; align-items:center;">
            <el-input v-model="searchTextC" placeholder="Search Che Liang" style="flex:1" />
            <el-button type="primary" icon="el-icon-search" style="margin-left:8px" @click="searchCheLiang">Search</el-button>
          </div>
          <div v-if="cheLiangResults.length" style="margin-top:10px;">
            <el-table :data="cheLiangResults" size="mini" border style="width:100%" @row-click="selectCheLiang">
              <el-table-column v-for="key in cheLiangColumnKeys" :key="key" :prop="key" :label="key" />
            </el-table>
            <p style="margin:4px 0;">Click a row to select.</p>
          </div>
          <div v-if="form.che_liang" style="margin-top:4px;color:#409EFF;">Selected Che Liang ID: {{ form.che_liang }}</div>
        </el-form-item>

        <el-form-item label="Product Qty" prop="product_qty">
          <el-input v-model.number="form.product_qty" placeholder="Enter quantity" />
        </el-form-item>

        <el-form-item label="Product Price A" prop="product_price_a">
          <el-input v-model.number="form.product_price_a" placeholder="Enter price A" />
        </el-form-item>

        <el-form-item label="Product Price B" prop="product_price_b">
          <el-input v-model.number="form.product_price_b" placeholder="Enter price B" />
        </el-form-item>

        <el-form-item label="Ke Bian" prop="ke_bian">
          <div style="display:flex; align-items:center;">
            <el-input v-model="searchTextK" placeholder="Search Ke Bian" style="flex:1" />
            <el-button type="primary" icon="el-icon-search" style="margin-left:8px" @click="searchKeBian">Search</el-button>
          </div>
          <div v-if="keBianResults.length" style="margin-top:10px;">
            <el-table :data="keBianResults" size="mini" border style="width:100%" @row-click="selectKeBian">
              <el-table-column v-for="key in keBianColumnKeys" :key="key" :prop="key" :label="key" />
            </el-table>
            <p style="margin:4px 0;">Click a row to select.</p>
          </div>
          <div v-if="form.ke_bian" style="margin-top:4px;color:#409EFF;">Selected Ke Bian ID: {{ form.ke_bian }}</div>
        </el-form-item>

        <el-form-item label="Jia Yi Fang A" prop="jia_yi_fang_a">
          <div style="display:flex; align-items:center;">
            <el-input v-model="searchTextA" placeholder="Search Jia Yi A" style="flex:1" />
            <el-button type="primary" icon="el-icon-search" style="margin-left:8px" @click="searchJiaYi('A')">Search</el-button>
          </div>
          <div v-if="jiaYiResultsA.length" style="margin-top:10px;">
            <el-table :data="jiaYiResultsA" size="mini" border style="width:100%" @row-click="row => selectJiaYi('A', row)">
              <el-table-column v-for="key in jiaYiColumnKeysA" :key="key" :prop="key" :label="key" />
            </el-table>
            <p style="margin:4px 0;">Click a row to select.</p>
          </div>
          <div v-if="form.jia_yi_fang_a" style="margin-top:4px;color:#409EFF;">Selected Jia Yi A ID: {{ form.jia_yi_fang_a }}</div>
        </el-form-item>

        <el-form-item label="Jia Yi Fang B" prop="jia_yi_fang_b">
          <div style="display:flex; align-items:center;">
            <el-input v-model="searchTextB" placeholder="Search Jia Yi B" style="flex:1" />
            <el-button type="primary" icon="el-icon-search" style="margin-left:8px" @click="searchJiaYi('B')">Search</el-button>
          </div>
          <div v-if="jiaYiResultsB.length" style="margin-top:10px;">
            <el-table :data="jiaYiResultsB" size="mini" border style="width:100%" @row-click="row => selectJiaYi('B', row)">
              <el-table-column v-for="key in jiaYiColumnKeysB" :key="key" :prop="key" :label="key" />
            </el-table>
            <p style="margin:4px 0;">Click a row to select.</p>
          </div>
          <div v-if="form.jia_yi_fang_b" style="margin-top:4px;color:#409EFF;">Selected Jia Yi B ID: {{ form.jia_yi_fang_b }}</div>
        </el-form-item>

        <el-form-item label="Lei A" prop="lei_a">
          <el-select v-model="form.lei_a" placeholder="Select Lei A" filterable @change="onLeiAChange">
            <el-option
              v-for="item in leiOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Lei B" prop="lei_b">
          <el-select v-model="form.lei_b" placeholder="Select Lei B" filterable @change="onLeiBChange">
            <el-option
              v-for="item in leiOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Bu Bian" prop="bu_bian">
          <el-input v-model.number="form.bu_bian" placeholder="Enter Bu Bian" />
        </el-form-item>

        <el-form-item label="Shu Riqi Datetime" prop="shu_riqi_datetime">
          <el-date-picker v-model="form.shu_riqi_datetime" type="datetime" placeholder="Select date & time" value-format="yyyy-MM-dd HH:mm:ss" disabled />
        </el-form-item>

        <el-form-item label="Riqi Datetime" prop="riqi_datetime">
          <div style="display:flex; align-items:center;">
            <el-date-picker v-model="form.riqi_datetime" type="datetime" placeholder="Select date & time" value-format="yyyy-MM-dd HH:mm:ss" style="flex:1" clearable />
            <el-button type="primary" size="mini" style="margin-left:8px" @click="setNowDateTime">Now</el-button>
          </div>
        </el-form-item>

        <el-form-item label="Bi Zhi" prop="bi_zhi">
          <el-select v-model="form.bi_zhi" placeholder="Select Bi Zhi" filterable @change="onBiZhiChange">
            <el-option
              v-for="item in biZhiOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitLoading" :disabled="!formEdited" @click="submitForm">Update</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import Pagination from '@/components/Pagination'

export default {
  name: 'GuilingUpdate',
  components: {
    Pagination
  },
  data() {
    return {
      rows: [],
      loading: false,
      // Pagination properties
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      dialogVisible: false,
      formLoading: false,
      submitLoading: false,
      form: {
        auto_id: null,
        product_id: null,
        product_qty: null,
        product_price_a: null,
        product_price_b: null,
        ke_bian: null,
        jia_yi_fang_a: null,
        jia_yi_fang_b: null,
        lei_a: null,
        lei_b: null,
        bu_bian: null,
        shu_riqi_datetime: '',
        riqi_datetime: '',
        che_liang: null,
        bi_zhi: null,
        details_json: {}
      },
      rules: {
        product_id: [
          { required: true, message: 'Product ID is required', trigger: 'change' }
        ],
        product_qty: [
          { type: 'number', required: true, message: 'Product quantity is required', trigger: 'blur' }
        ],
        product_price_a: [
          { type: 'number', required: true, message: 'Product price A is required', trigger: 'blur' }
        ],
        product_price_b: [
          { type: 'number', required: true, message: 'Product price B is required', trigger: 'blur' }
        ],
        ke_bian: [
          { required: true, message: 'Ke Bian is required', trigger: 'blur' }
        ],
        jia_yi_fang_a: [
          { required: true, message: 'Jia Yi Fang A is required', trigger: 'blur' }
        ],
        jia_yi_fang_b: [
          { required: true, message: 'Jia Yi Fang B is required', trigger: 'blur' }
        ],
        lei_a: [
          { required: true, message: 'Lei A is required', trigger: 'change' },
          { type: 'number', message: 'Lei A must be a number', trigger: 'change' }
        ],
        lei_b: [
          { required: true, message: 'Lei B is required', trigger: 'change' },
          { type: 'number', message: 'Lei B must be a number', trigger: 'change' }
        ],
        bu_bian: [
          { type: 'number', required: true, message: 'Bu Bian is required', trigger: 'blur' }
        ],
        shu_riqi_datetime: [
          { required: true, message: 'Shu Riqi Datetime is required', trigger: 'change' }
        ],
        riqi_datetime: [
          { required: true, message: 'Riqi Datetime is required', trigger: 'change' }
        ],
        che_liang: [
          { required: true, message: 'Che Liang is required', trigger: 'blur' }
        ],
        bi_zhi: [
          { required: true, message: 'Bi Zhi is required', trigger: 'blur' }
        ]
      },
      searchText: '',
      products: [],
      searchTextA: '',
      searchTextB: '',
      jiaYiResultsA: [],
      jiaYiResultsB: [],
      jiaYiColumnKeysA: [],
      jiaYiColumnKeysB: [],
      productDetails: null,
      jiaYiDetailsA: null,
      jiaYiDetailsB: null,
      formEdited: false,
      originalForm: null,
      searchTextK: '',
      keBianResults: [],
      keBianColumnKeys: [],
      searchTextC: '',
      cheLiangResults: [],
      cheLiangColumnKeys: [],
      keBianDetails: null,
      cheLiangDetails: null,
      expandedCards: {},
      leiOptions: [],
      biZhiOptions: [],
      activeTab: 'today',
      searchRows: [],
      searchLoading: false,
      searchProductText: '',
      searchProductResult: null,
      searchCriteria: {
        bi_zhi: null,
        bu_bian: null,
        product_id: null,
        product_qty: null,
        product_price_a: null,
        product_price_b: null,
        ke_bian: null,
        jia_yi_fang_a: null,
        jia_yi_fang_b: null,
        lei_a: null,
        lei_b: null,
        shu_riqi_datetime: null,
        riqi_datetime: null,
        che_liang: null
      },
      selectedProduct: null,
      searchKeBianText: '',
      selectedKeBian: null,
      searchCheLiangText: '',
      selectedCheLiang: null,
      searchJiaYiAText: '',
      selectedJiaYiA: null,
      searchJiaYiBText: '',
      selectedJiaYiB: null,
      // Big Table Sync data
      bigTableData: [],
      bigTableLoading: false,
      sendToBigTableLoading: false,
      processingCardId: null
    }
  },
  computed: {
    rowsToday() {
      return this.rows.filter(r => this.isToday(r.shu_riqi_datetime))
    }
  },
  watch: {
    form: {
      handler(newVal) {
        if (this.originalForm) {
          // Compare current form with original to detect changes
          this.formEdited = JSON.stringify(newVal) !== JSON.stringify(this.originalForm)
        }
      },
      deep: true
    },
    '$route'(to) {
      // Auto-fetch data when navigating to this page
      if (to.path.includes('guiling-update')) {
        console.log('Route changed to guiling-update, auto-fetching data...')
        this.fetchData()
      }
    }
  },
  created() {
    console.log('Component created, initializing...')
    this.fetchData()
    // fetch lei and bi zhi options
    this.fetchLeiOptions()
    this.fetchBiZhiOptions()
    // fetch big table data
    this.fetchBigTableData()
  },
  activated() {
    // This will be called when navigating to this page (if using keep-alive)
    console.log('Component activated, auto-fetching data...')
    this.fetchData()
  },
  methods: {
    openDAG() {
      window.open('http://192.168.1.102:8080/tree?dag_id=dags-shwethe_guiling', '_blank')
    },
    refreshCurrentTab() {
      if (this.activeTab === 'bigtable') {
        this.fetchBigTableData()
      } else {
        this.fetchData()
      }
    },
    fetchLeiOptions() {
      request({
        url: '/api/v1/code',
        method: 'get',
        params: { type_item: 'lei' }
      }, 'shwethe_guiling').then(res => {
        this.leiOptions = Array.isArray(res) ? res : (res.data || [])
      }).catch(err => {
        console.error('Failed to load Lei options:', err)
        this.$message.error('Failed to load Lei options')
      })
    },
    fetchBiZhiOptions() {
      request({
        url: '/api/v1/code',
        method: 'get',
        params: { type_item: 'currency' }
      }, 'shwethe_guiling').then(res => {
        this.biZhiOptions = Array.isArray(res) ? res : (res.data || [])
      }).catch(err => {
        console.error('Failed to load Bi Zhi options:', err)
        this.$message.error('Failed to load Bi Zhi options')
      })
    },
    handleRowClick(row) {
      // Clone row data to form to avoid direct reference
      this.form = JSON.parse(JSON.stringify(row))
      // Store original form data for comparison
      this.originalForm = JSON.parse(JSON.stringify(row))
      // Reset edited flag
      this.formEdited = false
      this.dialogVisible = true

      // Auto-search for product info
      if (this.form.product_id) {
        // Set search text to product ID to simulate user typing it
        this.searchText = String(this.form.product_id)
        // Auto-click search
        this.searchProduct()
      }

      // Auto-search for Jia Yi Fang A
      if (this.form.jia_yi_fang_a) {
        this.searchTextA = String(this.form.jia_yi_fang_a)
        this.searchJiaYi('A')
      }

      // Auto-search for Jia Yi Fang B
      if (this.form.jia_yi_fang_b) {
        this.searchTextB = String(this.form.jia_yi_fang_b)
        this.searchJiaYi('B')
      }

      // Auto-search for Ke Bian
      if (this.form.ke_bian) {
        this.searchTextK = String(this.form.ke_bian)
        this.searchKeBian()
      }

      // Auto-search for Che Liang
      if (this.form.che_liang) {
        this.searchTextC = String(this.form.che_liang)
        this.searchCheLiang()
      }

      // Ensure riqi_datetime has default if missing
      if (!this.form.riqi_datetime) {
        this.form.riqi_datetime = this.getNowDateTimeString()
      } else {
        // normalize to remove potential millisecond part
        this.form.riqi_datetime = String(this.form.riqi_datetime).split('.')[0]
      }
    },
    getJiaYiDisplayName(item) {
      // Try to find a name-like field in the item
      if (!item) return ''
      for (const key in item) {
        if (key.toLowerCase().includes('name') || key.toLowerCase().includes('title')) {
          return item[key]
        }
      }
      // If no name field found, return the first value that's not an ID
      for (const key in item) {
        if (!key.toLowerCase().includes('id')) {
          return item[key]
        }
      }
      return Object.values(item)[0] || ''
    },
    searchProduct() {
      if (!this.searchText) {
        this.$message.warning('Enter text to search')
        return
      }
      // Clear previous product details when searching
      this.productDetails = null
      // Clear form field to allow new selection
      this.form.product_id = null
      this.formLoading = true
      request({
        url: '/api/v1/product_info',
        method: 'get',
        params: { text: this.searchText }
      }, 'shwethe_guiling').then(res => {
        this.products = Array.isArray(res) ? res : (res.data || [])
        if (this.products.length === 0) {
          this.$message.info('No products found')
        }
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.formLoading = false
      })
    },
    selectProduct(row) {
      // Store product details for display
      this.productDetails = row
      this.form.product_id = Number(row.ProductId) || 0
      // Store full product details in details_json
      this.form.details_json = {
        ...this.form.details_json,
        product: row
      }
      this.$nextTick(() => {
        this.$refs.form.validateField('product_id')
      })
      // Removed auto price assignment so user can fill manually
      this.$message.success(`Selected ${row.ProductIdname}`)
      this.products = []
    },
    getIdFromRow(row) {
      for (const key in row) {
        if (/id$/i.test(key) || /Id/i.test(key)) return row[key]
      }
      return Object.values(row)[0]
    },
    searchJiaYi(type) {
      const text = type === 'A' ? this.searchTextA : this.searchTextB
      if (!text) {
        this.$message.warning('Enter text to search')
        return
      }
      // Clear previous details when searching
      if (type === 'A') {
        this.jiaYiDetailsA = null
        // Clear form field to allow new selection
        this.form.jia_yi_fang_a = null
      } else {
        this.jiaYiDetailsB = null
        // Clear form field to allow new selection
        this.form.jia_yi_fang_b = null
      }
      this.formLoading = true
      request({
        url: '/api/v1/jia_yi_info',
        method: 'get',
        params: { q: text }
      }, 'shwethe_guiling').then(res => {
        const list = Array.isArray(res) ? res : (res.data || [])
        if (type === 'A') {
          this.jiaYiResultsA = list
          this.jiaYiColumnKeysA = list.length ? Object.keys(list[0]) : []
        } else {
          this.jiaYiResultsB = list
          this.jiaYiColumnKeysB = list.length ? Object.keys(list[0]) : []
        }
        if (list.length === 0) this.$message.info('No results found')
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.formLoading = false
      })
    },
    selectJiaYi(type, row) {
      const id = Number(this.getIdFromRow(row)) || 0
      if (type === 'A') {
        this.form.jia_yi_fang_a = id
        this.jiaYiDetailsA = row
        // Store jia_yi_fang_a details in details_json
        this.form.details_json = {
          ...this.form.details_json,
          jia_yi_fang_a: row
        }
        this.jiaYiResultsA = []
      } else {
        this.form.jia_yi_fang_b = id
        this.jiaYiDetailsB = row
        // Store jia_yi_fang_b details in details_json
        this.form.details_json = {
          ...this.form.details_json,
          jia_yi_fang_b: row
        }
        this.jiaYiResultsB = []
      }
      this.$nextTick(() => {
        this.$refs.form.validateField(type === 'A' ? 'jia_yi_fang_a' : 'jia_yi_fang_b')
      })
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
          const token = localStorage.getItem('auth-token') || ''
          const payload = this.decodeToken(token)
          const uId = payload.user_id || payload.sub || 0

          const postData = {
            ...this.form,
            type_edit: 'update',
            u_id: uId
          }

          // ensure numeric fields are numbers
          const numericKeys = ['product_id', 'product_qty', 'product_price_a', 'product_price_b', 'ke_bian', 'jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'lei_b', 'bu_bian', 'bi_zhi', 'che_liang', 'u_id']
          numericKeys.forEach(k => {
            postData[k] = Number(postData[k]) || 0
          })

          console.log('POST payload', postData)

          request({
            url: '/api/v1/form_insert',
            method: 'post',
            data: postData,
            headers: {
              accept: 'application/json',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`
            }
          }, 'shwethe_guiling').then(() => {
            this.$message.success('Updated successfully!')
            this.dialogVisible = false
            this.fetchData() // Refresh the table
          }).catch(err => {
            console.error(err)
            if (err.response) {
              console.error('Server response', err.response.data)
              this.$message.error(err.response.data.message || 'Update failed')
            } else {
              this.$message.error('Update failed')
            }
          }).finally(() => {
            this.submitLoading = false
          })
        } else {
          this.$message.error('Please fix validation errors.')
        }
      })
    },
    decodeToken(token) {
      try {
        const base64 = token.split('.')[1].replace(/-/g, '+').replace(/_/g, '/')
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        }).join(''))
        return JSON.parse(jsonPayload)
      } catch (e) {
        return {}
      }
    },
    fetchData() {
      this.loading = true
      console.log('Fetching data with pagination:', this.listQuery)

      request({
        url: `/api/v1/form_insert/latest/paginated?page=${this.listQuery.page}&limit=${this.listQuery.limit}`,
        method: 'get'
      }, 'shwethe_guiling')
        .then(res => {
          console.log('API Response received:', res)
          // Handle different response structures
          let responseData, rawRows, total

          if (res.data && Array.isArray(res.data)) {
            // New paginated structure: { data: [...], total: 43, page: 1, ... }
            responseData = res
            rawRows = res.data
            total = res.total
          } else if (Array.isArray(res.data)) {
            // Nested data structure
            responseData = res.data
            rawRows = res.data.data || res.data
            total = res.data.total || 0
          } else if (Array.isArray(res)) {
            // Direct array response (old structure)
            responseData = res
            rawRows = res
            total = res.length
          } else {
            // Fallback
            responseData = res
            rawRows = res.data || []
            total = res.total || 0
          }

          // Update pagination info
          this.total = total

          // Build processed rows with details_* fields for every entry in details_json
          this.rows = rawRows.map(r => this.buildRowWithDetails(r))
        })
        .catch(err => {
          console.error('Paginated API Error:', err)
          console.error('Error response:', err.response)

          // Fallback to old endpoint if paginated fails
          console.log('Falling back to old endpoint...')
          return request({
            url: '/api/v1/form_insert/latest',
            method: 'get'
          }, 'shwethe_guiling')
            .then(res => {
              console.log('Fallback API Response:', res)
              const rawRows = Array.isArray(res) ? res : (res.data || [])
              this.rows = rawRows.map(r => this.buildRowWithDetails(r))
              this.total = rawRows.length
            })
            .catch(fallbackErr => {
              console.error('Fallback API Error:', fallbackErr)
              this.$message.error('Failed to load data: ' + (err.message || 'Unknown error'))
            })
        })
        .finally(() => {
          this.loading = false
        })
    },
    buildRowWithDetails(r) {
      const row = { ...r }
      const details = r.details_json || {}

      // Process details_json fields
      Object.keys(details).forEach(k => {
        row[`details_${k}`] = this.getDetailsString(details[k])
      })

      // Make sure lei_a and lei_b names are available in the row
      if (details.lei_a) {
        row.lei_a_name = details.lei_a.name || row.lei_a
      }

      if (details.lei_b) {
        row.lei_b_name = details.lei_b.name || row.lei_b
      }

      return row
    },
    getDetailsString(obj) {
      if (!obj) return ''
      return Object.values(obj).join(' | ')
    },
    selectKeBian(row) {
      const id = Number(this.getIdFromRow(row)) || 0
      this.form.ke_bian = id
      this.keBianDetails = row
      // Store ke_bian details in details_json
      this.form.details_json = {
        ...this.form.details_json,
        ke_bian: row
      }
      this.keBianResults = []
      this.$nextTick(() => {
        this.$refs.form.validateField('ke_bian')
      })
    },
    searchKeBian() {
      if (!this.searchTextK) {
        this.$message.warning('Enter text to search')
        return
      }
      // Clear previous details when searching
      this.keBianDetails = null
      // Clear form field to allow new selection
      this.form.ke_bian = null
      this.formLoading = true
      request({
        url: '/api/v1/jia_yi_info',
        method: 'get',
        params: { q: this.searchTextK }
      }, 'shwethe_guiling').then(res => {
        const list = Array.isArray(res) ? res : (res.data || [])
        this.keBianResults = list
        this.keBianColumnKeys = list.length ? Object.keys(list[0]) : []
        if (list.length === 0) this.$message.info('No results found')
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.formLoading = false
      })
    },
    searchCheLiang() {
      if (!this.searchTextC) {
        this.$message.warning('Enter text to search')
        return
      }
      // Clear previous details when searching
      this.cheLiangDetails = null
      // Clear form field to allow new selection
      this.form.che_liang = null
      this.formLoading = true
      request({
        url: '/api/v1/jia_yi_info',
        method: 'get',
        params: { q: this.searchTextC }
      }, 'shwethe_guiling').then(res => {
        const list = Array.isArray(res) ? res : (res.data || [])
        this.cheLiangResults = list
        this.cheLiangColumnKeys = list.length ? Object.keys(list[0]) : []
        if (list.length === 0) this.$message.info('No results found')
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.formLoading = false
      })
    },
    selectCheLiang(row) {
      const id = Number(this.getIdFromRow(row)) || 0
      this.form.che_liang = id
      this.cheLiangDetails = row
      // Store che_liang details in details_json
      this.form.details_json = {
        ...this.form.details_json,
        che_liang: row
      }
      this.cheLiangResults = []
      this.$nextTick(() => {
        this.$refs.form.validateField('che_liang')
      })
    },
    getNowDateTimeString() {
      const d = new Date()
      const pad = n => (n < 10 ? '0' + n : n)
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
    },
    setNowDateTime() {
      this.form.riqi_datetime = this.getNowDateTimeString()
      this.formEdited = true
    },
    getProductCode(item) {
      // Try to get product code from details_json
      if (item.details_json && item.details_json.product && item.details_json.product.ProductIdname) {
        return item.details_json.product.ProductIdname
      }

      // Try to get from direct properties
      if (item.ProductIdname) return item.ProductIdname

      // Try to extract from details_product (second part after pipe)
      if (item.details_product && item.details_product.includes('|')) {
        const parts = item.details_product.split('|')
        if (parts.length >= 2) {
          return parts[1].trim()
        }
      }

      return 'N/A'
    },
    getDimensions(item) {
      if (item.details_product) {
        // Look for dimensions in the format like 7x3"
        const dimensionsMatch = item.details_product.match(/\d+x\d+["']?/g)
        return dimensionsMatch ? dimensionsMatch[0] : 'N/A'
      }
      return 'N/A'
    },
    formatDate(dateStr) {
      if (!dateStr) return 'N/A'
      try {
        const date = new Date(dateStr)
        return date.toLocaleDateString(undefined, {
          year: '2-digit',
          month: '2-digit',
          day: '2-digit'
        })
      } catch (e) {
        return dateStr.split(' ')[0] // Fallback to just the date part
      }
    },
    formatDetail(detail) {
      if (!detail) return 'N/A'

      if (typeof detail === 'string') {
        // If it's a string with pipe separators, format it nicely
        if (detail.includes('|')) {
          const parts = detail.split('|').map(p => p.trim())
          // Remove duplicates
          return [...new Set(parts)].join(' | ')
        }
        return detail
      } else if (typeof detail === 'object' && detail !== null) {
        // Extract the most important fields from the object
        const values = []

        // Prioritize ID and name fields
        if (detail.id) values.push(detail.id)
        if (detail.name) values.push(detail.name)
        if (detail.title) values.push(detail.title)

        // If we didn't find important fields, use all values
        if (values.length === 0) {
          Object.values(detail).forEach(v => {
            if (typeof v === 'string' || typeof v === 'number') {
              values.push(v)
            }
          })
        }

        // Remove duplicates
        return [...new Set(values)].join(' | ')
      }

      return 'N/A'
    },
    getProductDescription(item) {
      if (!item.details_product) return 'N/A'

      // If details_json.product has detailed information, use that
      if (item.details_json && item.details_json.product) {
        const product = item.details_json.product
        const details = []

        // Add ProductId first, then other product details
        if (product.ProductId) details.push(`${product.ProductId}`)
        if (product.ProductIdname) details.push(`${product.ProductIdname}`)
        if (product.ProductMmName) details.push(`${product.ProductMmName}`)
        if (product.ProductPrice) details.push(`${product.ProductPrice}`)

        return details.join(' | ')
      }

      // If no details_json, try to get ProductId from item.product_id and use details_product string
      if (item.product_id) {
        return `${item.product_id} | ${item.details_product}`
      }

      // If no details_json, use the details_product string
      return item.details_product
    },
    onLeiAChange(value) {
      const selectedLei = this.leiOptions.find(item => item.id === value)
      if (selectedLei) {
        this.form.details_json = {
          ...this.form.details_json,
          lei_a: selectedLei
        }
      }
    },
    onLeiBChange(value) {
      const selectedLei = this.leiOptions.find(item => item.id === value)
      if (selectedLei) {
        this.form.details_json = {
          ...this.form.details_json,
          lei_b: selectedLei
        }
      }
    },
    getLeiName(item, leiType) {
      // First check if we have a name field from buildRowWithDetails
      if (item[`${leiType}_name`]) {
        return item[`${leiType}_name`]
      }

      // Then check details_json
      if (item.details_json && item.details_json[leiType]) {
        const leiDetails = item.details_json[leiType]
        if (typeof leiDetails === 'object' && leiDetails !== null) {
          return leiDetails.name || leiDetails.id || 'N/A'
        }
      }

      // Fall back to the ID value
      return item[leiType] || 'N/A'
    },
    toggleExpand(id) {
      // Use Vue.set to ensure reactivity when adding new properties
      if (this.expandedCards[id]) {
        this.$set(this.expandedCards, id, false)
      } else {
        this.$set(this.expandedCards, id, true)
      }
    },
    onBiZhiChange(value) {
      const selected = this.biZhiOptions.find(item => item.id === value)
      if (selected) {
        this.form.details_json = {
          ...this.form.details_json,
          bi_zhi: selected
        }
      }
      this.markFormEdited()
    },
    markFormEdited() {
      this.formEdited = true
    },
    getBiZhiName(item) {
      // Attempt to get currency name from details_json first
      if (item.details_json && item.details_json.bi_zhi) {
        const bi = item.details_json.bi_zhi
        if (typeof bi === 'object' && bi !== null) {
          return bi.name || bi.id || 'N/A'
        }
      }
      // Fallback to separate field if backend later includes bi_zhi_name
      if (item.bi_zhi_name) return item.bi_zhi_name
      // Else fallback to raw ID
      return item.bi_zhi || 'N/A'
    },
    isToday(dateStr) {
      if (!dateStr) return false
      const d = new Date(dateStr)
      const now = new Date()
      return d.getFullYear() === now.getFullYear() && d.getMonth() === now.getMonth() && d.getDate() === now.getDate()
    },
    searchRecords() {
      this.searchLoading = true

      const cleanedSearchCriteria = {}
      for (const key of Object.keys(this.searchCriteria)) {
        const value = this.searchCriteria[key]
        cleanedSearchCriteria[key] = value === '' ? null : value
      }

      request({
        url: '/api/v1/form_insert/search',
        method: 'post',
        data: cleanedSearchCriteria
      }, 'shwethe_guiling').then(res => {
        const raw = Array.isArray(res) ? res : (res.data || [])
        this.searchRows = raw.map(r => this.buildRowWithDetails(r))
      }).catch(err => {
        console.error(err)
        this.$message.error('Search failed')
      }).finally(() => {
        this.searchLoading = false
      })
    },
    resetSearch() {
      // Clear product search
      this.searchProductText = ''
      this.selectedProduct = null

      // Clear JiaYi search fields
      this.searchKeBianText = ''
      this.selectedKeBian = null
      this.searchCheLiangText = ''
      this.selectedCheLiang = null
      this.searchJiaYiAText = ''
      this.selectedJiaYiA = null
      this.searchJiaYiBText = ''
      this.selectedJiaYiB = null

      this.searchCriteria = {
        bi_zhi: null,
        bu_bian: null,
        product_id: null,
        product_qty: null,
        product_price_a: null,
        product_price_b: null,
        ke_bian: null,
        jia_yi_fang_a: null,
        jia_yi_fang_b: null,
        lei_a: null,
        lei_b: null,
        shu_riqi_datetime: null,
        riqi_datetime: null,
        che_liang: null
      }
      this.searchRows = []
    },
    queryProductSearch(query, cb) {
      if (query) {
        request({
          url: '/api/v1/product_search',
          method: 'get',
          params: { q: query }
        }, 'shwethe_guiling').then(res => {
          // Handle both single object and array responses
          if (res && Array.isArray(res.data)) {
            // If response is an array of products
            const results = res.data.map(item => ({
              value: item.product_id || item.ProductId,
              label: item.product_id_name || item.ProductIdname,
              ...item
            }))
            cb(results)
          } else if (res && res.data) {
            // If response is a single product
            const item = res.data
            const results = [{
              value: item.product_id,
              label: item.product_id_name || item.ProductIdname,
              ...item
            }]
            cb(results)
          } else {
            cb([])
          }
        }).catch(err => {
          console.error(err)
          cb([])
        })
      } else {
        cb([])
      }
    },
    handleProductSelect(item) {
      this.selectedProduct = item
      this.searchProductText = item.product_id_name || item.ProductIdname
      // Set the product_id in searchCriteria for the actual search
      this.searchCriteria.product_id = item.product_id || item.ProductId
    },
    handleJiaYiSelect(item, field) {
      // Update the correct selected item based on field
      if (field === 'ke_bian') {
        this.selectedKeBian = item
        this.searchKeBianText = item.jia_yi_id_name
      } else if (field === 'che_liang') {
        this.selectedCheLiang = item
        this.searchCheLiangText = item.jia_yi_id_name
      } else if (field === 'jia_yi_fang_a') {
        this.selectedJiaYiA = item
        this.searchJiaYiAText = item.jia_yi_id_name
      } else if (field === 'jia_yi_fang_b') {
        this.selectedJiaYiB = item
        this.searchJiaYiBText = item.jia_yi_id_name
      }

      // Set the ID in searchCriteria for the actual search
      this.searchCriteria[field] = item.jia_yi_id
    },
    queryJiaYiSearch(query, cb) {
      if (query) {
        request({
          url: '/api/v1/jiayi_search',
          method: 'get',
          params: { q: query }
        }, 'shwethe_guiling').then(res => {
          // Handle both single object and array responses
          if (res && Array.isArray(res.data)) {
            // If response is an array of items
            const results = res.data.map(item => ({
              value: item.jia_yi_id,
              label: item.jia_yi_id_name,
              ...item
            }))
            cb(results)
          } else if (res && res.data) {
            // If response is a single item
            const item = res.data
            const results = [{
              value: item.jia_yi_id,
              label: item.jia_yi_id_name,
              ...item
            }]
            cb(results)
          } else {
            cb([])
          }
        }).catch(err => {
          console.error(err)
          cb([])
        })
      } else {
        cb([])
      }
    },
    handleSearchKeBianInput(value) {
      if (this.selectedKeBian && value !== this.selectedKeBian.jia_yi_id_name) {
        this.selectedKeBian = null
        this.searchCriteria.ke_bian = null
      } else if (!value) {
        this.selectedKeBian = null
        this.searchCriteria.ke_bian = null
      }
    },
    handleSearchCheLiangInput(value) {
      if (this.selectedCheLiang && value !== this.selectedCheLiang.jia_yi_id_name) {
        this.selectedCheLiang = null
        this.searchCriteria.che_liang = null
      } else if (!value) {
        this.selectedCheLiang = null
        this.searchCriteria.che_liang = null
      }
    },
    handleSearchJiaYiAInput(value) {
      if (this.selectedJiaYiA && value !== this.selectedJiaYiA.jia_yi_id_name) {
        this.selectedJiaYiA = null
        this.searchCriteria.jia_yi_fang_a = null
      } else if (!value) {
        this.selectedJiaYiA = null
        this.searchCriteria.jia_yi_fang_a = null
      }
    },
    handleSearchJiaYiBInput(value) {
      if (this.selectedJiaYiB && value !== this.selectedJiaYiB.jia_yi_id_name) {
        this.selectedJiaYiB = null
        this.searchCriteria.jia_yi_fang_b = null
      } else if (!value) {
        this.selectedJiaYiB = null
        this.searchCriteria.jia_yi_fang_b = null
      }
    },
    handleSearchProductInput(value) {
      if (this.selectedProduct && (value !== this.selectedProduct.product_id_name && value !== this.selectedProduct.ProductIdname)) {
        this.selectedProduct = null
        this.searchCriteria.product_id = null
      } else if (!value) {
        this.selectedProduct = null
        this.searchCriteria.product_id = null
      }
    },
    // Big Table Sync methods
    fetchBigTableData() {
      this.bigTableLoading = true

      // Make direct HTTP request to the external API
      // const url = 'http://192.168.1.11:8021/shwethe_guiling/api/v1/check_guiling'
      const url = 'http://pv-api.shwethe.com/shwethe_guiling/api/v1/check_guiling'

      fetch(url)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          return response.json()
        })
        .then(data => {
          console.log('Big Table API Response:', data)
          this.bigTableData = Array.isArray(data) ? data : []
        })
        .catch(err => {
          console.error('Failed to fetch Big Table data:', err)
          this.$message.error('Failed to load Big Table data')
          this.bigTableData = []
        })
        .finally(() => {
          this.bigTableLoading = false
        })
    },
    handleBigTableCardClick(item) {
      // Prevent clicking if already processing
      if (this.processingCardId === item.auto_id) {
        return
      }

      const date = item.riqi_datetime_formatted

      this.$confirm(
        `Send data for date ${date} to Big Table?`,
        'Confirm Sync',
        {
          confirmButtonText: 'Send',
          cancelButtonText: 'Cancel',
          type: 'info',
          confirmButtonClass: 'el-button--primary'
        }
      ).then(() => {
        // Show immediate feedback that process has started
        this.$message({
          message: 'Sending data to Big Table...',
          type: 'info',
          duration: 2000
        })
        this.sendToBigTable(item)
      }).catch(() => {
        // User cancelled
      })
    },
    sendToBigTable(item) {
      this.sendToBigTableLoading = true
      this.processingCardId = item.auto_id

      const payload = {
        riqi_datetime: item.riqi_datetime_formatted
      }

      // Make direct HTTP request to the external API
      // const url = 'http://192.168.1.11:8021/shwethe_guiling/api/v1/sendToBigTable'
      const url = 'http://pv-api.shwethe.com/shwethe_guiling/api/v1/sendToBigTable'

      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          return response.json()
        })
        .then(data => {
          console.log('Send to Big Table success:', data)
          this.$message.success(`Successfully sent data for ${item.riqi_datetime_formatted} to Big Table`)
          // Refresh the data
          this.fetchBigTableData()
        })
        .catch(err => {
          console.error('Failed to send to Big Table:', err)
          this.$message.error('Failed to send data to Big Table')
        })
        .finally(() => {
          this.sendToBigTableLoading = false
          this.processingCardId = null
        })
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.dag-button {
  background-color: #909399;
  border-color: #909399;
}

.dag-button:hover {
  background-color: #82848a;
  border-color: #82848a;
}

.update-dialog {
  --field-max-width: 420px;
}

.update-dialog .el-form-item__content > .el-input,
.update-dialog .el-form-item__content > .el-select,
.update-dialog .el-form-item__content > .el-date-editor {
  width: var(--field-max-width);
}

.update-dialog .el-form-item {
  margin-bottom: 18px;
}

/* List View Card Styles */
.el-card {
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.el-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
}

.compact-card {
  cursor: pointer;
  padding: 0;
}

.compact-card .el-card__body {
  padding: 12px;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.product-id {
  font-weight: 600;
  font-size: 15px;
  color: #303133;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-code {
  font-size: 14px;
  color: #409EFF;
  font-weight: 500;
}

.product-description-full {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.description-title {
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 4px;
}

.description-content {
  font-size: 13px;
  color: #303133;
  word-break: break-word;
  line-height: 1.4;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  padding: 6px 0;
  border-top: 1px dashed #f0f0f0;
  border-bottom: 1px dashed #f0f0f0;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #606266;
}

.meta-item i {
  margin-right: 4px;
  font-size: 14px;
}

.section-header {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
  margin: 12px 0 8px 0;
  display: flex;
  align-items: center;
  padding-bottom: 4px;
  border-bottom: 1px solid #ebeef5;
}

.section-header i {
  margin-right: 6px;
  color: #409EFF;
}

.product-details {
  margin-top: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px 12px;
}

.detail-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 4px;
  font-size: 12px;
  background-color: #f9f9f9;
  padding: 4px 6px;
  border-radius: 4px;
}

.detail-label {
  font-weight: 500;
  color: #909399;
  margin-bottom: 2px;
  font-size: 11px;
}

.detail-value {
  font-weight: 500;
  color: #303133;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

@media (max-width: 768px) {
  .product-details {
    grid-template-columns: 1fr;
  }
}

.flexible-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 1000px;
  margin: 0 auto;
}

.grid-card {
  width: 100%;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  cursor: pointer;
}

.grid-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.grid-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f5f7fa;
}

.record-id {
  font-weight: 600;
  font-size: 13px;
  color: #303133;
  background-color: #f5f7fa;
  padding: 2px 5px;
  border-radius: 3px;
}

.product-code {
  font-size: 12px;
  color: #409EFF;
  font-weight: 500;
}

.grid-card-content {
  padding: 8px 10px;
}

.grid-section {
  margin-bottom: 10px;
}

.grid-section .section-header {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
  margin: 12px 0 8px 0;
  display: flex;
  align-items: center;
  padding-bottom: 4px;
  border-bottom: 1px solid #ebeef5;
}

.grid-section .section-header i {
  margin-right: 6px;
  color: #409EFF;
}

.grid-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.grid-cell {
  flex: 1;
  min-width: 120px;
  background-color: #f9f9f9;
  padding: 4px 8px;
  border-radius: 3px;
}

.cell-label {
  font-weight: 500;
  color: #909399;
  margin-bottom: 2px;
  font-size: 10px;
}

.cell-value {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
}

.grid-card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px dashed #f0f0f0;
}

@media (max-width: 768px) {
  .grid-row {
    flex-direction: column;
  }

  .grid-cell {
    flex: 1 0 100%;
  }
}

/* Product Search Dropdown Styles */
.product-search-dropdown {
  width: auto !important;
  min-width: 400px;
  max-width: 600px;
  overflow-x: visible;
}

.product-suggestion-item {
  padding: 8px;
  cursor: pointer;
  white-space: normal;
  line-height: 1.4;
}

.product-suggestion-item:hover {
  background-color: #f5f7fa;
}

.product-suggestion-item .product-code {
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 4px;
  word-break: break-all;
}

.product-suggestion-item .product-name {
  font-size: 12px;
  color: #303133;
  word-break: break-all;
}

.product-suggestion-item .product-desc {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
  word-break: break-all;
}

/* Big Table Sync Styles */
.big-table-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.big-table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.big-table-card.processing {
  pointer-events: none;
  opacity: 0.8;
}

.sync-status {
  font-size: 12px;
  color: #67C23A;
  font-weight: 500;
  background-color: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #67C23A;
  transition: all 0.3s ease;
}

.sync-status.processing {
  color: #E6A23C;
  background-color: #fdf6ec;
  border-color: #E6A23C;
}

.big-table-date {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.big-table-date i {
  margin-right: 8px;
  color: #409EFF;
  font-size: 18px;
}

.big-table-timestamp {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.timestamp-label {
  color: #909399;
  margin-bottom: 2px;
}

.timestamp-value {
  color: #606266;
  font-family: monospace;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}
</style>

<style>
/* Global styles for autocomplete dropdown */
.el-autocomplete-suggestion {
  min-width: 400px !important;
}

.el-autocomplete-suggestion__list {
  max-height: 400px !important;
}

.expanded-dropdown {
  min-width: 500px !important;
  max-width: 800px !important;
}

.expanded-dropdown .el-autocomplete-suggestion__list {
  max-height: 500px !important;
}

.expanded-dropdown .product-suggestion-item {
  padding: 10px;
}

.expanded-dropdown .product-code {
  font-size: 14px;
  margin-bottom: 6px;
}

.expanded-dropdown .product-name {
  font-size: 13px;
  margin-bottom: 4px;
}

.expanded-dropdown .product-desc {
  font-size: 12px;
}
</style>
