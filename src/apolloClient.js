import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client/core'
import { setContext } from '@apollo/client/link/context'

// 创建第一个 HTTP 链接
const httpLink1 = createHttpLink({
  uri: process.env.VUE_APP_GRAPHQL_URI || 'http://192.168.1.11:7101/graphql'
  // uri: process.env.VUE_APP_GRAPHQL_URI || 'http://pv-api-graphql.shwethe.com/graphql'

})

const goodsLink = createHttpLink({
  // uri: process.env.VUE_APP_GOODS_URI || 'http://192.168.1.11:7101/goods/graphql'
  uri: process.env.VUE_APP_GOODS_URI || 'http://pv-api-graphql.shwethe.com/goods/graphql'
})

const infoLink = createHttpLink({
  // uri: process.env.VUE_APP_INFO_URI || 'http://192.168.1.11:7101/shwethe_n_info/graphql'
  uri: process.env.VUE_APP_GOODS_URI || 'http://pv-api-graphql.shwethe.com/shwethe_n_info/graphql'
})

const wlLink = createHttpLink({
  uri: process.env.VUE_APP_USER_URI || 'http://192.168.1.11:7400/shwethe_wl/graphql'
  // uri: process.env.VUE_APP_USER_URI || 'http://pv-api-graphql.shwethe.com/user/graphql'
})

const companyLink = createHttpLink({
  uri: process.env.VUE_APP_USER_URI || 'http://192.168.1.11:7101/shwethe_company/graphql'
  // uri: process.env.VUE_APP_USER_URI || 'http://pv-api-graphql.shwethe.com/user/graphql'
})

// 创建第二个 HTTP 链接
const httpLink2 = createHttpLink({
  uri: 'https://graphql-endpoint-2.com/graphql' // 第二个 GraphQL 端点
})

const staticsLink = createHttpLink({
  // uri: process.env.VUE_APP_USER_URI || 'http://192.168.1.11:7101/statics/graphql'
  uri: process.env.VUE_APP_USER_URI || 'http://pv-api-graphql.shwethe.com/statics/graphql'
})

const auth_url = createHttpLink({
  // uri: 'http://47.236.155.72:8083/auth/query'
  uri: 'http://shwethe-pb-ingress-g.shwethe.com/auth/query'

})

const shwethe_data = createHttpLink({
  uri: 'http://shwethe-pb-ingress-g.shwethe.com/shwethe_data/query'
})

const deliveryGoodsSystem = createHttpLink({
  // uri: 'http://47.236.155.72:8085/delivery_goods_system/query'
  uri: 'http://shwethe-pb-ingress-g.shwethe.com/delivery_goods_system/query'
})

// 设置认证上下文
const authLink = setContext((_, { headers }) => {
  // 从本地存储中获取认证 token
  const token = localStorage.getItem('auth-token')
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : ''
    }
  }
})

// 创建第一个 Apollo Client
const apolloClient1 = new ApolloClient({
  link: authLink.concat(httpLink1),
  cache: new InMemoryCache()
})

// 创建第二个 Apollo Client
const apolloClient2 = new ApolloClient({
  link: authLink.concat(httpLink2),
  cache: new InMemoryCache()
})

const goodsClient = new ApolloClient({
  link: authLink.concat(goodsLink),
  cache: new InMemoryCache()
})

const infoClient = new ApolloClient({
  link: authLink.concat(infoLink),
  cache: new InMemoryCache()
})

const wlClient = new ApolloClient({
  link: authLink.concat(wlLink),
  cache: new InMemoryCache()
})

const companyClient = new ApolloClient({
  link: authLink.concat(companyLink),
  cache: new InMemoryCache()
})

const staticsClient = new ApolloClient({
  link: authLink.concat(staticsLink),
  cache: new InMemoryCache()
})

const auth_url_client = new ApolloClient({
  link: authLink.concat(auth_url),
  cache: new InMemoryCache()
})

const shwethe_data_client = new ApolloClient({
  link: authLink.concat(shwethe_data),
  cache: new InMemoryCache()
})

const deliveryGoodsSystemClient = new ApolloClient({
  link: authLink.concat(deliveryGoodsSystem),
  cache: new InMemoryCache()
})

export { apolloClient1, apolloClient2, goodsClient, infoClient, wlClient, companyClient, staticsClient, auth_url_client, shwethe_data_client, deliveryGoodsSystemClient }
