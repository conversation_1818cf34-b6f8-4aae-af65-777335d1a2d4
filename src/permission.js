import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/auth-redirect'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()
  console.log('权限验证 - hasToken:', hasToken)

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      console.log('已登录，重定向到首页')
      next({ path: '/' })
      NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
    } else {
      // determine whether the user has obtained his permission roles through getInfo
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      console.log('权限验证 - hasRoles:', hasRoles, 'roles:', store.getters.roles)

      if (hasRoles) {
        console.log('已有角色信息，直接通过')
        next()
      } else {
        try {
          console.log('获取用户信息...')
          // get user info
          // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']
          const { roles } = await store.dispatch('user/getInfo')
          console.log('获取到的角色:', roles)

          // generate accessible routes map based on roles
          console.log('生成动态路由...')
          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
          console.log('生成的路由:', accessRoutes)

          // dynamically add accessible routes
          router.addRoutes(accessRoutes)
          console.log('动态路由已添加，准备跳转到:', to.path)

          // hack method to ensure that addRoutes is complete
          // set the replace: true, so the navigation will not leave a history record
          next({ ...to, replace: true })
        } catch (error) {
          console.error('权限验证出错:', error)
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/
    console.log('无token')

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      console.log('白名单路径，直接通过')
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      console.log('无权限，重定向到登录页')
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
