<template>
  <div class="vehicle-status-manager">
    <!-- 状态显示模式 -->
    <div v-if="!editMode" class="status-display">
      <el-tag
        :type="getStatusTagType(currentStatus)"
        :color="getStatusColor(currentStatus)"
        :style="{ color: 'white' }"
        @click="enableEdit"
        style="cursor: pointer"
      >
        <i :class="`el-icon-${getStatusIcon(currentStatus)}`"></i>
        {{ getStatusName(currentStatus) }}
      </el-tag>
      <div v-if="remark" class="status-remark">
        {{ remark }}
      </div>
    </div>

    <!-- 快速编辑模式 -->
    <div v-else class="status-edit">
      <el-select
        v-model="selectedStatus"
        size="small"
        style="width: 120px"
        @change="handleStatusChange"
        @blur="disableEdit"
      >
        <el-option
          v-for="option in statusOptions"
          :key="option.status"
          :label="getStatusName(option.status)"
          :value="option.status"
        />
      </el-select>
      <el-button
        size="mini"
        type="success"
        icon="el-icon-check"
        @click="saveStatus"
        :loading="saving"
      ></el-button>
      <el-button
        size="mini"
        type="info"
        icon="el-icon-close"
        @click="cancelEdit"
      ></el-button>
    </div>

          <!-- 状态更新对话框 -->
      <el-dialog
        :title="$t('vehicle.dialog.updateStatus')"
        :visible.sync="dialogVisible"
        width="400px"
        @close="resetForm"
      >
        <el-form ref="statusForm" :model="statusForm" :rules="statusRules" label-width="80px">
          <el-form-item :label="$t('vehicle.status.new')" prop="status">
            <el-select v-model="statusForm.status" :placeholder="$t('vehicle.status.selectNewStatus')">
              <el-option
                v-for="option in statusOptions"
                :key="option.status"
                :label="getStatusName(option.status)"
                :value="option.status"
              >
                <span style="float: left">{{ getStatusName(option.status) }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ option.description }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('vehicle.status.remark')">
            <el-input
              v-model="statusForm.status_remark"
              type="textarea"
              :rows="3"
              :placeholder="$t('vehicle.status.enterRemark')"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('vehicle.common.cancel') }}</el-button>
          <el-button type="primary" @click="submitStatusUpdate" :loading="saving">{{ $t('vehicle.common.confirm') }}</el-button>
        </div>
      </el-dialog>
  </div>
</template>

<script>
import { getVehicleStatusOptions, updateVehicleStatus } from '@/api/vehicle'

export default {
  name: 'VehicleStatusManager',
  props: {
    vehicleId: {
      type: [String, Number],
      required: true
    },
    currentStatus: {
      type: String,
      required: true
    },
    remark: {
      type: String,
      default: ''
    },
    quickEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editMode: false,
      selectedStatus: '',
      statusOptions: [],
      saving: false,
      dialogVisible: false,
      statusForm: {
        status: '',
        status_remark: ''
      },
      statusRules: {
        status: [
          { required: true, message: this.$t('vehicle.form.statusRequired'), trigger: 'change' }
        ]
      }
    }
  },
  async created() {
    await this.getStatusOptions()
    this.selectedStatus = this.currentStatus
  },
  methods: {
    async getStatusOptions() {
      try {
        const response = await getVehicleStatusOptions()
        this.statusOptions = response.data
      } catch (error) {
        console.error(this.$t('vehicle.message.getStatusOptionsError'), error)
      }
    },
    enableEdit() {
      if (this.quickEdit) {
        this.editMode = true
        this.selectedStatus = this.currentStatus
      } else {
        this.showDialog()
      }
    },
    disableEdit() {
      this.editMode = false
      this.selectedStatus = this.currentStatus
    },
    cancelEdit() {
      this.disableEdit()
    },
    showDialog() {
      this.statusForm = {
        status: this.currentStatus,
        status_remark: this.remark
      }
      this.dialogVisible = true
    },
    handleStatusChange(value) {
      this.selectedStatus = value
    },
    async saveStatus() {
      if (this.selectedStatus === this.currentStatus) {
        this.disableEdit()
        return
      }
      
      this.saving = true
      try {
        await updateVehicleStatus(this.vehicleId, {
          status: this.selectedStatus,
          status_remark: this.$t('vehicle.status.quickUpdate')
        })
        this.$message.success(this.$t('vehicle.status.updateSuccess'))
        this.$emit('status-updated', {
          vehicleId: this.vehicleId,
          newStatus: this.selectedStatus,
          remark: this.$t('vehicle.status.quickUpdate')
        })
        this.disableEdit()
      } catch (error) {
        this.$message.error(this.$t('vehicle.status.updateError') + ': ' + error.message)
        this.selectedStatus = this.currentStatus
      } finally {
        this.saving = false
      }
    },
    async submitStatusUpdate() {
      this.$refs.statusForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            await updateVehicleStatus(this.vehicleId, {
              status: this.statusForm.status,
              status_remark: this.statusForm.status_remark
            })
            this.$message.success(this.$t('vehicle.status.updateSuccess'))
            this.$emit('status-updated', {
              vehicleId: this.vehicleId,
              newStatus: this.statusForm.status,
              remark: this.statusForm.status_remark
            })
            this.dialogVisible = false
          } catch (error) {
            this.$message.error(this.$t('vehicle.status.updateError') + ': ' + error.message)
          } finally {
            this.saving = false
          }
        }
      })
    },
    resetForm() {
      this.$refs.statusForm && this.$refs.statusForm.resetFields()
    },
    getStatusColor(status) {
      const statusMap = {
        'active': '#52c41a',
        'inactive': '#faad14',
        'maintenance': '#1890ff',
        'parked': '#722ed1',
        'in_transit': '#13c2c2',
        'reserved': '#eb2f96',
        'scrapped': '#8c8c8c',
        'sold': '#52c41a',
        'lost': '#f5222d',
        'damaged': '#ff4d4f'
      }
      return statusMap[status] || '#8c8c8c'
    },
    getStatusName(status) {
      // 优先使用国际化翻译
      const i18nKey = `vehicle.status.${status}`
      if (this.$te(i18nKey)) {
        return this.$t(i18nKey)
      }
      // 如果没有国际化翻译，使用API返回的状态名
      const option = this.statusOptions.find(o => o.status === status)
      return option ? option.status_name : status
    },
    getStatusIcon(status) {
      const iconMap = {
        'active': 'check-circle',
        'inactive': 'pause-circle',
        'maintenance': 'tools',
        'parked': 'car',
        'in_transit': 'truck',
        'reserved': 'calendar',
        'scrapped': 'delete',
        'sold': 'money',
        'lost': 'warning',
        'damaged': 'close'
      }
      return iconMap[status] || 'info'
    },
    getStatusTagType(status) {
      const typeMap = {
        'active': 'success',
        'inactive': 'warning',
        'maintenance': 'primary',
        'parked': '',
        'in_transit': 'info',
        'reserved': 'danger',
        'scrapped': 'info',
        'sold': 'success',
        'lost': 'danger',
        'damaged': 'danger'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-status-manager {
  .status-display {
    .status-remark {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
  
  .status-edit {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style> 