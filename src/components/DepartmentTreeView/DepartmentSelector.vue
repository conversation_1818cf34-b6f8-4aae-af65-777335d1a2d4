<template>
  <el-dialog
    title="选择部门"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div class="department-selector">
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索部门名称或编码"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button size="small" @click="expandAll">展开所有</el-button>
        <el-button size="small" @click="collapseAll">收起所有</el-button>
        <el-button size="small" @click="clearSelection">清空选择</el-button>
      </div>

      <!-- 树形视图 -->
      <div class="tree-container">
        <department-tree-view
          ref="treeView"
          :data="filteredData"
          :show-checkbox="multiple"
          :multiple="multiple"
          :default-expand-all="false"
          :default-expanded-keys="defaultExpandedKeys"
          @change="handleSelectionChange"
        />
      </div>

      <!-- 选择结果预览 -->
      <div v-if="selectedDepartments.length > 0" class="selection-preview">
        <el-divider>已选择 {{ selectedDepartments.length }} 个部门</el-divider>
        <div class="preview-list">
          <el-tag
            v-for="dept in selectedDepartments"
            :key="dept.id"
            type="success"
            closable
            style="margin-right: 8px; margin-bottom: 8px;"
            @close="handleRemoveFromSelection(dept.id)"
          >
            {{ dept.dept_name }} ({{ dept.dept_code }})
          </el-tag>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import DepartmentTreeView from './index.vue'

export default {
  name: 'DepartmentSelector',
  components: {
    DepartmentTreeView
  },
  props: {
    // 对话框是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 部门数据
    data: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 已选择的部门
    value: {
      type: [Array, Object],
      default: () => null
    },
    // 默认展开的节点
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: '',
      selectedDepartments: [],
      filteredData: []
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal
        if (newVal) {
          this.initializeSelection()
        }
      },
      immediate: true
    },
    data: {
      handler(newData) {
        this.filteredData = newData
      },
      immediate: true,
      deep: true
    },
    value: {
      handler(newVal) {
        this.initializeSelection()
      },
      immediate: true
    }
  },
  methods: {
    // 初始化选择状态
    initializeSelection() {
      if (this.value) {
        if (this.multiple) {
          this.selectedDepartments = Array.isArray(this.value) ? [...this.value] : [this.value]
        } else {
          this.selectedDepartments = [this.value]
        }
      } else {
        this.selectedDepartments = []
      }
    },

    // 搜索部门
    handleSearch() {
      if (!this.searchKeyword) {
        this.filteredData = this.data
        return
      }

      const keyword = this.searchKeyword.toLowerCase()
      this.filteredData = this.filterTreeData(this.data, keyword)
    },

    // 过滤树形数据
    filterTreeData(data, keyword) {
      if (!data || !Array.isArray(data)) return []

      return data.filter(item => {
        // 检查当前节点是否匹配
        const isMatch = item.dept_name.toLowerCase().includes(keyword) ||
                       item.dept_code.toLowerCase().includes(keyword)

        // 递归过滤子节点
        const filteredChildren = this.filterTreeData(item.children || [], keyword)

        // 如果当前节点匹配或有匹配的子节点，则保留
        if (isMatch || filteredChildren.length > 0) {
          return {
            ...item,
            children: filteredChildren
          }
        }

        return false
      }).filter(Boolean)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      if (this.multiple) {
        this.selectedDepartments = Array.isArray(selection) ? selection : []
      } else {
        this.selectedDepartments = selection ? [selection] : []
      }
    },

    // 从选择中移除部门
    handleRemoveFromSelection(deptId) {
      this.selectedDepartments = this.selectedDepartments.filter(dept => dept.id !== deptId)

      // 同步更新树形视图的选择状态
      if (this.$refs.treeView) {
        this.$refs.treeView.setSelectedNodes(this.selectedDepartments)
      }
    },

    // 展开所有节点
    expandAll() {
      if (this.$refs.treeView) {
        this.$refs.treeView.expandAll()
      }
    },

    // 收起所有节点
    collapseAll() {
      if (this.$refs.treeView) {
        this.$refs.treeView.collapseAll()
      }
    },

    // 清空选择
    clearSelection() {
      this.selectedDepartments = []
      if (this.$refs.treeView) {
        this.$refs.treeView.clearSelection()
      }
    },

    // 确认选择
    handleConfirm() {
      if (this.multiple) {
        this.$emit('confirm', this.selectedDepartments)
      } else {
        this.$emit('confirm', this.selectedDepartments.length > 0 ? this.selectedDepartments[0] : null)
      }
      this.handleClose()
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.department-selector {
  max-height: 500px;
  overflow-y: auto;
}

.search-box {
  margin-bottom: 15px;
}

.action-buttons {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.tree-container {
  min-height: 300px;
  max-height: 350px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.selection-preview {
  margin-top: 15px;
}

.preview-list {
  max-height: 100px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

/* 滚动条样式 */
.tree-container::-webkit-scrollbar,
.preview-list::-webkit-scrollbar {
  width: 6px;
}

.tree-container::-webkit-scrollbar-track,
.preview-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb,
.preview-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb:hover,
.preview-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
