# 部门树形视图组件 (DepartmentTreeView)

## 概述

基于Element UI的Tree组件封装的部门树形视图组件，支持单选、多选、搜索等功能。适用于展示具有层级关系的部门数据结构。

## 组件结构

```
src/components/DepartmentTreeView/
├── index.vue                 # 基础树形视图组件
├── DepartmentSelector.vue    # 部门选择器对话框组件
└── README.md                 # 文档说明
```

## 数据格式

组件支持以下数据格式：

```json
[
  {
    "id": 4,
    "dept_code": "001",
    "dept_name": "အရောင်းဌာန",
    "parent_id": null,
    "description": "",
    "created_at": "2025-07-12T15:48:36.321889+08:00",
    "updated_at": "2025-07-12T15:48:36.321889+08:00",
    "children": [
      {
        "id": 5,
        "dept_code": "0001",
        "dept_name": "လက်လီအရောင်းအဖွဲ့",
        "parent_id": 4,
        "description": "",
        "created_at": "2025-07-12T15:59:39.35837+08:00",
        "updated_at": "2025-07-12T17:10:20.850312+08:00"
      }
    ]
  }
]
```

## 基础树形视图组件 (index.vue)

### 使用方法

```vue
<template>
  <department-tree-view
    :data="treeData"
    :show-checkbox="true"
    :multiple="true"
    :default-expand-all="true"
    @node-click="handleNodeClick"
    @change="handleChange"
  />
</template>

<script>
import DepartmentTreeView from '@/components/DepartmentTreeView/index.vue'

export default {
  components: {
    DepartmentTreeView
  },
  data() {
    return {
      treeData: [
        // 您的部门数据
      ]
    }
  },
  methods: {
    handleNodeClick(data, node) {
      console.log('节点点击:', data)
    },
    handleChange(selection) {
      console.log('选择变化:', selection)
    }
  }
}
</script>
```

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|-------|
| data | 树形数据 | Array | [] |
| showCheckbox | 是否显示复选框 | Boolean | false |
| checkOnClickNode | 点击节点是否选中 | Boolean | false |
| defaultExpandAll | 是否默认展开所有节点 | Boolean | true |
| defaultCheckedKeys | 默认选中的节点键值 | Array | [] |
| defaultExpandedKeys | 默认展开的节点键值 | Array | [] |
| nodeKey | 节点唯一标识 | String | 'id' |
| multiple | 是否多选 | Boolean | false |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| node-click | 节点点击事件 | (data, node) |
| check-change | 复选框状态变化事件 | (data, checked, indeterminate) |
| check | 复选框选中事件 | (data, checkedData) |
| change | 选择变化事件 | selection |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| getSelectedNodes | 获取选中的节点 | - |
| setSelectedNodes | 设置选中的节点 | nodes |
| clearSelection | 清空选择 | - |
| expandAll | 展开所有节点 | - |
| collapseAll | 收起所有节点 | - |

## 部门选择器对话框组件 (DepartmentSelector.vue)

### 使用方法

```vue
<template>
  <div>
    <el-button @click="openSelector">选择部门</el-button>
    
    <department-selector
      :visible.sync="selectorVisible"
      :data="treeData"
      :multiple="false"
      :value="selectedDepartment"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script>
import DepartmentSelector from '@/components/DepartmentTreeView/DepartmentSelector.vue'

export default {
  components: {
    DepartmentSelector
  },
  data() {
    return {
      selectorVisible: false,
      treeData: [
        // 您的部门数据
      ],
      selectedDepartment: null
    }
  },
  methods: {
    openSelector() {
      this.selectorVisible = true
    },
    handleConfirm(selection) {
      this.selectedDepartment = selection
      console.log('选择的部门:', selection)
    }
  }
}
</script>
```

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|-------|
| visible | 对话框是否可见 | Boolean | false |
| data | 部门数据 | Array | [] |
| multiple | 是否多选 | Boolean | false |
| value | 已选择的部门 | Array/Object | null |
| defaultExpandedKeys | 默认展开的节点 | Array | [] |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:visible | 对话框显示状态变化 | visible |
| confirm | 确认选择 | selection |
| close | 对话框关闭 | - |

## 在岗位管理中的集成

### 1. 在岗位表单中添加树形选择器

```vue
<el-form-item label="部门" prop="department_id">
  <div style="display: flex; align-items: center; gap: 10px;">
    <el-select v-model="positionTemp.department_id" placeholder="请选择部门" style="flex: 1;">
      <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-button type="primary" icon="el-icon-s-grid" size="small" @click="openDepartmentSelector">
      树形选择
    </el-button>
  </div>
</el-form-item>
```

### 2. 添加部门选择器对话框

```vue
<department-selector
  :visible.sync="departmentSelectorVisible"
  :data="departmentTreeData"
  :multiple="false"
  @confirm="handleDepartmentSelected"
/>
```

### 3. 处理部门选择逻辑

```javascript
methods: {
  openDepartmentSelector() {
    this.departmentSelectorVisible = true
  },
  
  handleDepartmentSelected(selectedDepartment) {
    if (selectedDepartment) {
      this.positionTemp.department_id = selectedDepartment.id
      this.$message.success(`已选择部门：${selectedDepartment.dept_name}`)
    }
  }
}
```

## 演示页面

您可以访问演示页面查看组件的各种功能：

```
src/views/labor-project/position/TreeViewDemo.vue
```

演示页面包含：
- 基础树形视图演示
- 多选树形视图演示
- 选择器对话框演示
- 数据结构展示
- 操作日志记录

## 特性

### 1. 美观的UI设计
- 文件夹和文档图标区分
- 部门编码标签显示
- 子部门数量提示
- 选中状态高亮显示

### 2. 完整的交互功能
- 单选和多选模式
- 搜索过滤功能
- 展开/收起操作
- 已选择部门管理

### 3. 良好的用户体验
- 响应式设计
- 滚动条优化
- 加载状态提示
- 错误处理机制

### 4. 灵活的配置选项
- 可配置的默认展开状态
- 可配置的选择模式
- 可配置的节点键值
- 可配置的事件处理

## 样式定制

组件提供了丰富的CSS类名供样式定制：

```css
/* 树形节点样式 */
.department-tree-view >>> .el-tree-node__content {
  height: 40px;
  line-height: 40px;
}

/* 选中状态样式 */
.department-tree-view >>> .el-tree-node.is-current > .el-tree-node__content {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

/* 复选框样式 */
.department-tree-view >>> .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}
```

## 注意事项

1. **数据格式要求**：确保数据中包含必要的字段（id、dept_name、dept_code、children）
2. **性能优化**：对于大量数据，建议使用虚拟滚动或分页加载
3. **浏览器兼容性**：基于Element UI，支持现代浏览器
4. **响应式设计**：组件适配不同屏幕尺寸

## 版本更新

- **v1.0.0**：基础功能实现
  - 树形视图展示
  - 单选/多选模式
  - 搜索过滤功能
  - 选择器对话框
  - 岗位管理集成

## 技术栈

- **Vue.js 2.6+**
- **Element UI 2.13+**
- **JavaScript ES6+**
- **CSS3** 