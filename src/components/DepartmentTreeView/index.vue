<template>
  <div class="department-tree-view">
    <!-- 部门树形选择器 -->
    <el-tree
      :data="treeData"
      :props="defaultProps"
      :default-expand-all="defaultExpandAll"
      ref="tree"
      :expand-on-click-node="false"
      :check-on-click-node="checkOnClickNode"
      :show-checkbox="showCheckbox"
      :node-key="nodeKey"
      :default-checked-keys="defaultCheckedKeys"
      :default-expanded-keys="defaultExpandedKeys"
      :highlight-current="true"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
      @check="handleCheck"
    >
      <span slot-scope="{ node, data }" class="tree-node">
        <span class="tree-node-label">
          <i :class="getNodeIcon(data)" style="margin-right: 5px;" />
          <span>{{ data.dept_name }}</span>
        </span>
        <span class="tree-node-info">
          <el-tag size="mini" type="info">{{ data.dept_code }}</el-tag>
          <span v-if="data.children && data.children.length > 0" class="children-count">
            ({{ data.children.length }})
          </span>
        </span>
      </span>
    </el-tree>

    <!-- 已选择的部门信息 -->
    <div v-if="selectedDepartments.length > 0" class="selected-departments">
      <el-divider>已选择的部门</el-divider>
      <div class="selected-tags">
        <el-tag
          v-for="dept in selectedDepartments"
          :key="dept.id"
          closable
          style="margin-right: 8px; margin-bottom: 8px;"
          @close="handleRemoveSelected(dept.id)"
        >
          {{ dept.dept_name }} ({{ dept.dept_code }})
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DepartmentTreeView',
  props: {
    // 树形数据
    data: {
      type: Array,
      default: () => []
    },
    // 是否显示复选框
    showCheckbox: {
      type: Boolean,
      default: false
    },
    // 点击节点是否选中
    checkOnClickNode: {
      type: Boolean,
      default: false
    },
    // 是否默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default: true
    },
    // 默认选中的节点键值
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    // 默认展开的节点键值
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    // 节点唯一标识
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeData: [],
      selectedDepartments: [],
      defaultProps: {
        children: 'children',
        label: 'dept_name',
        value: 'id'
      }
    }
  },
  watch: {
    data: {
      handler(newData) {
        this.treeData = this.processTreeData(newData)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 处理树形数据，添加必要的属性
    processTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => ({
        ...item,
        // 确保每个节点都有唯一的key
        key: item.id,
        // 处理子节点
        children: item.children ? this.processTreeData(item.children) : []
      }))
    },

    // 获取节点图标
    getNodeIcon(data) {
      if (data.children && data.children.length > 0) {
        return 'el-icon-folder'
      } else {
        return 'el-icon-document'
      }
    },

    // 处理节点点击
    handleNodeClick(data, node) {
      if (this.multiple) {
        // 多选模式
        const index = this.selectedDepartments.findIndex(dept => dept.id === data.id)
        if (index === -1) {
          this.selectedDepartments.push(data)
        } else {
          this.selectedDepartments.splice(index, 1)
        }
      } else {
        // 单选模式
        this.selectedDepartments = [data]
      }

      this.$emit('node-click', data, node)
      this.$emit('change', this.multiple ? this.selectedDepartments : data)
    },

    // 处理复选框状态变化
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate)
    },

    // 处理复选框选中
    handleCheck(data, checkedData) {
      this.selectedDepartments = checkedData.checkedNodes.filter(node => node.id)
      this.$emit('check', data, checkedData)
      this.$emit('change', this.selectedDepartments)
    },

    // 移除已选择的部门
    handleRemoveSelected(deptId) {
      this.selectedDepartments = this.selectedDepartments.filter(dept => dept.id !== deptId)

      // 如果启用了复选框，同时取消树形控件中的选中状态
      if (this.showCheckbox) {
        this.$refs.tree.setChecked(deptId, false)
      }

      this.$emit('change', this.multiple ? this.selectedDepartments : null)
    },

    // 获取选中的节点
    getSelectedNodes() {
      return this.selectedDepartments
    },

    // 设置选中的节点
    setSelectedNodes(nodes) {
      this.selectedDepartments = nodes || []
      if (this.showCheckbox && this.$refs.tree) {
        const nodeIds = nodes.map(node => node.id)
        this.$refs.tree.setCheckedKeys(nodeIds)
      }
    },

    // 清空选择
    clearSelection() {
      this.selectedDepartments = []
      if (this.showCheckbox && this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([])
      }
      this.$emit('change', this.multiple ? [] : null)
    },

    // 展开所有节点
    expandAll() {
      this.$refs.tree.expandAll()
    },

    // 收起所有节点
    collapseAll() {
      this.$refs.tree.collapseAll()
    }
  }
}
</script>

<style scoped>
.department-tree-view {
  width: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 10px;
}

.tree-node-label {
  display: flex;
  align-items: center;
  flex: 1;
}

.tree-node-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.children-count {
  font-size: 12px;
  color: #909399;
}

.selected-departments {
  margin-top: 15px;
}

.selected-tags {
  max-height: 100px;
  overflow-y: auto;
}

/* 树形控件样式优化 */
.department-tree-view >>> .el-tree-node__content {
  height: 40px;
  line-height: 40px;
}

.department-tree-view >>> .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.department-tree-view >>> .el-tree-node__expand-icon {
  font-size: 16px;
}

.department-tree-view >>> .el-tree-node__label {
  font-size: 14px;
}

/* 选中状态样式 */
.department-tree-view >>> .el-tree-node.is-current > .el-tree-node__content {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

/* 复选框样式 */
.department-tree-view >>> .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}

/* 滚动条样式 */
.selected-tags::-webkit-scrollbar {
  width: 6px;
}

.selected-tags::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.selected-tags::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.selected-tags::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
