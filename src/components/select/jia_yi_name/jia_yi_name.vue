<template>
  <div class="jia-yi-name-select">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      :loading="loading"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :clearable="clearable"
      :multiple="multiple"
      :disabled="disabled"
      style="width: 100%"
      @change="handleChange"
      @clear="handleClear"
      @blur="handleBlur"
      @focus="handleFocus"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
      <div
        v-if="showLoadMore"
        class="load-more-btn"
        @click="loadMore"
      >
        <span>加载更多</span>
      </div>
    </el-select>
  </div>
</template>

<script>
import { searchJiayi, getJiayiById } from '@/api/shwethe_data/jiayi'

export default {
  name: 'JiaYiNameSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请搜索并选择...'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    pageSize: {
      type: Number,
      default: 10
    },
    // 最小搜索字符长度
    minSearchLength: {
      type: Number,
      default: 2
    },
    // 排序字段
    sortField: {
      type: String,
      default: ''
    },
    // 排序方式 (asc/desc)
    sortOrder: {
      type: String,
      default: 'asc'
    }
  },
  data() {
    return {
      loading: false,
      options: [],
      selectedValue: this.value,
      currentPage: 1,
      totalCount: 0,
      currentKeyword: '',
      showLoadMore: false
    }
  },
  watch: {
    value(newVal, oldVal) {
      console.log('🔄 Value changed:', { newVal, oldVal })
      this.selectedValue = newVal

      // 如果值发生变化，重新加载详细信息
      if (newVal !== oldVal) {
        if (newVal) {
          // 检查是否已经有对应的选项
          const existingOption = this.options.find(item => item.id === newVal)
          if (!existingOption) {
            console.log('🔍 需要加载新的详细信息:', newVal)
            this.loadItemById(newVal)
          }
        } else {
          // 值为空时，清空选项
          this.options = []
        }
      }
    },
    selectedValue(newVal) {
      this.$emit('input', newVal)
    }
  },
  created() {
    // 如果有初始值，加载对应的详细信息
    if (this.value) {
      this.loadItemById(this.value)
    }
  },
  methods: {
    // 根据ID加载详细信息（用于编辑模式）
    async loadItemById(id) {
      if (!id) return

      try {
        console.log('🔍 加载ID详细信息:', id)
        const response = await getJiayiById(id)
        console.log('✅ 根据ID获取详细信息响应:', response)

        if (response && response.data) {
          const item = response.data

          // 构建显示名称
          let displayName = ''
          if (item.jia_yi_id_name && item.jia_yi_mm_name) {
            displayName = `${item.jia_yi_id_name}|${item.jia_yi_mm_name}`
          } else if (item.jia_yi_id_name) {
            displayName = item.jia_yi_id_name
          } else if (item.jia_yi_mm_name) {
            displayName = item.jia_yi_mm_name
          } else {
            displayName = `ID: ${item.jia_yi_id || id}`
          }

          const formattedItem = {
            id: item.jia_yi_id || item.auto_id,
            name: displayName,
            jia_yi_id: item.jia_yi_id,
            auto_id: item.auto_id,
            jia_yi_id_name: item.jia_yi_id_name,
            jia_yi_mm_name: item.jia_yi_mm_name,
            ...item
          }

          // 将项目添加到选项中（确保编辑时能正确显示）
          this.options = [formattedItem]

          console.log('✅ 加载的项目:', formattedItem)
        }
      } catch (error) {
        console.error('❌ 根据ID加载详细信息失败:', error)
        // 失败时至少显示ID
        this.options = [{
          id: id,
          name: `ID: ${id}`,
          jia_yi_id: id
        }]
      }
    },

    // 远程搜索方法
    async remoteMethod(query) {
      if (query && query.length >= this.minSearchLength) {
        this.currentKeyword = query
        this.currentPage = 1
        // 保存当前选中的项目（如果有的话）
        const currentSelectedItem = this.options.find(item => item.id === this.selectedValue)
        this.options = []
        await this.fetchData(query, 1)

        // 如果有选中项且不在搜索结果中，保留它
        if (currentSelectedItem && !this.options.some(item => item.id === currentSelectedItem.id)) {
          this.options.unshift(currentSelectedItem)
        }
      } else {
        // 如果查询为空，保留当前选中的项目
        if (this.selectedValue) {
          const currentSelectedItem = this.options.find(item => item.id === this.selectedValue)
          this.options = currentSelectedItem ? [currentSelectedItem] : []
        } else {
          this.options = []
        }
        this.showLoadMore = false
      }
    },

    // 获取数据
    async fetchData(keyword, page = 1) {
      this.loading = true
      try {
        const response = await searchJiayi({
          keyword,
          page,
          page_size: this.pageSize,
          sort: this.sortField,
          order: this.sortOrder
        })

        console.log('🔍 搜索API响应:', response)

        // 处理实际的 API 响应数据结构
        let items = []
        let total = 0

        if (response && response.data && Array.isArray(response.data)) {
          // 实际的API响应格式: { data: [...], total: 100, page: 1, size: 10 }
          items = response.data
          total = response.total || 0
        } else if (response && Array.isArray(response)) {
          // 简单数组格式：[...]
          items = response
          total = response.length
        } else {
          console.warn('未知的数据格式:', response)
          items = []
          total = 0
        }

        if (items && items.length > 0) {
          // 映射实际的字段结构到组件期望的格式
          items = items.map(item => {
            // 构建显示名称：jia_yi_id_name|jia_yi_mm_name
            let displayName = ''
            if (item.jia_yi_id_name && item.jia_yi_mm_name) {
              displayName = `${item.jia_yi_id_name}|${item.jia_yi_mm_name}`
            } else if (item.jia_yi_id_name) {
              displayName = item.jia_yi_id_name
            } else if (item.jia_yi_mm_name) {
              displayName = item.jia_yi_mm_name
            } else {
              displayName = '未知'
            }

            return {
              id: item.jia_yi_id || item.auto_id, // 使用 jia_yi_id 作为主键，auto_id 作为备用
              name: displayName, // 使用组合后的显示名称
              jia_yi_id: item.jia_yi_id,
              auto_id: item.auto_id,
              jia_yi_id_name: item.jia_yi_id_name,
              jia_yi_mm_name: item.jia_yi_mm_name,
              ...item // 保留原始数据
            }
          })

          console.log('🔍 处理后的选项:', items)

          if (page === 1) {
            this.options = items
          } else {
            this.options = [...this.options, ...items]
          }

          this.totalCount = total
          this.currentPage = page

          // 判断是否显示"加载更多"
          this.showLoadMore = this.options.length < this.totalCount
        } else {
          if (page === 1) {
            this.options = []
          }
          this.showLoadMore = false
        }
      } catch (error) {
        console.error('搜索数据失败:', error)
        this.$message.error('搜索失败，请重试')
        this.options = []
        this.showLoadMore = false
      } finally {
        this.loading = false
      }
    },

    // 加载更多
    async loadMore() {
      if (this.currentKeyword && !this.loading && this.showLoadMore) {
        await this.fetchData(this.currentKeyword, this.currentPage + 1)
      }
    },

    // 处理选择变化
    handleChange(value) {
      this.$emit('change', value)

      // 如果需要返回完整的选中对象
      if (this.multiple) {
        const selectedObjects = this.options.filter(item =>
          value.includes(item.id)
        )
        this.$emit('select', selectedObjects)
      } else {
        const selectedObject = this.options.find(item => item.id === value)
        this.$emit('select', selectedObject)
      }
    },

    // 处理清空
    handleClear() {
      this.options = []
      this.showLoadMore = false
      this.$emit('clear')
    },

    // 处理失焦
    handleBlur() {
      this.$emit('blur')
    },

    // 处理聚焦
    handleFocus() {
      this.$emit('focus')
    },

    // 重置组件
    reset() {
      this.selectedValue = this.multiple ? [] : ''
      this.options = []
      this.currentKeyword = ''
      this.currentPage = 1
      this.showLoadMore = false
    },

    // 公共方法：根据ID设置值并加载详细信息
    async setValueById(id) {
      if (!id) {
        this.selectedValue = ''
        this.options = []
        return
      }

      this.selectedValue = id
      await this.loadItemById(id)
    },

    // 公共方法：获取当前选中的完整信息
    getSelectedItem() {
      return this.options.find(item => item.id === this.selectedValue)
    },

    // 公共方法：强制重新加载（用于重用组件时）
    async forceReload() {
      console.log('🔄 强制重新加载组件:', this.selectedValue)
      this.options = []
      this.currentKeyword = ''
      this.currentPage = 1
      this.showLoadMore = false

      if (this.selectedValue) {
        await this.loadItemById(this.selectedValue)
      }
    }
  }
}
</script>

<style scoped>
.jia-yi-name-select {
  width: 100%;
}

.load-more-btn {
  text-align: center;
  padding: 8px 0;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  border-top: 1px solid #e4e7ed;
  margin-top: 6px;
}

.load-more-btn:hover {
  background-color: #f5f7fa;
}

/* 下拉选项样式调整 */
.el-select-dropdown__item {
  padding: 8px 12px;
  line-height: 1.5;
}
</style>
