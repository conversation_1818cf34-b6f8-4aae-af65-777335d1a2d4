<template>
  <div class="example-container">
    <div class="example-section">
      <h3>基础用法</h3>
      <jia-yi-name-select
        v-model="selectedValue"
        placeholder="请输入关键词搜索..."
        @change="handleChange"
        @select="handleSelect"
      />
      <p>选中值: {{ selectedValue }}</p>
      <p>选中对象: {{ JSON.stringify(selectedObject) }}</p>
    </div>

    <div class="example-section">
      <h3>多选模式</h3>
      <jia-yi-name-select
        v-model="multipleValues"
        :multiple="true"
        placeholder="支持多选..."
        @change="handleMultipleChange"
        @select="handleMultipleSelect"
      />
      <p>选中值: {{ multipleValues }}</p>
      <p>选中对象: {{ JSON.stringify(multipleObjects) }}</p>
    </div>

    <div class="example-section">
      <h3>自定义配置</h3>
      <jia-yi-name-select
        v-model="customValue"
        :page-size="20"
        :min-search-length="1"
        :clearable="false"
        placeholder="自定义配置..."
        @change="handleCustomChange"
      />
      <p>选中值: {{ customValue }}</p>
    </div>

    <div class="example-section">
      <h3>操作按钮</h3>
      <el-button @click="resetAll">重置所有</el-button>
      <el-button @click="setDefault">设置默认值</el-button>
    </div>
  </div>
</template>

<script>
import JiaYiNameSelect from './jia_yi_name.vue'

export default {
  name: 'JiaYiNameExample',
  components: {
    JiaYiNameSelect
  },
  data() {
    return {
      selectedValue: '',
      selectedObject: null,
      multipleValues: [],
      multipleObjects: [],
      customValue: ''
    }
  },
  methods: {
    handleChange(value) {
      console.log('单选变化:', value)
    },

    handleSelect(obj) {
      console.log('单选对象:', obj)
      this.selectedObject = obj
    },

    handleMultipleChange(values) {
      console.log('多选变化:', values)
    },

    handleMultipleSelect(objects) {
      console.log('多选对象:', objects)
      this.multipleObjects = objects
    },

    handleCustomChange(value) {
      console.log('自定义变化:', value)
    },

    resetAll() {
      this.selectedValue = ''
      this.selectedObject = null
      this.multipleValues = []
      this.multipleObjects = []
      this.customValue = ''
    },

    setDefault() {
      // 这里可以设置一些默认值进行测试
      this.selectedValue = 'test-id-1'
      this.multipleValues = ['test-id-1', 'test-id-2']
    }
  }
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.example-section h3 {
  margin-top: 0;
  color: #303133;
  font-size: 18px;
}

.example-section p {
  margin: 10px 0;
  color: #606266;
  font-size: 14px;
}

.el-button {
  margin-right: 10px;
}
</style>
