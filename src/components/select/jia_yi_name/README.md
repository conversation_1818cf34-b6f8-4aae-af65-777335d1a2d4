# JiaYiNameSelect 组件

一个基于 Element UI 的可搜索下拉选择器组件，支持远程搜索和分页加载。

## 功能特点

- ✅ 远程搜索：输入关键词实时搜索
- ✅ 分页加载：支持加载更多数据
- ✅ 多选支持：可配置单选或多选模式
- ✅ 自定义配置：可配置 API 地址、分页大小等
- ✅ 事件回调：提供完整的事件回调机制
- ✅ 加载状态：显示加载中状态
- ✅ 错误处理：优雅处理网络错误

## 基础用法

```vue
<template>
  <jia-yi-name-select 
    v-model="selectedValue" 
    placeholder="请输入关键词搜索..."
    @change="handleChange"
    @select="handleSelect"
  />
</template>

<script>
import JiaYiNameSelect from '@/components/select/jia_yi_name/jia_yi_name.vue'

export default {
  components: {
    JiaYiNameSelect
  },
  data() {
    return {
      selectedValue: ''
    }
  },
  methods: {
    handleChange(value) {
      console.log('选中值:', value)
    },
    handleSelect(obj) {
      console.log('选中对象:', obj)
    }
  }
}
</script>
```

## 多选模式

```vue
<template>
  <jia-yi-name-select 
    v-model="multipleValues"
    :multiple="true"
    placeholder="支持多选..."
    @change="handleMultipleChange"
    @select="handleMultipleSelect"
  />
</template>

<script>
export default {
  data() {
    return {
      multipleValues: []
    }
  },
  methods: {
    handleMultipleChange(values) {
      console.log('多选值:', values)
    },
    handleMultipleSelect(objects) {
      console.log('多选对象:', objects)
    }
  }
}
</script>
```

## Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | String/Number/Array | `''` | 选中的值（v-model） |
| `placeholder` | String | `'请搜索并选择...'` | 占位文本 |
| `clearable` | Boolean | `true` | 是否可清空 |
| `multiple` | Boolean | `false` | 是否多选 |
| `disabled` | Boolean | `false` | 是否禁用 |
| `apiUrl` | String | `'http://localhost:8086/shwethe_data/api/jiayi/search'` | API 地址 |
| `pageSize` | Number | `10` | 每页数据量 |
| `minSearchLength` | Number | `2` | 最小搜索字符长度 |

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `input` | 选中值变化时触发 | `(value)` |
| `change` | 选中值变化时触发 | `(value)` |
| `select` | 选中项变化时触发 | `(object/objects)` |
| `clear` | 清空时触发 | `()` |
| `focus` | 获得焦点时触发 | `()` |
| `blur` | 失去焦点时触发 | `()` |

## Methods 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| `reset` | 重置组件状态 | `()` |

## API 数据格式

组件期望 API 返回以下格式的数据：

```json
{
  "items": [
    {
      "id": "1",
      "name": "选项1"
    },
    {
      "id": "2", 
      "name": "选项2"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 10
}
```

### 请求参数

- `keyword`: 搜索关键词
- `page`: 页码（从1开始）
- `page_size`: 每页数据量

### 响应字段

- `items`: 数据数组，每个对象必须包含 `id` 和 `name` 字段
- `total`: 总记录数
- `page`: 当前页码
- `page_size`: 每页数据量

## 自定义配置示例

```vue
<template>
  <jia-yi-name-select 
    v-model="customValue"
    :page-size="20"
    :min-search-length="1"
    :clearable="false"
    api-url="https://your-api-endpoint.com/search"
    placeholder="自定义配置..."
    @change="handleCustomChange"
  />
</template>
```

## 完整示例

参考 `example.vue` 文件中的完整使用示例。

## 注意事项

1. 组件依赖 Element UI，请确保项目已正确安装和配置
2. 需要 axios 库处理 HTTP 请求
3. API 必须支持 `keyword`、`page`、`page_size` 参数
4. 建议设置合理的 `minSearchLength` 以减少不必要的 API 调用
5. 多选模式下，`value` 应该是数组类型
6. 组件会自动处理加载状态和错误提示

## 错误处理

组件内置了错误处理机制：
- 网络请求失败时会显示错误提示
- 会在控制台输出详细错误信息
- 自动重置选项列表和加载状态

## 性能优化建议

1. 设置合适的 `minSearchLength` 减少 API 调用
2. 合理配置 `pageSize` 平衡性能和用户体验
3. 后端 API 应该实现适当的缓存机制
4. 考虑实现防抖(debounce)功能以进一步优化搜索性能 