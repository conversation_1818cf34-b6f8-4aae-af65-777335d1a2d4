<template>
  <el-tabs v-model="activeTab" type="border-card">
    <el-tab-pane label="လူမှုဖူလုံရေး" name="socialSecurity">
      <social-security
        :list-query="listQuery"
        @filter="handleFilter"
        @refresh="getList"
      />
    </el-tab-pane>
    <el-tab-pane label="အိမ်ရာရန်ပုံငွေ" name="housingFund" disabled>
      <!-- 后续可添加其他福利类型 -->
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import SocialSecurity from './SocialSecurity.vue'

export default {
  name: 'BenefitTabs',
  components: {
    SocialSecurity
  },
  props: {
    list: Array,
    total: Number,
    loading: Boolean
  },
  data() {
    return {
      activeTab: 'socialSecurity',
      listQuery: {
        page: 1,
        limit: 20,
        employeeName: undefined,
        workActive: undefined
      }
    }
  },
  methods: {
    handleFilter() {
      this.$emit('filter', this.listQuery)
    },
    getList() {
      this.$emit('refresh')
    }
  }
}
</script>
