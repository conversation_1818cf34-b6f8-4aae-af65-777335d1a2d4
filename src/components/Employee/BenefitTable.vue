<template>
  <el-table
    v-loading="loading"
    :data="data"
    border
    fit
    highlight-current-row
    style="width: 100%;"
  >
    <!-- 列定义 -->
    <el-table-column label="ID" prop="id" width="80" align="center">
      <template #default="{ row }">{{ row.id }}</template>
    </el-table-column>

    <el-table-column label="ဝန်ထမ်းအမည်" min-width="120" align="center">
      <template #default="{ row }">{{ row.employeeName }}</template>
    </el-table-column>

    <!-- 操作列 -->
    <el-table-column label="လုပ်ဆောင်ချက်" align="center" width="230">
      <template #default="{ row }">
        <el-button
          type="primary"
          size="mini"
          @click="$emit('create', row)"
        >
          {{ row.social_security ? 'ပြင်ဆင်ရန်' : 'အသစ်ထည့်ရန်' }}
        </el-button>
        <el-button
          v-if="row.social_security"
          type="danger"
          size="mini"
          @click="$emit('delete', row)"
        >
          ဖျက်ရန်
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'BenefitTable',
  props: {
    data: Array,
    loading: Boolean
  }
}
</script>
