<template>
  <el-dialog
    :title="textMap[status]"
    :visible.sync="visible"
    width="40%"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <!-- 表单字段 -->
      <el-form-item label="ဝန်ထမ်းအမည်">
        <el-input v-model="formData.employeeName" disabled />
      </el-form-item>

      <el-form-item label="ငွေပမာဏ" prop="amount" required>
        <el-input-number
          v-model="formData.amount"
          :min="0"
          :precision="2"
        />
      </el-form-item>

      <el-form-item label="စတင်သည့်ရက်စွဲ" prop="effectiveDate" required>
        <el-date-picker
          v-model="formData.effectiveDate"
          type="date"
          value-format="timestamp"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="$emit('close')">ပယ်ဖျက်ရန်</el-button>
      <el-button type="primary" @click="submitForm">အတည်ပြုရန်</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'BenefitForm',
  props: {
    visible: Boolean,
    status: String,
    formData: Object
  },
  data() {
    return {
      textMap: {
        create: 'အသစ်ထည့်ရန်',
        update: 'ပြင်ဆင်ရန်'
      },
      rules: {
        amount: [
          { required: true, message: 'ကျေးဇူးပြု၍ ငွေပမာဏထည့်ပါ', trigger: 'blur' }
        ],
        effectiveDate: [
          { required: true, message: 'ကျေးဇူးပြု၍ ရက်စွဲရွေးချယ်ပါ', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.formData)
        }
      })
    }
  }
}
</script>
