<template>
  <div class="social-security-container">
    <!-- 过滤条件 -->
    <div class="filter-container">
      <el-input
        v-model="localQuery.employeeName"
        placeholder="ဝန်ထမ်းအမည်"
        class="filter-item"
        @keyup.enter="$emit('filter')"
      />
      <el-select
        v-model="localQuery.workActive"
        placeholder="အလုပ်လုပ်နေမှုအခြေအနေ"
        class="filter-item"
      >
        <el-option
          v-for="item in workActiveOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="$emit('filter')"
      >
        ရှာဖွေရန်
      </el-button>
    </div>

    <!-- 表格 -->
    <benefit-table
      :data="list"
      :loading="loading"
      @create="handleCreate"
      @update="handleUpdate"
      @delete="handleDelete"
    />

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="localQuery.page"
      :limit.sync="localQuery.limit"
      @pagination="$emit('refresh')"
    />

    <!-- 表单对话框 -->
    <benefit-form
      :visible="dialogVisible"
      :status="dialogStatus"
      :form-data="tempForm"
      @submit="handleSubmit"
      @close="dialogVisible = false"
    />
  </div>
</template>

<script>
import BenefitTable from './BenefitTable.vue'
import BenefitForm from './BenefitForm.vue'
import Pagination from '@/components/Pagination'

export default {
  name: 'SocialSecurity',
  components: { BenefitTable, BenefitForm, Pagination },
  props: {
    listQuery: Object,
    list: Array,
    total: Number,
    loading: Boolean
  },
  data() {
    return {
      localQuery: { ...this.listQuery },
      workActiveOptions: [
        { value: true, label: 'အလုပ်လုပ်နေသည်' },
        { value: false, label: 'အလုပ်ထွက်သွားသည်' }
      ],
      dialogVisible: false,
      dialogStatus: 'create',
      tempForm: {}
    }
  },
  watch: {
    listQuery(newVal) {
      this.localQuery = { ...newVal }
    }
  },
  methods: {
    handleCreate(row) {
      this.dialogStatus = 'create'
      this.tempForm = { ...row }
      this.dialogVisible = true
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.tempForm = { ...row }
      this.dialogVisible = true
    },
    handleDelete(row) {
      // 删除逻辑
    },
    handleSubmit(formData) {
      this.$emit('submit', {
        type: 'socialSecurity',
        data: formData,
        status: this.dialogStatus
      })
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.social-security-container {
  padding: 20px;

  .filter-container {
    margin-bottom: 20px;

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }
  }
}
</style>
