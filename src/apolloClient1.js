export const apolloClient1 = new ApolloClient({
  link: new HttpLink({
    uri: 'your-graphql-endpoint',
    headers: {
      'Content-Type': 'application/json'
    }
  }),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          employeeBenefits: {
            merge: true
          }
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache'
    },
    query: {
      fetchPolicy: 'no-cache'
    }
  }
})
