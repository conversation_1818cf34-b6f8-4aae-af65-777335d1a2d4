import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN' // 中文
import elementEnLocale from 'element-ui/lib/locale/lang/en' // 英文

import myLocale from './my'
import zhLocale from './zh'

Vue.use(VueI18n)

const messages = {
  my: {
    ...myLocale,
    ...elementEnLocale // 缅甸语使用英文的 Element UI 语言包
  },
  zh: {
    ...zhLocale,
    ...elementZhLocale
  }
}

export function getLanguage() {
  const chooseLanguage = Cookies.get('language')
  if (chooseLanguage) return chooseLanguage

  // 检查浏览器语言
  const language = (navigator.language || navigator.browserLanguage).toLowerCase()
  const locales = Object.keys(messages)
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      return locale
    }
  }
  return 'my' // 默认使用缅甸语
}

const i18n = new VueI18n({
  locale: getLanguage(),
  messages
})

export default i18n 