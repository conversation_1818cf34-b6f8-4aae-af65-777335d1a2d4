export default {
  // 导航栏
  navbar: {
    langSelect: {
      success: 'ဘာသာစကားပြောင်းလဲမှုအောင်မြင်ပါသည်'
    }
  },
  
  // 车辆状态管理系统 - 缅甸语翻译
  vehicle: {
    // 通用
    common: {
      search: 'ရှာဖွေရန်',
      cancel: 'ပယ်ဖျက်မည်',
      confirm: 'အတည်ပြုမည်',
      edit: 'တည်းဖြတ်မည်',
      delete: 'ဖျက်မည်',
      save: 'သိမ်းမည်',
      add: 'ထည့်မည်',
      detail: 'အသေးစိတ်',
      refresh: 'ပြန်လည်ဖွင့်မည်',
      export: 'ထုတ်မည်',
      loading: 'ရယူနေပါသည်...',
      noData: 'အချက်အလက်မရှိပါ',
      total: 'စုစုပေါင်း',
      page: 'စာမျက်နှာ',
      size: 'အရွယ်အစား'
    },
    
    // 状态相关
    status: {
      title: 'ယာဉ်အခြေအနေစီမံခန့်ခွဲမှု',
      management: 'အခြေအနေစီမံခန့်ခွဲမှု',
      current: 'လက်ရှိအခြေအနေ',
      new: 'အခြေအနေအသစ်',
      remark: 'မှတ်ချက်',
      update: 'အခြေအနေပြောင်းလဲမည်',
      updateSuccess: 'အခြေအနေပြောင်းလဲမှုအောင်မြင်ပါသည်',
      updateError: 'အခြေအနေပြောင်းလဲမှုမအောင်မြင်ပါ',
      selectStatus: 'အခြေအနေရွေးချယ်ရန်',
      selectNewStatus: 'အခြေအနေအသစ်ရွေးချယ်ရန်',
      enterRemark: 'အခြေအနေပြောင်းလဲမှုရှင်းလင်းချက်ရေးပါ',
      quickUpdate: 'လျင်မြန်သောပြောင်းလဲမှု',
      
      // 具体状态
      active: 'ပုံမှန်အသုံးပြုမှု',
      inactive: 'ရပ်ဆိုင်းအသုံးပြုမှု',
      maintenance: 'ပြုပြင်ထိန်းသိမ်းမှု',
      parked: 'ရပ်ထားမှု',
      in_transit: 'သယ်ပို့နေမှု',
      reserved: 'ကြိုတင်မှာယူမှု',
      scrapped: 'ဖျက်သိမ်းမှု',
      sold: 'ရောင်းချပြီးမှု',
      lost: 'ပျောက်ဆုံးမှု',
      damaged: 'ပျက်စီးမှု',
      
      // 状态描述
      descriptions: {
        active: 'ယာဉ်ပုံမှန်အလုပ်လုပ်နေသည်၊ အသုံးပြုနိုင်သည်',
        inactive: 'ယာဉ်ယာယီအသုံးပြုရပ်တန့်သည်',
        maintenance: 'ယာဉ်ပြုပြင်ထိန်းသိမ်းမှုလုပ်နေသည်',
        parked: 'ယာဉ်ကိုရပ်ထားသည်',
        in_transit: 'ယာဉ်သယ်ပို့ခရီးတွင်ရှိသည်',
        reserved: 'ယာဉ်ကိုကြိုတင်မှာယူပြီးသည်',
        scrapped: 'ယာဉ်ဖျက်သိမ်းပြီးသည်',
        sold: 'ယာဉ်ရောင်းချပြီးသည်',
        lost: 'ယာဉ်ပျောက်ဆုံးသည်',
        damaged: 'ယာဉ်ပျက်စီးသည်၊ အသုံးမပြုနိုင်ပါ'
      }
    },
    
    // 列表页面
    list: {
      title: 'ယာဉ်စာရင်း',
      addVehicle: 'ယာဉ်ထည့်မည်',
      vehicleManagement: 'ယာဉ်စီမံခန့်ခွဲမှု',
      statusManagement: 'အခြေအနေစီမံခန့်ခွဲမှု',
      searchPlaceholder: 'ယာဉ်ရှာဖွေရန်',
      searchByStatus: 'အခြေအနေအလိုက်ရှာဖွေရန်',
      searchByType: 'အမျိုးအစားအလိုက်ရှာဖွေရန်',
      licensePlate: 'ယာဉ်နံပါတ်',
      model: 'ယာဉ်မော်ဒယ်',
      brand: 'အမှတ်တံဆိပ်',
      color: 'အရောင်',
      year: 'နှစ်',
      type: 'ယာဉ်အမျိုးအစား',
      image: 'ယာဉ်ပုံ',
      actions: 'လုပ်ဆောင်ချက်များ',
      updateTime: 'ပြောင်းလဲသည့်အချိန်',
      confirmDelete: 'ဤယာဉ်ကိုဖျက်ရန်သေချာပါသလား?',
      deleteSuccess: 'ဖျက်မှုအောင်မြင်ပါသည်',
      warning: 'သတိပေးချက်'
    },
    
    // 统计页面
    statistics: {
      title: 'ယာဉ်စာရင်းအင်း',
      statusStats: 'အခြေအနေစာရင်းအင်း',
      totalVehicles: 'ယာဉ်စုစုပေါင်း',
      statusDistribution: 'အခြေအနေဖြန့်ဝေမှု',
      refreshStats: 'စာရင်းအင်းပြန်လည်ဖွင့်မည်',
      viewDetails: 'အသေးစိတ်ကြည့်မည်',
      vehicles: 'ယာဉ်'
    },
    
    // 表单相关
    form: {
      licensePlateRequired: 'ယာဉ်နံပါတ်လိုအပ်ပါသည်',
      modelRequired: 'ယာဉ်မော်ဒယ်လိုအပ်ပါသည်',
      brandRequired: 'အမှတ်တံဆိပ်လိုအပ်ပါသည်',
      statusRequired: 'အခြေအနေရွေးချယ်ရန်လိုအပ်ပါသည်',
      enterLicensePlate: 'ယာဉ်နံပါတ်ရေးပါ',
      enterModel: 'ယာဉ်မော်ဒယ်ရေးပါ',
      enterBrand: 'အမှတ်တံဆိပ်ရေးပါ',
      enterColor: 'အရောင်ရေးပါ',
      enterYear: 'နှစ်ရေးပါ',
      yearPlaceholder: 'နှစ်ရေးပါ'
    },
    
    // 对话框
    dialog: {
      addVehicle: 'ယာဉ်ထည့်မည်',
      editVehicle: 'ယာဉ်တည်းဖြတ်မည်',
      updateStatus: 'ယာဉ်အခြေအနေပြောင်းလဲမည်',
      close: 'ပိတ်မည်'
    },
    
    // 消息提示
    message: {
      success: 'အောင်မြင်ပါသည်',
      error: 'အမှားရှိပါသည်',
      createSuccess: 'ဖန်တီးမှုအောင်မြင်ပါသည်',
      updateSuccess: 'ပြောင်းလဲမှုအောင်မြင်ပါသည်',
      deleteSuccess: 'ဖျက်မှုအောင်မြင်ပါသည်',
      getListError: 'ယာဉ်စာရင်းရယူမှုမအောင်မြင်ပါ',
      getStatusOptionsError: 'အခြေအနေရွေးချယ်မှုများရယူမှုမအောင်မြင်ပါ',
      getStatsError: 'စာရင်းအင်းရယူမှုမအောင်မြင်ပါ'
    }
  }
} 