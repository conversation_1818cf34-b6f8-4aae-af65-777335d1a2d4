export default {
  // 导航栏
  navbar: {
    langSelect: {
      success: '语言切换成功'
    }
  },
  
  // 车辆状态管理系统 - 中文翻译
  vehicle: {
    // 通用
    common: {
      search: '搜索',
      cancel: '取消',
      confirm: '确定',
      edit: '编辑',
      delete: '删除',
      save: '保存',
      add: '添加',
      detail: '详情',
      refresh: '刷新',
      export: '导出',
      loading: '加载中...',
      noData: '暂无数据',
      total: '总计',
      page: '页',
      size: '条/页'
    },
    
    // 状态相关
    status: {
      title: '车辆状态管理',
      management: '状态管理',
      current: '当前状态',
      new: '新状态',
      remark: '状态备注',
      update: '更新状态',
      updateSuccess: '状态更新成功',
      updateError: '状态更新失败',
      selectStatus: '选择状态',
      selectNewStatus: '请选择新状态',
      enterRemark: '请输入状态变更说明',
      quickUpdate: '快速更新',
      
      // 具体状态
      active: '正常使用',
      inactive: '停用',
      maintenance: '维修中',
      parked: '停放中',
      in_transit: '运输中',
      reserved: '预约中',
      scrapped: '报废',
      sold: '已售出',
      lost: '丢失',
      damaged: '损坏',
      
      // 状态描述
      descriptions: {
        active: '车辆正常运行，可以使用',
        inactive: '车辆暂停使用',
        maintenance: '车辆正在维修保养',
        parked: '车辆已停放',
        in_transit: '车辆正在运输途中',
        reserved: '车辆已被预约',
        scrapped: '车辆已报废',
        sold: '车辆已售出',
        lost: '车辆丢失',
        damaged: '车辆损坏无法使用'
      }
    },
    
    // 列表页面
    list: {
      title: '车辆列表',
      addVehicle: '添加车辆',
      vehicleManagement: '车辆管理',
      statusManagement: '状态管理',
      searchPlaceholder: '搜索车辆',
      searchByStatus: '按状态筛选',
      searchByType: '按类型筛选',
      licensePlate: '车牌号',
      model: '车辆型号',
      brand: '品牌',
      color: '颜色',
      year: '年份',
      type: '车辆类型',
      image: '车辆图片',
      actions: '操作',
      updateTime: '更新时间',
      confirmDelete: '确认删除这辆车吗?',
      deleteSuccess: '删除成功',
      warning: '警告'
    },
    
    // 统计页面
    statistics: {
      title: '车辆统计',
      statusStats: '状态统计',
      totalVehicles: '车辆总数',
      statusDistribution: '状态分布',
      refreshStats: '刷新统计',
      viewDetails: '查看详情',
      vehicles: '辆'
    },
    
    // 表单相关
    form: {
      licensePlateRequired: '车牌号是必填项',
      modelRequired: '车辆型号是必填项',
      brandRequired: '品牌是必填项',
      statusRequired: '请选择新状态',
      enterLicensePlate: '请输入车牌号',
      enterModel: '请输入车辆型号',
      enterBrand: '请输入品牌',
      enterColor: '请输入颜色',
      enterYear: '请输入年份',
      yearPlaceholder: '请输入年份'
    },
    
    // 对话框
    dialog: {
      addVehicle: '添加车辆',
      editVehicle: '编辑车辆',
      updateStatus: '更新车辆状态',
      close: '关闭'
    },
    
    // 消息提示
    message: {
      success: '成功',
      error: '错误',
      createSuccess: '创建成功',
      updateSuccess: '更新成功',
      deleteSuccess: '删除成功',
      getListError: '获取车辆列表失败',
      getStatusOptionsError: '获取状态选项失败',
      getStatsError: '获取统计失败'
    }
  }
} 