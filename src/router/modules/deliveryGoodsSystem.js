import Layout from '@/layout'

const deliveryGoodsSystemRouter = {
  path: '/deliveryGoodsSystem',
  component: Layout,
  redirect: '/deliveryGoodsSystem/goods-list',
  name: 'DeliveryGoodsSystem',
  meta: {
    title: 'ကုန်ပစ္စည်းပေးပို့မှုစနစ်',
    icon: 'documentation'
  },
  children: [
    {
      path: 'goods-list',
      component: () => import('@/views/delivery_goods_system/goods-list/index'),
      name: 'DeliveryGoodsList',
      meta: { title: 'ကုန်ပစ္စည်းပေးပို့မှုစာရင်း' }
    },
    {
      path: 'form-input',
      component: () => import('@/views/delivery_goods_system/form-input/index'),
      name: 'FormInput',
      meta: { title: 'ပေးပို့မှုမှတ်တမ်းထည့်သွင်းရန်' }
    },
    {
      path: 'transfer-list',
      component: () => import('@/views/delivery_goods_system/transfer-list/index'),
      name: 'TransferList',
      meta: { title: 'ပေးပို့မှုမှတ်တမ်းထည့်သွင်းရန်' }
    },
    {
      path: 'college-che-ci',
      component: () => import('@/views/delivery_goods_system/college_che_ci/index'),
      name: 'CollegeCheCi',
      meta: { title: 'ကုန်ပစ္စည်းပေးပို့မှုစနစ်' }
    }
  ]
}

export default deliveryGoodsSystemRouter
