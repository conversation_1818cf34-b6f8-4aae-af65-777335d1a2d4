import Layout from '@/layout'

const employeeRouter = {
  path: '/employee',
  component: Layout,
  redirect: '/employee/employee-benefit',
  name: 'Employee',
  meta: {
    title: '员工管理',
    icon: 'user'
  },
  children: [
    {
      path: 'employee-benefit',
      component: () => import('@/views/employee/employee-benefit/index'),
      name: 'EmployeeBenefit',
      meta: { title: '员工福利' }
    }
    // 你可以在这里添加更多的子路由
  ]
}

export default employeeRouter
