import Layout from '@/layout'

const shwetheWlRouter = {
  path: '/shwethe-wl',
  component: Layout,
  redirect: '/shwethe-wl/wl-data-userd',
  name: 'ShwetheWl',
  meta: {
    title: '信息管理',
    icon: 'documentation'
  },
  children: [
    {
      path: 'wl-data-userd',
      component: () => import('@/views/shwethe-wl/wl-data-userd/index'),
      name: 'WlDataUserd',
      meta: { title: '用户数据' }
    }
  ]
}

export default shwetheWlRouter
