import Layout from '@/layout'

const shwetheNRouter = {
  path: '/shwethe-n',
  component: Layout,
  redirect: '/shwethe-n/customer',
  name: 'Shwethe<PERSON>',
  meta: {
    title: '信息管理',
    icon: 'documentation'
  },
  children: [
    {
      path: 'customer',
      component: () => import('@/views/shwethe-n/customer/index'),
      name: 'Customer',
      meta: { title: '客户管理' }
    },
    {
      path: 'add_nl',
      component: () => import('@/views/shwethe-n/add_nl/index'),
      name: 'AddNl',
      meta: { title: '添加内容' }
    }
  ]
}
export default shwetheNRouter
