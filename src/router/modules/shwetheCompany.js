import Layout from '@/layout'

const shwetheWlRouter = {
  path: '/shwethe-company',
  component: Layout,
  redirect: '/shwethe-company/wl-data-userd',
  name: 'ShwetheWl',
  meta: {
    title: 'shwethe',
    icon: 'documentation'
  },
  children: [
    {
      path: 'wl-data-userd',
      component: () => import('@/views/shwethe_company/createDuty/index'),
      name: 'WlDataUserd',
      meta: { title: 'shwethe' }
    },
    {
      path: 'duty-info',
      component: () => import('@/views/shwethe_company/dutyInfo/index'),
      name: 'DutyInfo',
      meta: { title: 'dutyInfo' }
    },
    {
      path: 'duty-daily',
      component: () => import('@/views/shwethe_company/dutyDaily/index'),
      name: 'DutyDaily',
      meta: { title: 'dutyDaily' }
    }
  ]
}

export default shwetheWlRouter
