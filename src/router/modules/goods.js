import Layout from '@/layout'

const goodsRouter = {
  path: '/goods',
  component: Layout,
  redirect: '/goods/goodsList',
  name: 'Goods',
  meta: { title: '商品管理', icon: 'el-icon-goods' },
  children: [
    {
      path: 'goodsList',
      component: () => import('@/views/goods/goodsList/index'),
      name: 'GoodsList',
      meta: { title: '商品列表' }
    },
    {
      path: 'goodsList/category',
      component: () => import('@/views/goods/categories/index'),
      name: 'GoodsCategory',
      meta: { title: '商品分类' }
    }
  ]
}

export default goodsRouter
