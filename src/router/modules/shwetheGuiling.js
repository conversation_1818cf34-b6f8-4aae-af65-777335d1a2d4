import Layout from '@/layout'

const shwetheGuilingRouter = {
  path: '/shwethe-guiling',
  component: Layout,
  redirect: '/shwethe-guiling/guiling',
  name: 'ShwetheGuiling',
  meta: {
    title: 'shwethe_guiling',
    icon: 'documentation'
  },
  children: [
    {
      path: 'guiling',
      component: () => import('@/views/shwethe-guiling/guiling/index.vue'),
      name: 'Guil<PERSON>',
      meta: { title: 'guiling' }
    },
    {
      path: 'guiling-update',
      component: () => import('@/views/shwethe-guiling/guiling-update/index.vue'),
      name: 'GuilingUpdate',
      meta: { title: 'guiling update' }
    }
  ]
}

export default shwetheGuilingRouter
