import Layout from '@/layout'

const laborProjectRouter = {
  path: '/labor-project',
  component: Layout,
  redirect: '/labor-project/index',
  name: 'LaborProject',
  meta: {
    title: '劳动管理系统',
    icon: 'peoples'
  },
  children: [
    // {
    //   path: 'index',
    //   component: () => import('@/views/labor-project/index.vue'),
    //   name: 'LaborProjectIndex',
    //   meta: { title: '系统首页' }
    // },
    {
      path: 'employee',
      component: () => import('@/views/labor-project/employee/index.vue'),
      name: 'LaborProjectEmployee',
      meta: { title: '员工管理' }
    },
    {
      path: 'position',
      component: () => import('@/views/labor-project/position/index.vue'),
      name: 'LaborProjectPosition',
      meta: { title: '岗位管理' }
    },
    // {
    //   path: 'employee-debug',
    //   component: () => import('@/views/labor-project/employee/debug.vue'),
    //   name: 'LaborProjectEmployeeDebug',
    //   meta: { title: '员工管理(调试)' }
    // },
    // {
    //   path: 'attendance',
    //   component: () => import('@/views/labor-project/attendance/index.vue'),
    //   name: 'LaborProjectAttendance',
    //   meta: { title: '考勤管理' }
    // },
    // {
    //   path: 'leave',
    //   component: () => import('@/views/labor-project/leave/index.vue'),
    //   name: 'LaborProjectLeave',
    //   meta: { title: '请假管理' }
    // },
    // {
    //   path: 'salary',
    //   component: () => import('@/views/labor-project/salary/index.vue'),
    //   name: 'LaborProjectSalary',
    //   meta: { title: '薪资管理' }
    // },
    {
      path: 'salary-item-types',
      component: () => import('@/views/labor-project/salary/salary-item-types.vue'),
      name: 'LaborProjectSalaryItemTypes',
      meta: { title: '薪资项目类型管理' }
    },
    {
      path: 'employee-salary-templates',
      component: () => import('@/views/labor-project/salary/employee-salary-templates.vue'),
      name: 'LaborProjectEmployeeSalaryTemplates',
      meta: { title: '员工薪资模板管理' }
    },
    // {
    //   path: 'overtime',
    //   component: () => import('@/views/labor-project/overtime/index.vue'),
    //   name: 'LaborProjectOvertime',
    //   meta: { title: '加班管理' }
    // },
    {
      path: 'department',
      component: () => import('@/views/labor-project/department/index.vue'),
      name: 'LaborProjectDepartment',
      meta: { title: '部门管理' }
    }
    // {
    //   path: 'reports',
    //   component: () => import('@/views/labor-project/reports/index.vue'),
    //   name: 'LaborProjectReports',
    //   meta: { title: '报表统计' }
    // },
    // {
    //   path: 'settings',
    //   component: () => import('@/views/labor-project/settings/index.vue'),
    //   name: 'LaborProjectSettings',
    //   meta: { title: '系统设置' }
    // }
  ]
}

export default laborProjectRouter
