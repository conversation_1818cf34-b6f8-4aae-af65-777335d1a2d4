import Layout from '@/layout'

const vehicleManagementRouter = {
  path: '/vehicle-management',
  component: Layout,
  redirect: '/vehicle-management/index',
  name: 'VehicleManagement',
  meta: {
    title: '车辆管理',
    icon: 'el-icon-truck'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/vehicle-management/index'),
      name: 'VehicleList',
      meta: { title: '车辆列表', icon: 'el-icon-s-grid' }
    },
    {
      path: 'detail/:che_liang_id',
      component: () => import('@/views/vehicle-management/detail'),
      name: 'VehicleDetail',
      meta: { title: '车辆详情', noCache: true },
      hidden: true
    },
    {
      path: 'statistics',
      component: () => import('@/views/vehicle-management/statistics'),
      name: 'VehicleStatistics',
      meta: { title: '车辆统计', icon: 'el-icon-data-analysis' }
    },
    {
      path: 'maintenance',
      component: () => import('@/views/vehicle-management/maintenance'),
      name: 'VehicleMaintenance',
      meta: { title: '维护管理', icon: 'el-icon-setting' }
    },
    {
      path: 'status',
      component: () => import('@/views/vehicle-management/status'),
      name: 'VehicleStatus',
      meta: { title: '状态管理', icon: 'el-icon-pie-chart' }
    },
    {
      path: 'i18n-demo',
      component: () => import('@/views/vehicle-management/i18n-demo'),
      name: 'VehicleI18nDemo',
      meta: { title: '国际化演示', icon: 'el-icon-chat-line-round' }
    }
  ]
}

export default vehicleManagementRouter 