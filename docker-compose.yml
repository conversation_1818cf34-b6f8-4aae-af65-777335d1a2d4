version: '3.8'

services:
  web:
    image: shwethe_manager_web
    build: .
    container_name: shwethe_web
    ports:
      - "9527:80"
    environment:
      - NODE_ENV=production
    networks:
      - shwethe_net

  nginx:
    image: nginx:alpine
    container_name: shwethe_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - web
    networks:
      - shwethe_net

networks:
  shwethe_net:
    driver: bridge 